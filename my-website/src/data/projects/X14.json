{"id": 14, "project_name": "Deploying and Integrating Kubeflow on AWS EKS", "description": "The goal of this project was to deploy Kubeflow on AWS EKS, manage machine learning pipelines, and integrate MLflow for enhanced model tracking and experimentation.", "year": 2024, "month": 7, "role": "", "project_type": "individual", "technologies_used": [{"name": "Kubeflow", "description": "A platform for deploying, monitoring, and managing machine learning models on Kubernetes."}, {"name": "AWS VPC & Subnets", "description": "Proficiency in configuring VPCs, subnets (public/private), and routing for secure network communications."}, {"name": "AWS EKS", "description": "A managed Kubernetes service on AWS Web Services (AWS) for deploying containerized applications."}, {"name": "MLflow", "description": "An open-source platform for managing the complete machine learning lifecycle, including experiment tracking and model management."}], "infrastructure": [{"name": "AWS EKS Cluster", "description": "The Kubernetes cluster was deployed on AWS EKS with two worker nodes and required configurations for service management.", "steps": ["Created Kubernetes cluster using eksctl", "Set up IAM roles and permissions", "Configured AWS EKS access through kubectl"]}, {"name": "<PERSON><PERSON>", "description": "Deployed Juju for managing Kubernetes applications, particularly for deploying Kubeflow and MLflow.", "steps": ["Installed Juju and bootstrapped it to the AWS EKS cluster", "Registered the cluster and created necessary namespaces"]}], "skills_required": [{"name": "AWS EKS Management", "description": "Expertise in setting up and managing Kubernetes clusters on AWS using AWS EKS."}, {"name": "Kubeflow and MLflow Integration", "description": "Experience integrating MLflow with Kubeflow for machine learning lifecycle management and experiment tracking."}, {"name": "Kubernetes Management", "description": "Proficiency in managing Kubernetes clusters and containerized applications."}, {"name": "Juju Framework", "description": "Experience with the Juju framework for managing Kubernetes applications, including deployments and scaling."}], "challenges_faced": [{"name": "Cluster Access and Permissions", "description": "Ensuring proper IAM roles and permissions were configured for managing the AWS EKS cluster and services."}], "outcomes": [{"name": "Successful Deployment of Kubeflow and MLflow", "description": "Kubeflow and MLflow were successfully deployed and integrated into the AWS EKS environment, allowing for enhanced model tracking and experimentation."}, {"name": "Streamlined Machine Learning Workflow", "description": "MLflow was integrated with Kubeflow notebooks for efficient machine learning experiment tracking and management."}], "links": []}