<!-- @format -->

# Portfolio Website

A responsive portfolio website built with React, TypeScript, and Tailwind CSS.

## Features

- Responsive design that works on all devices
- Clean and modern UI with a simplified color scheme
- Interactive project cards with details modal
- Skills section with categorized skills
- Experience and education timeline
- Certificate showcase
- Blog section
- Contact information with social links

## Technologies Used

- React
- TypeScript
- Tailwind CSS
- Framer Motion for animations

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository

   ```bash
   git clone https://github.com/yourusername/portfolio.git
   cd portfolio
   ```

2. Install dependencies

   ```bash
   npm install
   # or
   yarn install
   ```

3. Start the development server

   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

## Customization

The portfolio data is stored in `src/data/portfolio.ts`. You can customize the following:

- Personal information
- Skills
- Experience
- Education
- Projects
- Blog posts
- Contact information

## Deployment

Build the project for production:

```bash
npm run build
# or
yarn build
```

The built files will be in the `dist` directory, which you can deploy to any static hosting service.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
