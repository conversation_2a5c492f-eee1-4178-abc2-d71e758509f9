<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | <PERSON> Said</title>
    <style>
        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #000000;
            color: #ffffff;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            padding: 0;
            text-align: center;
        }

        .container {
            max-width: 600px;
            padding: 2rem;
        }

        h1 {
            font-size: 6rem;
            margin: 0;
            color: #66fcf1;
        }

        h2 {
            font-size: 2rem;
            margin: 1rem 0;
        }

        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #a0a0a0;
        }

        .btn {
            display: inline-block;
            background-color: #66fcf1;
            color: #000000;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background-color: #4fd1c5;
            transform: translateY(-2px);
        }

        .logo {
            margin-bottom: 2rem;
        }

        .logo img {
            height: 60px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="logo">
            <img src="/photos/img/512x512_favicon.png" alt="Said Mustafa Said Logo">
        </div>
        <h1>404</h1>
        <h2>Page Not Found</h2>
        <p>The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
        </p>
        <a href="/" class="btn">Return to Homepage</a>
    </div>
</body>

</html>