{{- if .Values.controller.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.controller.serviceAccount.name }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "aws-ebs-csi-driver.labels" . | nindent 4 }}
  {{- with .Values.controller.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- if eq .Release.Name "kustomize" }}
  #Enable if EKS IAM roles for service accounts (IRSA) is used. See https://docs.aws.amazon.com/eks/latest/userguide/iam-roles-for-service-accounts.html for details.
  #annotations:
  #  eks.amazonaws.com/role-arn: arn:<partition>:iam::<account>:role/ebs-csi-role
  {{- end }}
{{- if .Values.controller.serviceAccount.automountServiceAccountToken }}
automountServiceAccountToken: {{ .Values.controller.serviceAccount.automountServiceAccountToken }}
{{- end }}
{{- end -}}
