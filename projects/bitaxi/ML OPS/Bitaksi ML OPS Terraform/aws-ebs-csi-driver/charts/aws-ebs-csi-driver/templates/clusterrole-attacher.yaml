---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ebs-external-attacher-role
  labels:
    {{- include "aws-ebs-csi-driver.labels" . | nindent 4 }}
rules:
  - apiGroups: [ "" ]
    resources: [ "persistentvolumes" ]
    verbs: [ "get", "list", "watch", "update", "patch" ]
  - apiGroups: [ "" ]
    resources: [ "nodes" ]
    verbs: [ "get", "list", "watch" ]
  - apiGroups: [ "csi.storage.k8s.io" ]
    resources: [ "csinodeinfos" ]
    verbs: [ "get", "list", "watch" ]
  - apiGroups: [ "storage.k8s.io" ]
    resources: [ "volumeattachments" ]
    verbs: [ "get", "list", "watch", "update", "patch" ]
  - apiGroups: [ "storage.k8s.io" ]
    resources: [ "volumeattachments/status" ]
    verbs: [ "patch" ]
  {{- with .Values.sidecars.attacher.additionalClusterRoleRules }}
    {{- . | toYaml | nindent 2 }}
  {{- end }}
