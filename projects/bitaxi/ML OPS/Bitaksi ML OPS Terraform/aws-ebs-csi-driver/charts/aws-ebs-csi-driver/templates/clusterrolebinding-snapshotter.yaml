---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ebs-csi-snapshotter-binding
  labels:
    {{- include "aws-ebs-csi-driver.labels" . | nindent 4 }}
subjects:
  - kind: ServiceAccount
    name: {{ .Values.controller.serviceAccount.name }}
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: ebs-external-snapshotter-role
  apiGroup: rbac.authorization.k8s.io
