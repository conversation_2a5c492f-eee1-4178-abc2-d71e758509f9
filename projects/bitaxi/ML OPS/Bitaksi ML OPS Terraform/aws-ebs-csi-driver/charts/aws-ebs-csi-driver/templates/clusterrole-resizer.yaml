---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ebs-external-resizer-role
  labels:
    {{- include "aws-ebs-csi-driver.labels" . | nindent 4 }}
rules:
  # The following rule should be uncommented for plugins that require secrets
  # for provisioning.
  # - apiGroups: [""]
  #   resources: ["secrets"]
  #   verbs: ["get", "list", "watch"]
  - apiGroups: [ "" ]
    resources: [ "persistentvolumes" ]
    verbs: [ "get", "list", "watch", "update", "patch" ]
  - apiGroups: [ "" ]
    resources: [ "persistentvolumeclaims" ]
    verbs: [ "get", "list", "watch" ]
  - apiGroups: [ "" ]
    resources: [ "persistentvolumeclaims/status" ]
    verbs: [ "update", "patch" ]
  - apiGroups: [ "storage.k8s.io" ]
    resources: [ "storageclasses" ]
    verbs: [ "get", "list", "watch" ]
  - apiGroups: [ "" ]
    resources: [ "events" ]
    verbs: [ "list", "watch", "create", "update", "patch" ]
  - apiGroups: [ "" ]
    resources: [ "pods" ]
    verbs: [ "get", "list", "watch" ]
  - apiGroups: [ "storage.k8s.io" ]
    resources: [ "volumeattributesclasses" ]
    verbs: [ "get", "list", "watch" ]
  {{- with .Values.sidecars.resizer.additionalClusterRoleRules }}
    {{- . | toYaml | nindent 2 }}
  {{- end }}
