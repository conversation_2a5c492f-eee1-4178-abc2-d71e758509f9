---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ebs-external-snapshotter-role
  labels:
    {{- include "aws-ebs-csi-driver.labels" . | nindent 4 }}
rules:
  - apiGroups: [ "" ]
    resources: [ "events" ]
    verbs: [ "list", "watch", "create", "update", "patch" ]
  # Secret permission is optional.
  # Enable it if your driver needs secret.
  # For example, `csi.storage.k8s.io/snapshotter-secret-name` is set in VolumeSnapshotClass.
  # See https://kubernetes-csi.github.io/docs/secrets-and-credentials.html for more details.
  # - apiGroups: [ "" ]
  #   resources: [ "secrets" ]
  #   verbs: [ "get", "list" ]
  - apiGroups: [ "snapshot.storage.k8s.io" ]
    resources: [ "volumesnapshotclasses" ]
    verbs: [ "get", "list", "watch" ]
  - apiGroups: [ "snapshot.storage.k8s.io" ]
    resources: [ "volumesnapshotcontents" ]
    verbs: [ "create", "get", "list", "watch", "update", "delete", "patch" ]
  - apiGroups: [ "snapshot.storage.k8s.io" ]
    resources: [ "volumesnapshotcontents/status" ]
    verbs: [ "update", "patch" ]
  {{- with .Values.sidecars.snapshotter.additionalClusterRoleRules }}
    {{- . | toYaml | nindent 2 }}
  {{- end }}
