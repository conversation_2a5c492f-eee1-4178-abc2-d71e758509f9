# Controller Service
kind: Deployment
apiVersion: apps/v1
metadata:
  name: ebs-csi-controller
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "aws-ebs-csi-driver.labels" . | nindent 4 }}
  {{- with .Values.controller.deploymentAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.controller.replicaCount }}
  {{- if or (kindIs "float64" .Values.controller.revisionHistoryLimit) (kindIs "int64" .Values.controller.revisionHistoryLimit) }}
  revisionHistoryLimit: {{ .Values.controller.revisionHistoryLimit }}
  {{- end }}
  {{- with .Values.controller.updateStrategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  selector:
    matchLabels:
      app: ebs-csi-controller
      {{- include "aws-ebs-csi-driver.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        app: ebs-csi-controller
        {{- include "aws-ebs-csi-driver.labels" . | nindent 8 }}
        {{- if .Values.controller.podLabels }}
        {{- toYaml .Values.controller.podLabels | nindent 8 }}
        {{- end }}
      {{- if .Values.controller.podAnnotations }}
      annotations:
        {{- tpl ( .Values.controller.podAnnotations | toYaml ) . | nindent 8 }}
      {{- end }}
    spec:
      nodeSelector:
        kubernetes.io/os: linux
        {{- with .Values.controller.nodeSelector }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      serviceAccountName: {{ .Values.controller.serviceAccount.name }}
      priorityClassName: {{ .Values.controller.priorityClassName }}
      {{- with default .Values.controller.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      tolerations:
        {{- with .Values.controller.tolerations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- if .Values.controller.topologySpreadConstraints }}
      {{- $tscLabelSelector := dict "labelSelector" ( dict "matchLabels" ( dict "app" "ebs-csi-controller" ) ) }}
      {{- $constraints := list }}
      {{- range .Values.controller.topologySpreadConstraints }}
        {{- $constraints = mustAppend $constraints (mergeOverwrite . $tscLabelSelector) }}
      {{- end }}
      topologySpreadConstraints:
        {{- $constraints | toYaml | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.initContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: ebs-plugin
          image: {{ printf "%s%s:%s" (default "" .Values.image.containerRegistry) .Values.image.repository (default (printf "v%s" .Chart.AppVersion) (.Values.image.tag | toString)) }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          args:
            {{- if ne .Release.Name "kustomize" }}
            - controller
            {{- else }}
            # - {all,controller,node} # specify the driver mode
            {{- end }}
            - --endpoint=$(CSI_ENDPOINT)
            {{- if .Values.controller.extraVolumeTags }}
              {{- include "aws-ebs-csi-driver.extra-volume-tags" . | nindent 12 }}
            {{- end }}
            {{- with (tpl (default "" .Values.controller.k8sTagClusterId) . )  }}
            - --k8s-tag-cluster-id={{ . }}
            {{- end }}
            {{- if and (.Values.controller.enableMetrics) (not .Values.controller.httpEndpoint) }}
            - --http-endpoint=0.0.0.0:3301
            {{- end}}
            {{- with .Values.controller.httpEndpoint }}
            - --http-endpoint={{ . }}
            {{- end }}
            {{- if .Values.controller.sdkDebugLog }}
            - --aws-sdk-debug-log=true
            {{- end}}
            {{- if .Values.controller.batching }}
            - --batching=true
            {{- end}}
            {{- with .Values.controller.loggingFormat }}
            - --logging-format={{ . }}
            {{- end }}
            {{- with .Values.controller.userAgentExtra }}
            - --user-agent-extra={{ . }}
            {{- end }}
            {{- if .Values.controller.otelTracing }}
            - --enable-otel-tracing=true
            {{- end}}
            - --v={{ .Values.controller.logLevel }}
            {{- range .Values.controller.additionalArgs }}
            - {{ . }}
            {{- end }}
          env:
            - name: CSI_ENDPOINT
              value: unix:///var/lib/csi/sockets/pluginproxy/csi.sock
            - name: CSI_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            {{- with .Values.awsAccessSecret }}
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: {{ .name }}
                  key: {{ .keyId }}
                  optional: true
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ .name }}
                  key: {{ .accessKey }}
                  optional: true
            {{- end }}
            - name: AWS_EC2_ENDPOINT
              valueFrom:
                configMapKeyRef:
                  name: aws-meta
                  key: endpoint
                  optional: true
            {{- with .Values.controller.region }}
            - name: AWS_REGION
              value: {{ . }}
            {{- end }}
            {{- if .Values.proxy.http_proxy }}
            {{- include "aws-ebs-csi-driver.http-proxy" . | nindent 12 }}
            {{- end }}
            {{- with .Values.controller.env }}
            {{- . | toYaml | nindent 12 }}
            {{- end }}
            {{- with .Values.controller.otelTracing }}
            - name: OTEL_SERVICE_NAME
              value: {{ .otelServiceName }}
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: {{ .otelExporterEndpoint }}
            {{- end }}
          {{- with .Values.controller.envFrom }}
          envFrom:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: socket-dir
              mountPath: /var/lib/csi/sockets/pluginproxy/
          {{- with .Values.controller.volumeMounts }}
          {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: healthz
              containerPort: 9808
              protocol: TCP
            {{- if .Values.controller.enableMetrics }}
            - name: metrics
              containerPort: 3301
              protocol: TCP
            {{- end}}
          livenessProbe:
            httpGet:
              path: /healthz
              port: healthz
            initialDelaySeconds: 10
            timeoutSeconds: 3
            periodSeconds: 10
            failureThreshold: 5
          readinessProbe:
            httpGet:
              path: /healthz
              port: healthz
            initialDelaySeconds: 10
            timeoutSeconds: 3
            periodSeconds: 10
            failureThreshold: 5
          {{- with .Values.controller.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.controller.containerSecurityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        - name: csi-provisioner
          image: {{ printf "%s%s:%s" (default "" .Values.image.containerRegistry) .Values.sidecars.provisioner.image.repository .Values.sidecars.provisioner.image.tag }}
          imagePullPolicy: {{ default .Values.image.pullPolicy .Values.sidecars.provisioner.image.pullPolicy }}
          args:
            {{- if not (regexMatch "(-timeout)" (join " " .Values.sidecars.provisioner.additionalArgs)) }}
            - --timeout=60s
            {{- end }}
            - --csi-address=$(ADDRESS)
            - --v={{ .Values.sidecars.provisioner.logLevel }}
            - --feature-gates=Topology=true
            {{- if .Values.controller.extraCreateMetadata }}
            - --extra-create-metadata
            {{- end}}
            - --leader-election={{ .Values.sidecars.provisioner.leaderElection.enabled | required "leader election state for csi-provisioner is required, must be set to true || false." }}
            {{- if .Values.sidecars.provisioner.leaderElection.enabled }}
            {{- if .Values.sidecars.provisioner.leaderElection.leaseDuration }}
            - --leader-election-lease-duration={{ .Values.sidecars.provisioner.leaderElection.leaseDuration }}
            {{- end }}
            {{- if .Values.sidecars.provisioner.leaderElection.renewDeadline}}
            - --leader-election-renew-deadline={{ .Values.sidecars.provisioner.leaderElection.renewDeadline }}
            {{- end }}
            {{- if .Values.sidecars.provisioner.leaderElection.retryPeriod }}
            - --leader-election-retry-period={{ .Values.sidecars.provisioner.leaderElection.retryPeriod }}
            {{- end }}
            {{- end }}
            - --default-fstype={{ .Values.controller.defaultFsType }}
            {{- if not (regexMatch "(-kube-api-qps)|(-kube-api-burst)|(-worker-threads)" (join " " .Values.sidecars.provisioner.additionalArgs)) }}
            - --kube-api-qps=20
            - --kube-api-burst=100
            - --worker-threads=100
            {{- end }}
            {{- if not (regexMatch "(-retry-interval-max)" (join " " .Values.sidecars.provisioner.additionalArgs)) }}
            - --retry-interval-max=30m
            {{- end }}
            {{- range .Values.sidecars.provisioner.additionalArgs }}
            - {{ . }}
            {{- end }}
          env:
            - name: ADDRESS
              value: /var/lib/csi/sockets/pluginproxy/csi.sock
            {{- if .Values.proxy.http_proxy }}
            {{- include "aws-ebs-csi-driver.http-proxy" . | nindent 12 }}
            {{- end }}
            {{- with .Values.sidecars.provisioner.env }}
            {{- . | toYaml | nindent 12 }}
            {{- end }}
          {{- with .Values.controller.envFrom }}
          envFrom:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: socket-dir
              mountPath: /var/lib/csi/sockets/pluginproxy/
          {{- with default .Values.controller.resources .Values.sidecars.provisioner.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.sidecars.provisioner.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        - name: csi-attacher
          image: {{ printf "%s%s:%s" (default "" .Values.image.containerRegistry) .Values.sidecars.attacher.image.repository .Values.sidecars.attacher.image.tag }}
          imagePullPolicy: {{ default .Values.image.pullPolicy .Values.sidecars.attacher.image.pullPolicy }}
          args:
            {{- if not (regexMatch "(-timeout)" (join " " .Values.sidecars.attacher.additionalArgs)) }}
            - --timeout=60s
            {{- end }}
            - --csi-address=$(ADDRESS)
            - --v={{ .Values.sidecars.attacher.logLevel }}
            - --leader-election={{ .Values.sidecars.attacher.leaderElection.enabled | required "leader election state for csi-attacher is required, must be set to true || false." }}
            {{- if .Values.sidecars.attacher.leaderElection.enabled }}
            {{- if .Values.sidecars.attacher.leaderElection.leaseDuration }}
            - --leader-election-lease-duration={{ .Values.sidecars.attacher.leaderElection.leaseDuration }}
            {{- end }}
            {{- if .Values.sidecars.attacher.leaderElection.renewDeadline}}
            - --leader-election-renew-deadline={{ .Values.sidecars.attacher.leaderElection.renewDeadline }}
            {{- end }}
            {{- if .Values.sidecars.attacher.leaderElection.retryPeriod }}
            - --leader-election-retry-period={{ .Values.sidecars.attacher.leaderElection.retryPeriod }}
            {{- end }}
            {{- end }}
            {{- if not (regexMatch "(-kube-api-qps)|(-kube-api-burst)|(-worker-threads)" (join " " .Values.sidecars.attacher.additionalArgs)) }}
            - --kube-api-qps=20
            - --kube-api-burst=100
            - --worker-threads=100
            {{- end }}
            {{- if not (regexMatch "(-retry-interval-max)" (join " " .Values.sidecars.attacher.additionalArgs)) }}
            - --retry-interval-max=5m
            {{- end }}
            {{- range .Values.sidecars.attacher.additionalArgs }}
            - {{ . }}
            {{- end }}
          env:
            - name: ADDRESS
              value: /var/lib/csi/sockets/pluginproxy/csi.sock
            {{- if .Values.proxy.http_proxy }}
            {{- include "aws-ebs-csi-driver.http-proxy" . | nindent 12 }}
            {{- end }}
            {{- with .Values.sidecars.attacher.env }}
            {{- . | toYaml | nindent 12 }}
            {{- end }}
          {{- with .Values.controller.envFrom }}
          envFrom:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: socket-dir
              mountPath: /var/lib/csi/sockets/pluginproxy/
          {{- with default .Values.controller.resources .Values.sidecars.attacher.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.sidecars.attacher.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        {{- if or .Values.sidecars.snapshotter.forceEnable (.Capabilities.APIVersions.Has "snapshot.storage.k8s.io/v1beta1") (.Capabilities.APIVersions.Has "snapshot.storage.k8s.io/v1") }}
        - name: csi-snapshotter
          image: {{ printf "%s%s:%s" (default "" .Values.image.containerRegistry) .Values.sidecars.snapshotter.image.repository .Values.sidecars.snapshotter.image.tag }}
          imagePullPolicy: {{ default .Values.image.pullPolicy .Values.sidecars.snapshotter.image.pullPolicy }}
          args:
            - --csi-address=$(ADDRESS)
            - --leader-election=true
            - --v={{ .Values.sidecars.snapshotter.logLevel }}
            {{- if .Values.controller.extraCreateMetadata }}
            - --extra-create-metadata
            {{- end}}
            {{- if not (regexMatch "(-kube-api-qps)|(-kube-api-burst)|(-worker-threads)" (join " " .Values.sidecars.snapshotter.additionalArgs)) }}
            - --kube-api-qps=20
            - --kube-api-burst=100
            - --worker-threads=100
            {{- end }}
            {{- if not (regexMatch "(-retry-interval-max)" (join " " .Values.sidecars.snapshotter.additionalArgs)) }}
            - --retry-interval-max=30m
            {{- end }}
            {{- range .Values.sidecars.snapshotter.additionalArgs }}
            - {{ . }}
            {{- end }}
          env:
            - name: ADDRESS
              value: /var/lib/csi/sockets/pluginproxy/csi.sock
            {{- if .Values.proxy.http_proxy }}
            {{- include "aws-ebs-csi-driver.http-proxy" . | nindent 12 }}
            {{- end }}
            {{- with .Values.sidecars.snapshotter.env }}
            {{- . | toYaml | nindent 12 }}
            {{- end }}
          {{- with .Values.controller.envFrom }}
          envFrom:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: socket-dir
              mountPath: /var/lib/csi/sockets/pluginproxy/
          {{- with default .Values.controller.resources .Values.sidecars.snapshotter.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.sidecars.snapshotter.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        {{- end }}
        {{- if (.Values.controller.volumeModificationFeature).enabled }}
        - name: volumemodifier
          image: {{ printf "%s%s:%s" (default "" .Values.image.containerRegistry) .Values.sidecars.volumemodifier.image.repository .Values.sidecars.volumemodifier.image.tag }}
          imagePullPolicy: {{ default .Values.image.pullPolicy .Values.sidecars.volumemodifier.image.pullPolicy }}
          args:
            {{- if not (regexMatch "(-timeout)" (join " " .Values.sidecars.volumemodifier.additionalArgs)) }}
            - --timeout=60s
            {{- end }}
            - --csi-address=$(ADDRESS)
            - --v={{ .Values.sidecars.volumemodifier.logLevel }}
            - --leader-election={{ .Values.sidecars.volumemodifier.leaderElection.enabled | required "leader election state for csi-volumemodifier is required, must be set to true || false." }}
            {{- if .Values.sidecars.volumemodifier.leaderElection.enabled }}
            {{- if .Values.sidecars.volumemodifier.leaderElection.leaseDuration }}
            - --leader-election-lease-duration={{ .Values.sidecars.volumemodifier.leaderElection.leaseDuration }}
            {{- end }}
            {{- if .Values.sidecars.volumemodifier.leaderElection.renewDeadline}}
            - --leader-election-renew-deadline={{ .Values.sidecars.volumemodifier.leaderElection.renewDeadline }}
            {{- end }}
            {{- if .Values.sidecars.volumemodifier.leaderElection.retryPeriod }}
            - --leader-election-retry-period={{ .Values.sidecars.volumemodifier.leaderElection.retryPeriod }}
            {{- end }}
            {{- end }}
            {{- range .Values.sidecars.volumemodifier.additionalArgs }}
            - {{ . }}
            {{- end }}
          env:
            - name: ADDRESS
              value: /var/lib/csi/sockets/pluginproxy/csi.sock
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            {{- if .Values.proxy.http_proxy }}
            {{- include "aws-ebs-csi-driver.http-proxy" . | nindent 12 }}
            {{- end }}
            {{- with .Values.sidecars.volumemodifier.env }}
            {{- . | toYaml | nindent 12 }}
            {{- end }}
          {{- with .Values.controller.envFrom }}
          envFrom:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: socket-dir
              mountPath: /var/lib/csi/sockets/pluginproxy/
          {{- with default .Values.controller.resources .Values.sidecars.volumemodifier.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.sidecars.volumemodifier.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        {{- end }}
        - name: csi-resizer
          image: {{ printf "%s%s:%s" (default "" .Values.image.containerRegistry) .Values.sidecars.resizer.image.repository .Values.sidecars.resizer.image.tag }}
          imagePullPolicy: {{ default .Values.image.pullPolicy .Values.sidecars.resizer.image.pullPolicy }}
          args:
            {{- if not (regexMatch "(-timeout)" (join " " .Values.sidecars.resizer.additionalArgs)) }}
            - --timeout=60s
            {{- end }}
            - --csi-address=$(ADDRESS)
            - --v={{ .Values.sidecars.resizer.logLevel }}
            - --handle-volume-inuse-error=false
            {{- with .Values.sidecars.resizer.leaderElection }}
            - --leader-election={{ .enabled | default true }}
            {{- if .leaseDuration }}
            - --leader-election-lease-duration={{ .leaseDuration }}
            {{- end }}
            {{- if .renewDeadline }}
            - --leader-election-renew-deadline={{ .renewDeadline }}
            {{- end }}
            {{- if .retryPeriod }}
            - --leader-election-retry-period={{ .retryPeriod }}
            {{- end }}
            {{- end }}
            {{- if not (regexMatch "(-kube-api-qps)|(-kube-api-burst)|(-workers)" (join " " .Values.sidecars.resizer.additionalArgs)) }}
            - --kube-api-qps=20
            - --kube-api-burst=100
            - --workers=100
            {{- end }}
            {{- if not (regexMatch "(-retry-interval-max)" (join " " .Values.sidecars.resizer.additionalArgs)) }}
            - --retry-interval-max=30m
            {{- end }}
            {{- range .Values.sidecars.resizer.additionalArgs }}
            - {{ . }}
            {{- end }}
          env:
            - name: ADDRESS
              value: /var/lib/csi/sockets/pluginproxy/csi.sock
            {{- if .Values.proxy.http_proxy }}
            {{- include "aws-ebs-csi-driver.http-proxy" . | nindent 12 }}
            {{- end }}
            {{- with .Values.sidecars.resizer.env }}
            {{- . | toYaml | nindent 12 }}
            {{- end }}
          {{- with .Values.controller.envFrom }}
          envFrom:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: socket-dir
              mountPath: /var/lib/csi/sockets/pluginproxy/
          {{- with default .Values.controller.resources .Values.sidecars.resizer.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.sidecars.resizer.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        - name: liveness-probe
          image: {{ printf "%s%s:%s" (default "" .Values.image.containerRegistry) .Values.sidecars.livenessProbe.image.repository .Values.sidecars.livenessProbe.image.tag }}
          imagePullPolicy: {{ default .Values.image.pullPolicy .Values.sidecars.livenessProbe.image.pullPolicy }}
          args:
            - --csi-address=/csi/csi.sock
            {{- range .Values.sidecars.livenessProbe.additionalArgs }}
            - {{ . }}
            {{- end }}
          {{- with .Values.controller.envFrom }}
          envFrom:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: socket-dir
              mountPath: /csi
          {{- with default .Values.controller.resources .Values.sidecars.livenessProbe.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.sidecars.livenessProbe.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
      {{- range .Values.imagePullSecrets }}
        - name: {{ . }}
      {{- end }}
      {{- end }}
      volumes:
        - name: socket-dir
          {{- if .Values.controller.socketDirVolume }}
          {{- toYaml .Values.controller.socketDirVolume | nindent 10 }}
          {{- else }}
          emptyDir: {}
          {{- end }}
        {{- with .Values.controller.volumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- if .Values.controller.dnsConfig }}
      dnsConfig:
        {{- toYaml .Values.controller.dnsConfig | nindent 8 }}
      {{- end }}
