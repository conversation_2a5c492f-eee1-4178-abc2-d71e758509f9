---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ebs-csi-node-getter-binding
  labels:
    {{- include "aws-ebs-csi-driver.labels" . | nindent 4 }}
subjects:
  - kind: ServiceAccount
    name: {{ .Values.node.serviceAccount.name }}
    namespace: {{ .Values.node.namespaceOverride | default .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: ebs-csi-node-role
  apiGroup: rbac.authorization.k8s.io
