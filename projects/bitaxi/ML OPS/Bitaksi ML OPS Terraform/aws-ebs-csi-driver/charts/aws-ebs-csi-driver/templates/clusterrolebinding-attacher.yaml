---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ebs-csi-attacher-binding
  labels:
    {{- include "aws-ebs-csi-driver.labels" . | nindent 4 }}
subjects:
  - kind: ServiceAccount
    name: {{ .Values.controller.serviceAccount.name }}
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: ebs-external-attacher-role
  apiGroup: rbac.authorization.k8s.io
