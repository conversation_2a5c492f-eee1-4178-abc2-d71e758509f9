{{- range .Values.storageClasses }}
---
kind: StorageClass
apiVersion: storage.k8s.io/v1
metadata:
  name: {{ .name }}
  {{- with .annotations }}
  annotations: {{- . | toYaml | trim | nindent 4 }}
  {{- end }}
  {{- with .labels }}
  labels: {{- . | toYaml | trim | nindent 4 }}
  {{- end }}
provisioner: ebs.csi.aws.com
{{ omit (dict "volumeBindingMode" "WaitForFirstConsumer" | merge .) "name" "annotations" "labels" | toYaml }}
{{- end }}
