{{$defaultArgs := dict
  "NodeName" "ebs-csi-node"
}}
{{- include "node" (deepCopy $ | mustMerge $defaultArgs) -}}
{{- range $name, $values := .Values.additionalDaemonSets }}
{{$args := dict
  "NodeName" (printf "ebs-csi-node-%s" $name)
  "Values" (dict
    "node" (deepCopy $.Values.node | mustMerge $values)
  )
}}
{{- include "node" (deepCopy $ | mustMerge $args) -}}
{{- end }}
{{- if .Values.a1CompatibilityDaemonSet }}
{{$args := dict
  "NodeName" "ebs-csi-node-a1compat"
  "Values" (dict
    "image" (dict
      "tag" (printf "%s-a1compat" (default (printf "v%s" .Chart.AppVersion) (.Values.image.tag | toString)))
    )
    "node" (dict
      "affinity" (dict
        "nodeAffinity" (dict
          "requiredDuringSchedulingIgnoredDuringExecution" (dict
            "nodeSelectorTerms" (list
              (dict "matchExpressions" (list
                (dict
                  "key" "eks.amazonaws.com/compute-type"
                  "operator" "NotIn"
                  "values" (list "fargate")
                )
                (dict
                  "key" "node.kubernetes.io/instance-type"
                  "operator" "In"
                  "values" (list "a1.medium" "a1.large" "a1.xlarge" "a1.2xlarge" "a1.4xlarge")
                )
              ))
            )
          )
        )
      )
    )
  )
}}
{{- include "node" (deepCopy $ | mustMerge $args) -}}
{{- end }}
