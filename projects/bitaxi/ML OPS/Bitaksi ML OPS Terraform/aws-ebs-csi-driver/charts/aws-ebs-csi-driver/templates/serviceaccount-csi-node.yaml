{{- if .Values.node.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.node.serviceAccount.name }}
  namespace: {{ .Values.node.namespaceOverride | default .Release.Namespace }}
  labels:
    {{- include "aws-ebs-csi-driver.labels" . | nindent 4 }}
  {{- with .Values.node.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- if .Values.node.serviceAccount.automountServiceAccountToken }}
automountServiceAccountToken: {{ .Values.node.serviceAccount.automountServiceAccountToken }}
{{- end }}
{{- end -}}
