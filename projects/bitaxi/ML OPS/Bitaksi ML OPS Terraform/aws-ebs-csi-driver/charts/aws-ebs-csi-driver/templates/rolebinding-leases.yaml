kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ebs-csi-leases-rolebinding
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "aws-ebs-csi-driver.labels" . | nindent 4 }}
subjects:
- kind: ServiceAccount
  name: {{ .Values.controller.serviceAccount.name }}
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: Role
  name: ebs-csi-leases-role
  apiGroup: rbac.authorization.k8s.io
