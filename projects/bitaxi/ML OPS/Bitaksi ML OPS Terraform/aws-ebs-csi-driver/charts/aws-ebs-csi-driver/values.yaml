# Default values for aws-ebs-csi-driver.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

image:
  repository: public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver
  # Overrides the image tag whose default is v{{ .Chart.AppVersion }}
  tag: ""
  pullPolicy: IfNotPresent
# -- Custom labels to add into metadata
customLabels: {}
# k8s-app: aws-ebs-csi-driver

sidecars:
  provisioner:
    env: []
    image:
      pullPolicy: IfNotPresent
      repository: public.ecr.aws/eks-distro/kubernetes-csi/external-provisioner
      tag: "v5.0.1-eks-1-30-10"
    logLevel: 2
    # Additional parameters provided by external-provisioner.
    additionalArgs: []
    # Grant additional permissions to external-provisioner
    additionalClusterRoleRules:
    resources: {}
    # Tune leader lease election for csi-provisioner.
    # Leader election is on by default.
    leaderElection:
      enabled: true
      # Optional values to tune lease behavior.
      # The arguments provided must be in an acceptable time.ParseDuration format.
      # Ref: https://pkg.go.dev/flag#Duration
      # leaseDuration: "15s"
      # renewDeadline: "10s"
      # retryPeriod: "5s"
    securityContext:
      seccompProfile:
        type: RuntimeDefault
      readOnlyRootFilesystem: true
      allowPrivilegeEscalation: false
  attacher:
    env: []
    image:
      pullPolicy: IfNotPresent
      repository: public.ecr.aws/eks-distro/kubernetes-csi/external-attacher
      tag: "v4.6.1-eks-1-30-10"
    # Tune leader lease election for csi-attacher.
    # Leader election is on by default.
    leaderElection:
      enabled: true
      # Optional values to tune lease behavior.
      # The arguments provided must be in an acceptable time.ParseDuration format.
      # Ref: https://pkg.go.dev/flag#Duration
      # leaseDuration: "15s"
      # renewDeadline: "10s"
      # retryPeriod: "5s"
    logLevel: 2
    # Additional parameters provided by external-attacher.
    additionalArgs: []
    # Grant additional permissions to external-attacher
    additionalClusterRoleRules: []
    resources: {}
    securityContext:
      seccompProfile:
        type: RuntimeDefault
      readOnlyRootFilesystem: true
      allowPrivilegeEscalation: false
  snapshotter:
    # Enables the snapshotter sidecar even if the snapshot CRDs are not installed
    forceEnable: false
    env: []
    image:
      pullPolicy: IfNotPresent
      repository: public.ecr.aws/eks-distro/kubernetes-csi/external-snapshotter/csi-snapshotter
      tag: "v8.0.1-eks-1-30-10"
    logLevel: 2
    # Additional parameters provided by csi-snapshotter.
    additionalArgs: []
    # Grant additional permissions to csi-snapshotter
    additionalClusterRoleRules: []
    resources: {}
    securityContext:
      seccompProfile:
        type: RuntimeDefault
      readOnlyRootFilesystem: true
      allowPrivilegeEscalation: false
  livenessProbe:
    image:
      pullPolicy: IfNotPresent
      repository: public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe
      tag: "v2.13.0-eks-1-30-10"
    # Additional parameters provided by livenessprobe.
    additionalArgs: []
    resources: {}
    securityContext:
      readOnlyRootFilesystem: true
      allowPrivilegeEscalation: false
  resizer:
    env: []
    image:
      pullPolicy: IfNotPresent
      repository: public.ecr.aws/eks-distro/kubernetes-csi/external-resizer
      tag: "v1.11.1-eks-1-30-10"
    # Tune leader lease election for csi-resizer.
    # Leader election is on by default.
    leaderElection:
      enabled: true
      # Optional values to tune lease behavior.
      # The arguments provided must be in an acceptable time.ParseDuration format.
      # Ref: https://pkg.go.dev/flag#Duration
      # leaseDuration: "15s"
      # renewDeadline: "10s"
      # retryPeriod: "5s"
    logLevel: 2
    # Additional parameters provided by external-resizer.
    additionalArgs: []
    # Grant additional permissions to external-resizer
    additionalClusterRoleRules: []
    resources: {}
    securityContext:
      seccompProfile:
        type: RuntimeDefault
      readOnlyRootFilesystem: true
      allowPrivilegeEscalation: false
  nodeDriverRegistrar:
    env: []
    image:
      pullPolicy: IfNotPresent
      repository: public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar
      tag: "v2.11.0-eks-1-30-10"
    logLevel: 2
    # Additional parameters provided by node-driver-registrar.
    additionalArgs: []
    resources: {}
    securityContext:
      readOnlyRootFilesystem: true
      allowPrivilegeEscalation: false
    livenessProbe:
      exec:
        command:
          - /csi-node-driver-registrar
          - --kubelet-registration-path=$(DRIVER_REG_SOCK_PATH)
          - --mode=kubelet-registration-probe
      initialDelaySeconds: 30
      periodSeconds: 90
      timeoutSeconds: 15
  volumemodifier:
    env: []
    image:
      pullPolicy: IfNotPresent
      repository: public.ecr.aws/ebs-csi-driver/volume-modifier-for-k8s
      tag: "v0.3.0"
    leaderElection:
      enabled: true
      # Optional values to tune lease behavior.
      # The arguments provided must be in an acceptable time.ParseDuration format.
      # Ref: https://pkg.go.dev/flag#Duration
      # leaseDuration: "15s"
      # renewDeadline: "10s"
      # retryPeriod: "5s"
    logLevel: 2
    # Additional parameters provided by volume-modifier-for-k8s.
    additionalArgs: []
    resources: {}
    securityContext:
      seccompProfile:
        type: RuntimeDefault
      readOnlyRootFilesystem: true
      allowPrivilegeEscalation: false
proxy:
  http_proxy:
  no_proxy:
imagePullSecrets: []
nameOverride:
fullnameOverride:
awsAccessSecret:
  name: aws-secret
  keyId: key_id
  accessKey: access_key
controller:
  batching: true
  volumeModificationFeature:
    enabled: false
  # Additional parameters provided by aws-ebs-csi-driver controller.
  additionalArgs: []
  sdkDebugLog: false
  loggingFormat: text
  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 1
          preference:
            matchExpressions:
              - key: eks.amazonaws.com/compute-type
                operator: NotIn
                values:
                  - fargate
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - podAffinityTerm:
            labelSelector:
              matchExpressions:
                - key: app
                  operator: In
                  values:
                    - ebs-csi-controller
            topologyKey: kubernetes.io/hostname
          weight: 100
  # The default filesystem type of the volume to provision when fstype is unspecified in the StorageClass.
  # If the default is not set and fstype is unset in the StorageClass, then no fstype will be set
  defaultFsType: ext4
  env: []
  # Use envFrom to reference ConfigMaps and Secrets across all containers in the deployment
  envFrom: []
  # If set, add pv/pvc metadata to plugin create requests as parameters.
  extraCreateMetadata: true
  # Extra volume tags to attach to each dynamically provisioned volume.
  # ---
  # extraVolumeTags:
  #   key1: value1
  #   key2: value2
  extraVolumeTags: {}
  httpEndpoint:
  # (deprecated) The TCP network address where the prometheus metrics endpoint
  # will run (example: `:8080` which corresponds to port 8080 on local host).
  # The default is empty string, which means metrics endpoint is disabled.
  # ---
  enableMetrics: false
  serviceMonitor:
    # Enables the ServiceMonitor resource even if the prometheus-operator CRDs are not installed
    forceEnable: false
    # Additional labels for ServiceMonitor object
    labels:
      release: prometheus
    interval: "15s"
  # If set to true, AWS API call metrics will be exported to the following
  # TCP endpoint: "0.0.0.0:3301"
  # ---
  # ID of the Kubernetes cluster used for tagging provisioned EBS volumes (optional).
  k8sTagClusterId:
  logLevel: 2
  userAgentExtra: "helm"
  nodeSelector: {}
  deploymentAnnotations: {}
  podAnnotations: {}
  podLabels: {}
  priorityClassName: system-cluster-critical
  # AWS region to use. If not specified then the region will be looked up via the AWS EC2 metadata
  # service.
  # ---
  # region: us-east-1
  region:
  replicaCount: 2
  revisionHistoryLimit: 10
  socketDirVolume:
    emptyDir: {}
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
  # type: RollingUpdate
  # rollingUpdate:
  #   maxSurge: 0
  #   maxUnavailable: 1
  resources:
    requests:
      cpu: 10m
      memory: 40Mi
    limits:
      memory: 256Mi
  serviceAccount:
    # A service account will be created for you if set to true. Set to false if you want to use your own.
    create: true
    name: ebs-csi-controller-sa
    annotations: {}
    ## Enable if EKS IAM for SA is used
    # eks.amazonaws.com/role-arn: arn:<partition>:iam::<account>:role/ebs-csi-role
    automountServiceAccountToken: true
  tolerations:
    - key: CriticalAddonsOnly
      operator: Exists
    - effect: NoExecute
      operator: Exists
      tolerationSeconds: 300
  # TSCs without the label selector stanza
  #
  # Example:
  #
  # topologySpreadConstraints:
  #  - maxSkew: 1
  #    topologyKey: topology.kubernetes.io/zone
  #    whenUnsatisfiable: ScheduleAnyway
  #  - maxSkew: 1
  #    topologyKey: kubernetes.io/hostname
  #    whenUnsatisfiable: ScheduleAnyway
  topologySpreadConstraints: []
  # securityContext on the controller pod
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000
  # Add additional volume mounts on the controller with controller.volumes and controller.volumeMounts
  volumes: []
  # Add additional volumes to be mounted onto the controller:
  # - name: custom-dir
  #   hostPath:
  #     path: /path/to/dir
  #     type: Directory
  volumeMounts: []
  # And add mount paths for those additional volumes:
  # - name: custom-dir
  #   mountPath: /mount/path
  # ---
  # securityContext on the controller container (see sidecars for securityContext on sidecar containers)
  containerSecurityContext:
    seccompProfile:
      type: RuntimeDefault
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
  initContainers: []
  # containers to be run before the controller's container starts.
  #
  # Example:
  #
  # - name: wait
  #   image: busybox
  #   command: [ 'sh', '-c', "sleep 20" ]
  # Enable opentelemetry tracing for the plugin running on the daemonset
  otelTracing: {}
  #  otelServiceName: ebs-csi-controller
  #  otelExporterEndpoint: "http://localhost:4317"

  # Enable dnsConfig for the controller and node pods
  dnsConfig: {}
node:
  env: []
  envFrom: []
  kubeletPath: /var/lib/kubelet
  loggingFormat: text
  logLevel: 2
  priorityClassName:
  additionalArgs: []
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: eks.amazonaws.com/compute-type
                operator: NotIn
                values:
                  - fargate
              - key: node.kubernetes.io/instance-type
                operator: NotIn
                values:
                  - a1.medium
                  - a1.large
                  - a1.xlarge
                  - a1.2xlarge
                  - a1.4xlarge
  nodeSelector: {}
  daemonSetAnnotations: {}
  podAnnotations: {}
  podLabels: {}
  terminationGracePeriodSeconds: 30
  tolerateAllTaints: true
  tolerations:
    - operator: Exists
      effect: NoExecute
      tolerationSeconds: 300
  resources:
    requests:
      cpu: 10m
      memory: 40Mi
    limits:
      memory: 256Mi
  revisionHistoryLimit: 10
  probeDirVolume:
    emptyDir: {}
  serviceAccount:
    create: true
    name: ebs-csi-node-sa
    annotations: {}
    ## Enable if EKS IAM for SA is used
    # eks.amazonaws.com/role-arn: arn:<partition>:iam::<account>:role/ebs-csi-role
    automountServiceAccountToken: true
  # Enable the linux daemonset creation
  enableLinux: true
  enableWindows: false
  # The number of attachment slots to reserve for system use (and not to be used for CSI volumes)
  # When this parameter is not specified (or set to -1), the EBS CSI Driver will attempt to determine the number of reserved slots via heuristic
  # Cannot be specified at the same time as `node.volumeAttachLimit`
  reservedVolumeAttachments:
  # The "maximum number of attachable volumes" per node
  # Cannot be specified at the same time as `node.reservedVolumeAttachments`
  volumeAttachLimit:
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: "10%"
  hostNetwork: false
  # securityContext on the node pod
  securityContext:
    # The node pod must be run as root to bind to the registration/driver sockets
    runAsNonRoot: false
    runAsUser: 0
    runAsGroup: 0
    fsGroup: 0
  # allows you to deploy aws-ebs-csi-node daemonset to separate namespace (make sure namespace exists before deploy)
  namespaceOverride: ""
  # Add additional volume mounts on the node pods with node.volumes and node.volumeMounts
  volumes: []
  # Add additional volumes to be mounted onto the node pods:
  # - name: custom-dir
  #   hostPath:
  #     path: /path/to/dir
  #     type: Directory
  volumeMounts: []
  # And add mount paths for those additional volumes:
  # - name: custom-dir
  #   mountPath: /mount/path
  # ---
  # securityContext on the node container (see sidecars for securityContext on sidecar containers)
  # Privileged containers always run as `Unconfined`, which means that they are not restricted by a seccomp profile.
  containerSecurityContext:
    readOnlyRootFilesystem: true
    privileged: true
  # Enable opentelemetry tracing for the plugin running on the daemonset
  otelTracing: {}
  #  otelServiceName: ebs-csi-node
  #  otelExporterEndpoint: "http://localhost:4317"
additionalDaemonSets:
# Additional node DaemonSets, using the node config structure
# See docs/additional-daemonsets.md for more information
#
# example:
#   nodeSelector:
#     node.kubernetes.io/instance-type: c5.large
#   volumeAttachLimit: 15

# Enable compatibility for the A1 instance family via use of an AL2-based image in a separate DaemonSet
# a1CompatibilityDaemonSet: true
storageClasses: []
# Add StorageClass resources like:
# - name: ebs-sc
#   # annotation metadata
#   annotations:
#     storageclass.kubernetes.io/is-default-class: "true"
#   # label metadata
#   labels:
#     my-label-is: supercool
#   # defaults to WaitForFirstConsumer
#   volumeBindingMode: WaitForFirstConsumer
#   # defaults to Delete
#   reclaimPolicy: Retain
#   parameters:
#     encrypted: "true"

defaultStorageClass:
  enabled: false
volumeSnapshotClasses: []
# Add VolumeSnapshotClass resources like:
# - name: ebs-vsc
#   # annotation metadata
#   annotations:
#     snapshot.storage.kubernetes.io/is-default-class: "true"
#   # label metadata
#   labels:
#     my-label-is: supercool
#   # deletionPolicy must be specified
#   deletionPolicy: Delete
#   parameters:

# Use old CSIDriver without an fsGroupPolicy set
# Intended for use with older clusters that cannot easily replace the CSIDriver object
# This parameter should always be false for new installations
useOldCSIDriver: false
helmTester:
  enabled: true
  # Supply a custom image to the ebs-csi-driver-test pod in helm-tester.yaml
  image: "gcr.io/k8s-staging-test-infra/kubekins-e2e:v20240705-131cd74733-master"
