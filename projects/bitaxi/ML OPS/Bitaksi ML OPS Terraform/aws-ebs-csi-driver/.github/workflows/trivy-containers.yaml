# Copyright 2024 The Kubernetes Authors.
#
# Licensed under the Apache License, Version 2.0 (the 'License');
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Trivy Scanner - Container Images
on:
  push:
    branches:
      - master
  schedule:
    - cron: '0 */24 * * *'

jobs:
  build-matrix:
    runs-on: ubuntu-latest
    outputs:
      images: ${{ steps.set-matrix.outputs.result }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - id: set-matrix
        uses: mikefarah/yq@master
        with:
          # Dynamically build the matrix of images to scan
          cmd: "yq '[{\"repository\": .image.repository, \"tag\": \"v'$(yq '.appVersion' charts/aws-ebs-csi-driver/Chart.yaml)'\"}] + (.sidecars | map(.image)) | map(.repository + \":\" + .tag) | . style=\"flow\"' charts/aws-ebs-csi-driver/values.yaml"
  
  trivy-scan:
    needs: build-matrix
    runs-on: ubuntu-latest
    strategy:
      matrix:
        image: ${{ fromJson(needs.build-matrix.outputs.images) }}
    
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
          role-session-name: GitHubCI
          aws-region: us-east-1
          role-duration-seconds: 1800
          role-skip-session-tagging: true

      - name: Login to Amazon ECR Public
        uses: aws-actions/amazon-ecr-login@v2
        with:
          registry-type: public
          
      - name: Pull container image
        run: docker pull ${{ matrix.image }}

      - name: Scan container image
        uses: aquasecurity/trivy-action@0.16.1
        with:
          image-ref: '${{ matrix.image }}'
          output: 'results.sarif'
          format: 'sarif'
          ignore-unfixed: true
          severity: 'HIGH,CRITICAL'
      
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'results.sarif'
