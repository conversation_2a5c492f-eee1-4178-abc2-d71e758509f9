# Copyright 2024 The Kubernetes Authors.
#
# Licensed under the Apache License, Version 2.0 (the 'License');
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Generate Code Coverage
on: [pull_request]
  
jobs:
  cover-base:
    name: Generate Base Coverage
    runs-on: ubuntu-latest
    steps:
      - name: Checkout base
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.base.ref }}

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version-file: 'go.mod'

      - name: Generate report
        run: |
          go test -coverprofile base-coverage.tmp ./cmd/... ./pkg/...
          cat base-coverage.tmp | grep -v "mock_" > base-coverage.out

      - name: Upload report
        uses: actions/upload-artifact@v3
        with:
          name: base-coverage
          path: base-coverage.out

  cover-pr:
    name: Generate PR Coverage
    runs-on: ubuntu-latest
    steps:
      - name: Checkout PR
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version-file: 'go.mod'

      - name: Generate report
        run: |
          go test -coverprofile pr-coverage.tmp ./cmd/... ./pkg/...
          cat pr-coverage.tmp | grep -v "mock_" > pr-coverage.out

      - name: Upload report
        uses: actions/upload-artifact@v3
        with:
          name: pr-coverage
          path: pr-coverage.out
