# Go 1.21.4 image
FROM golang:1.21.4 as builder

# Set the Current Working Directory inside the container
WORKDIR /go/src/app

# Copy go mod and sum files
COPY ./lambda/functions/golang/go.mod ./lambda/functions/golang/go.sum ./

# Download all dependencies
RUN go mod download

# Copy the source code
COPY ./lambda/functions/golang/ .

# Build the Go app for arm64 architecture
RUN CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o /go/src/app/app

# Make the binary executable
RUN chmod +x ./app

# Set the entrypoint for the Lambda function
ENTRYPOINT ["./app"]
