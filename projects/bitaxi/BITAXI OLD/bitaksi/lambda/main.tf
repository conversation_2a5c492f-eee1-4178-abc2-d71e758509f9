resource "aws_cloudwatch_event_rule" "skyloop-bedrock-lambda-rule" {
  name        = "skyloop-bedrock-lambda-rule"
  description = "skyloop-bedrock-lambda-rule"
  state = "DISABLED"
  schedule_expression = "cron(0 3 * * ? *)"
  tags = {
	Name = "bedrock-lambda"
  }
}

resource "aws_cloudwatch_event_target" "skyloop-bedrock-lambda-target" {
  rule      = aws_cloudwatch_event_rule.skyloop-bedrock-lambda-rule.name
  target_id = "skyloop-bedrock-lambda-target"
  arn       = aws_lambda_function.skyloop-bedrock-lambda.arn
}

 resource "aws_iam_role" "skyloop-bedrock-lambda-role" {
   name = "skyloop-bedrock-lambda-role"
   assume_role_policy = <<EOF
 {
   "Version": "2012-10-17",
   "Statement": [
 	{
 	  "Action": "sts:AssumeRole",
 	  "Principal": {
 		"Service": "lambda.amazonaws.com"
 	  },
 	  "Effect": "Allow",
 	  "Sid": ""
 	}
   ]
 }
 EOF
  tags = aws_cloudwatch_event_rule.skyloop-bedrock-lambda-rule.tags
 }

 resource "aws_iam_policy" "skyloop-bedrock-lambda-execution-policy" {
   name = "skyloop-bedrock-lambda-execution-policy"
   policy = <<EOF
 {
     "Version": "2012-10-17",
     "Statement": [
         {
             "Effect": "Allow",
             "Action": [
                 "logs:CreateLogGroup",
                 "logs:CreateLogStream",
                 "logs:PutLogEvents"
             ],
             "Resource": "arn:aws:logs:*:*:*"
         }
     ]
 }
 EOF
  tags = aws_cloudwatch_event_rule.skyloop-bedrock-lambda-rule.tags
 }

 resource "aws_iam_role_policy_attachment" "skyloop-bedrock-lambda-execution-policy-attachment" {
   role = aws_iam_role.skyloop-bedrock-lambda-role.name
   policy_arn = aws_iam_policy.skyloop-bedrock-lambda-execution-policy.arn
 }
 
 resource "aws_iam_policy" "skyloop-bedrock-full-access-policy" {
   name = "skyloop-bedrock-full-access-policy"
   policy = <<EOF
{
  "Version" : "2012-10-17",
  "Statement" : [
    {
      "Sid" : "BedrockAll",
      "Effect" : "Allow",
      "Action" : [
        "bedrock:*"
      ],
      "Resource" : "*"
    },
    {
      "Sid" : "DescribeKey",
      "Effect" : "Allow",
      "Action" : [
        "kms:DescribeKey"
      ],
      "Resource" : "arn:*:kms:*:::*"
    },
    {
      "Sid" : "APIsWithAllResourceAccess",
      "Effect" : "Allow",
      "Action" : [
        "iam:ListRoles",
        "ec2:DescribeVpcs",
        "ec2:DescribeSubnets",
        "ec2:DescribeSecurityGroups"
      ],
      "Resource" : "*"
    },
    {
      "Sid" : "PassRoleToBedrock",
      "Effect" : "Allow",
      "Action" : [
        "iam:PassRole"
      ],
      "Resource" : "arn:aws:iam::*:role/*AmazonBedrock*",
      "Condition" : {
        "StringEquals" : {
          "iam:PassedToService" : [
            "bedrock.amazonaws.com"
          ]
        }
      }
    }
  ]
}
 EOF
  tags = aws_cloudwatch_event_rule.skyloop-bedrock-lambda-rule.tags
 }
 
 resource "aws_iam_role_policy_attachment" "skyloop-bedrock-full-access-policy-attachment" {
   role = aws_iam_role.skyloop-bedrock-lambda-role.name
   policy_arn = aws_iam_policy.skyloop-bedrock-full-access-policy.arn
 }

 resource "aws_iam_policy" "skyloop-bedrock-lambda-s3-policy" {
   name = "skyloop-bedrock-lambda-s3-policy"
   policy = <<EOF
 {
     "Version": "2012-10-17",
     "Statement": [
         {
             "Effect": "Allow",
             "Action": "s3:*",
             "Resource": [
				"arn:aws:s3:::${var.input_bucket_name}/*",
				"arn:aws:s3:::${var.output_bucket_name}/*"
				]
         }
     ]
 }
 EOF
  tags = aws_cloudwatch_event_rule.skyloop-bedrock-lambda-rule.tags
 }

 resource "aws_iam_role_policy_attachment" "skyloop-bedrock-lambda-s3-policy-attachment" {
   role = aws_iam_role.skyloop-bedrock-lambda-role.name
   policy_arn = aws_iam_policy.skyloop-bedrock-lambda-s3-policy.arn
 }
 
resource "aws_iam_policy" "skyloop-bedrock-lambda-ecr-policy" {
  name = "skyloop-bedrock-lambda-ecr-policy"
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": "ecr:*",
            "Resource": "${aws_ecr_repository.skyloop-bedrock-lambda-repo.arn}/*"
        }
    ]
}
EOF
  tags = aws_cloudwatch_event_rule.skyloop-bedrock-lambda-rule.tags
}

resource "aws_iam_role_policy_attachment" "skyloop-bedrock-lambda-ecr-policy-attachment" {
  role = aws_iam_role.skyloop-bedrock-lambda-role.name
  policy_arn = aws_iam_policy.skyloop-bedrock-lambda-ecr-policy.arn
}

resource "aws_lambda_function" "skyloop-bedrock-lambda" {
  function_name = "skyloop-bedrock-lambda"
  architectures  = ["arm64"]
  memory_size   = 128
  ephemeral_storage {
  size        = 512
  }
  package_type = "Image"
  timeout = 900
  
  image_uri = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/${var.repository_name}:latest"
  
  role = aws_iam_role.skyloop-bedrock-lambda-role.arn
  tags = aws_cloudwatch_event_rule.skyloop-bedrock-lambda-rule.tags
}

resource "aws_lambda_permission" "skyloop-bedrock-lambda-permission" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.skyloop-bedrock-lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.skyloop-bedrock-lambda-rule.arn
}

resource "aws_ecr_repository" "skyloop-bedrock-lambda-repo" {
  name = "skyloop-bedrock-lambda-repo"
  image_tag_mutability = "MUTABLE"

  image_scanning_configuration {
    scan_on_push = true
  }
  tags = aws_cloudwatch_event_rule.skyloop-bedrock-lambda-rule.tags
}

output "tags" {
  value = aws_cloudwatch_event_rule.skyloop-bedrock-lambda-rule.tags
}