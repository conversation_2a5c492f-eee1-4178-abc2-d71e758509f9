import re
import boto3
import json
from datetime import datetime, timezone, timedelta

first_prompt = """You are a tool that summarizes comments and criticisms in list format. Do not deviate from this function. I will provide you with comments enclosed in quotes, which may be in Turkish or various other languages. These comments will be separated by newlines. You are to extract criticisms and negative comments from them and provide summaries. These comments will follow in this text and the list format should be as follows: You will use only Turkish in your responses. Also, do not use any sentences or words outside this list. Just give the list. Don't even say "I can summarize it as follows:
- [Summary of criticism or comment 1]
- [Summary of criticism or comment 2]
- [Summary of criticism or comment 3]

"""

max_tokens = 60000

BUCKET_NAME = 'data-bedrock-poc'
OUT_BUCKET_NAME = 'data-bedrock-output'
s3 = boto3.client('s3')

date = datetime.now(timezone.utc) - timedelta(days=1)
date = date.strftime("%d-%m-%Y")
OUTPUT_FILE = "/tmp/" + date + "_comments.txt"

def get_s3_objects_end24hours():
    try:
        response = s3.list_objects_v2(Bucket=BUCKET_NAME)

        if 'Contents' in response:
            for obj in response['Contents']:
                last_24_hours = datetime.now(timezone.utc) - timedelta(days=1)
                if obj['LastModified'] > last_24_hours:
                    print(f"Son 24 saat içinde güncellenen dosya: {obj['Key']}")
                    return obj['Key']
        return None
    except Exception as e:
        print(e)
        return {
            'statusCode': 500,
            'body': json.dumps('Dosya getirilemedi.')
        }
    
def get_object_content_csv(object_key):
    try:
        file_obj = s3.get_object(Bucket=BUCKET_NAME, Key=object_key)
        file_content = file_obj['Body'].read().decode('utf-8')
        return file_content
    except Exception as e:
        print(e)
        return {
            'statusCode': 501,
            'body': json.dumps('Dosya içeriği okunamadı.')
        }

def filter_lines(file_path):
    with open(file_path, 'r') as file:
        lines = file.readlines()

    filtered_lines = [line for line in lines if line.strip().startswith("-")]
    
    with open(file_path, 'w') as file:
        file.writelines(filtered_lines)

def answer_add_to_file(completion):
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as file:
        file.write(completion)
        file.write("\n")

def anthropic_start(prompt):
    client = boto3.client(
        service_name="bedrock-runtime", region_name="us-east-1"
    )
    
    model_id = "anthropic.claude-3-haiku-20240307-v1:0"
    
    try:
        response = client.invoke_model(
            modelId=model_id,
            body=json.dumps({
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 1024,
                "messages": [
                    {
                        "role": "user",
                        "content": [{"type": "text", "text": prompt}]
                    }
                ],
            }),
        )

        result = json.loads(response.get("body").read())
        input_tokens = result["usage"]["input_tokens"]
        output_tokens = result["usage"]["output_tokens"]
        output_list = result.get("content", [])
        
        print("Invocation details:")
        print(f"- The input length is {input_tokens} tokens.")
        print(f"- The output length is {output_tokens} tokens.")
        
        print(f"- The model returned {len(output_list)} response(s):")
        for output in output_list:
            answer_add_to_file(output["text"])
        
        return result

    
    except Exception as e:
        print(f"Error: Couldn't invoke Claude 3 Haiku. Here's why: {str(e)}")
        raise

def token_count(text):
    tokens = re.findall(r'\b\w+\b', text)
    return len(tokens)

def split_the_list(list, max_tokens):
    grouped_lists = []
    temp_list = []
    token_counter = 0
    
    for item in list:
        item_token_count = token_count(item)
        if token_counter + item_token_count > (max_tokens - token_count(first_prompt)):
            grouped_lists.append(temp_list)
            temp_list = [item]
            token_counter = item_token_count
        else:
            token_counter += item_token_count
            temp_list.append(item)
    
    if temp_list:
        grouped_lists.append(temp_list)
    
    return grouped_lists

def combine_quoted_lines(lines):
    combined_lines = []
    current_line = ""
    in_quote = False

    for line in lines:
        quote_count = line.count('"')

        if in_quote:
            current_line += " " + line.strip()
        else:
            current_line = line.strip()
        
        if quote_count % 2 == 1:
            in_quote = not in_quote

        if not in_quote and current_line:
            combined_lines.append(current_line)
            current_line = ""

    if current_line:
        combined_lines.append(current_line)

    return combined_lines

def extract_reviews(data):
    merged_data = data.replace('\n', ' ')
    
    reviews = []
    pattern = re.compile(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{6},(".*?"|[^"]*?)(?=\s*\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{6},|$)')
    matches = pattern.finditer(merged_data)
    
    for match in matches:
        review = match.group(1).strip()
        if review.startswith('"') and review.endswith('"'):
            review = review[1:-1].replace(' ', ' ')
        reviews.append(review)
    split_list = split_the_list(reviews, max_tokens)


    for i, element in enumerate(split_list):
        print(f"Element {i+1} is sending")
        anthropic_start(first_prompt + "\n\n\'" +"\n".join(element) + "\'")


def put_s3_objects():
    try:
        s3.upload_file(OUTPUT_FILE, OUT_BUCKET_NAME, OUTPUT_FILE)
        print("File uploaded successfully!!!")
    except Exception as e:
        print(e)
        return {
        	'statusCode': 502,
        	'body': json.dumps('Dosya yüklenemedi.')
        }

def lambda_handler(event, context):
    #object = get_s3_objects_end24hours() # Son 24 saat içinde güncellenen dosyayı getir
    object = "client_review_sample.csv"
    object_content = get_object_content_csv(object)
    extract_reviews(str(object_content))
    filter_lines(OUTPUT_FILE)
    put_s3_objects()
    return {
		'statusCode': 200,
		'body': json.dumps("OK!")
	}

lambda_handler(None, None)
