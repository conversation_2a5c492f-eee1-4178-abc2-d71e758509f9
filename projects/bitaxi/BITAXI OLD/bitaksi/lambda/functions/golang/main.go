package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/bedrockruntime"
	"github.com/aws/aws-sdk-go/service/s3"
)

type MyResponse struct {
	Message string `json:"message"`
}

type ClaudeMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type ClaudeRequest struct {
	Messages         []ClaudeMessage `json:"messages"`
	MaxTokens        int             `json:"max_tokens"`
	Temperature      float64         `json:"temperature"`
	StopSequences    []string        `json:"stop_sequences"`
	AnthropicVersion string          `json:"anthropic_version"`
}

type ClaudeResponse struct {
	Content []struct {
		Text string `json:"text"`
	} `json:"content"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
	} `json:"usage"`
}

type InvokeModelWrapper struct {
	BedrockRuntimeClient *bedrockruntime.BedrockRuntime
}

var OUTPUT_FILE string
var FILE_TO_WRITE string

const (
	BUCKET_NAME     = "data-bedrock-poc"
	OUT_BUCKET_NAME = "data-bedrock-output"
)

var (
	firstPrompt = `You are conducting sentiment analysis. Your task is to create a balanced and comprehensive summary reflecting users' opinions about taxi drivers. The summary must include negative comments, criticisms, and positive feedback, accurately reflecting the overall sentiment. add every small detail to the analysis

	Ensure your summary:

	Accurately represents both positive and negative feedback.
	Includes percentages for all and each type of comment realted to the taxi drivers.
	Is always written in Turkish, regardless of the input language.
	The summary should be concise and in paragraph form and in detailed anyalysis. 
	the response will include detailed analysis like : sigara içme şikayetleri %3, hızlı sürüş şikayetleri %7 

		use this format:
	example: 

	Olumlu Geri Bildirimler (%60):
	- Sürücülerin çoğu .....(%)
	- Bazı sürücüler, .....(%)
	- Genel olarak s.....(%)
	.......
	...
	..

	Olumsuz Geri Bildirimler (%40): 
	- Bazı sürücüler ... (%)
	- Sigara içme, agresif .....(%)
	- Hizmet bedeli .....(%)
	- Navigasyon kullanımı ....(%)
	.....
	..
	.
	`



	secondPrompt = `You are an AI that interprets text composed of outputs from comment analyses. By interpreting this text, you should determine the general sentiment and key points of the comments. Additionally, you should specify the positive and negative aspects of the comments. Ensure your response is always in Turkish, independent of the input language. The analysis should include all the different type of feedbacks and the analysis should be detailed, and original. everything will be according to pecentage will be added to every detail that affects good and bad feedbacks. all feedback types should include a percentage.

	use this format:
	example: 

	Olumlu Geri Bildirimler (%60):
	- Sürücülerin çoğu .....(%)
	- Bazı sürücüler, .....(%)
	- Genel olarak s.....(%)
	.......
	...
	..

	Olumsuz Geri Bildirimler (%40): 
	- Bazı sürücüler ... (%)
	- Sigara içme, agresif .....(%)
	- Hizmet bedeli .....(%)
	- Navigasyon kullanımı ....(%)
	.....
	..
	.
	`

	maxTokens = 60000
	afterDays = 1
	retry     = 0
)

func main() {
	lambda.Start(lambdaHandler)
}

func getS3ObjectsEnd24Hours(svc *s3.S3) (string, error) {
	input := &s3.ListObjectsV2Input{
		Bucket: aws.String(BUCKET_NAME),
	}

	resp, err := svc.ListObjectsV2(input)
	if err != nil {
		return "", err
	}

	last24Hours := time.Now().Add(-24 * time.Hour)
	for _, obj := range resp.Contents {
		if obj.LastModified.After(last24Hours) {
			fmt.Printf("Son 24 saat içinde güncellenen dosya: %s\n", *obj.Key)
			return *obj.Key, nil
		}
	}
	return "", nil
}

func getObjectContentCSV(svc *s3.S3, objectKey string) (string, error) {
	input := &s3.GetObjectInput{
		Bucket: aws.String(BUCKET_NAME),
		Key:    aws.String(objectKey),
	}

	resp, err := svc.GetObjectWithContext(context.TODO(), input)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

func getObjectFileContent(objPath string) (string, error) {
	file, err := os.Open(FILE_TO_WRITE)
	if err != nil {
		return "", err
	}
	defer file.Close()

	body, err := ioutil.ReadAll(file)
	if err != nil {
		return "", err
	}
	return string(body), nil
}

func filterLines() error {
	file, err := os.Open(FILE_TO_WRITE)
	if err != nil {
		return err
	}
	defer file.Close()

	lines, err := ioutil.ReadAll(file)
	if err != nil {
		return err
	}

	filteredLines := []string{}
	for _, line := range strings.Split(string(lines), "\n") {
		if strings.HasPrefix(strings.TrimSpace(line), "-") {
			filteredLines = append(filteredLines, line)
		}
	}

	err = ioutil.WriteFile(FILE_TO_WRITE, []byte(strings.Join(filteredLines, "\n")), 0644)
	if err != nil {
		return err
	}

	return nil
}

func tokenCount(text string) int {
	return len(strings.Fields(text))
}

func splitTheList(list []string, maxTokens int) [][]string {
	var groupedLists [][]string
	var tempList []string
	tokenCounter := 0

	for _, item := range list {
		itemTokenCount := tokenCount(item)
		if tokenCounter+itemTokenCount > (maxTokens - tokenCount(firstPrompt)) {
			groupedLists = append(groupedLists, tempList)
			tempList = []string{item}
			tokenCounter = itemTokenCount
		} else {
			tokenCounter += itemTokenCount
			tempList = append(tempList, item)
		}
	}

	if len(tempList) > 0 {
		groupedLists = append(groupedLists, tempList)
	}

	return groupedLists
}

func answerAddToFile(completion string, value int) error {
	fmt.Println("Value: ", value)
	if value == 1 {
		file, err := os.OpenFile(FILE_TO_WRITE, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			return err
		}
		defer file.Close()

		_, err = file.WriteString(completion + "\n")
		if err != nil {
			return err
		}
	} else if value == 2 {
		// file, err := os.OpenFile(FILE_TO_WRITE, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		file, err := os.OpenFile(strings.Split(FILE_TO_WRITE, "_")[0] + "_second" +"_output.txt", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			return err
		}
		defer file.Close()

		_, err = file.WriteString(completion + "\n")
		if err != nil {
			return err
		}
	}
	return nil
}

func NewInvokeModelWrapper() (*InvokeModelWrapper, error) {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String("us-east-1"),
	})
	if err != nil {
		return nil, err
	}

	client := bedrockruntime.New(sess)
	return &InvokeModelWrapper{
		BedrockRuntimeClient: client,
	}, nil
}

func (wrapper *InvokeModelWrapper) InvokeClaude(prompt string, value int) (string, error) {
	modelID := "anthropic.claude-3-haiku-20240307-v1:0"

	messages := []ClaudeMessage{
		{
			Role:    "user",
			Content: prompt,
		},
	}

	body, err := json.Marshal(ClaudeRequest{
		Messages:         messages,
		MaxTokens:        1024,
		Temperature:      0.5,
		StopSequences:    []string{"\n\nHuman:"},
		AnthropicVersion: "bedrock-2023-05-31",
	})

	if err != nil {
		return "", err
	}

	output, err := wrapper.BedrockRuntimeClient.InvokeModel(&bedrockruntime.InvokeModelInput{
		ModelId:     aws.String(modelID),
		ContentType: aws.String("application/json"),
		Body:        body,
	})

	if err != nil {
		return "", err
	}

	var response ClaudeResponse
	if err := json.Unmarshal(output.Body, &response); err != nil {
		return "", err
	}

	if len(response.Content) == 0 {
		return "", fmt.Errorf("unexpected response format")
	}

	fmt.Printf("Invocation details:\n")
	fmt.Printf("- The input length is %d tokens.\n", response.Usage.InputTokens)
	fmt.Printf("- The output length is %d tokens.\n", response.Usage.OutputTokens)
	fmt.Printf("- The model returned %d response(s):\n", len(response.Content))

	//for _, output := range response.Content {
	//	answerAddToFile(output.Text, value)
	//}

	return response.Content[0].Text, nil
}

func anthropicStart(prompt string, value int) error {
	if value == 1 {
		wrapper, err := NewInvokeModelWrapper()
		if err != nil {
			log.Fatalf("failed to create wrapper: %v", err)
		}

		completion, err := wrapper.InvokeClaude(prompt, value)
		if err != nil {
			log.Fatalf("failed to invoke Claude: %v", err)
		}

		err = answerAddToFile(completion, value)
		if err != nil {
			return err
		}
	} else if value == 2 {
		wrapper, err := NewInvokeModelWrapper()
		if err != nil {
			log.Fatalf("failed to create wrapper: %v", err)
		}

		completion, err := wrapper.InvokeClaude(prompt, value)
		if err != nil {
			log.Fatalf("failed to invoke Claude: %v", err)
		}

		err = answerAddToFile(completion, value)
		if err != nil {
			return err
		}
	}
	return nil
}

func extractReviews(data string, value int) ([]string, error) {
	var reviews []string
	if value == 1 {
		mergedData := strings.ReplaceAll(data, "\n", " ")

		pattern := `\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{2,3},(.*?)(?:$|\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{2,3},)`
		re := regexp.MustCompile(pattern)

		matches := re.FindAllStringSubmatch(mergedData, -1)
		if matches == nil {
			return nil, fmt.Errorf("no matches found")
		}

		for _, match := range matches {
			if len(match) > 1 {
				review := strings.TrimSpace(match[1])
				if strings.HasPrefix(review, `"`) && strings.HasSuffix(review, `"`) {
					review = strings.Trim(review, `"`)
				}
				reviews = append(reviews, review)
			}
		}

		splitList := splitTheList(reviews, maxTokens)
		for i, element := range splitList {
			fmt.Printf("Element %d is sending to Claude\n", i+1)
			err := anthropicStart(firstPrompt+"'"+strings.Join(element, "\n")+"'", value)
			if err != nil {
				return nil, err
			}
			//fmt.Printf("index: %d, element: %s, retry: %d\n", i, element, retry)
			if i >= 1 {
				retry = 1
			}
		}
	} else if value == 2 {
		lines := strings.Split(data, "\n")
		var lineList []string
		var lineData string
		for _, line := range lines {
			if strings.HasPrefix(line, "-") {
				if lineData != "" {
					lineList = append(lineList, lineData)
				}
				lineData = line
			} else {
				lineData += " " + line
			}
		}

		splitList := splitTheList(lineList, maxTokens)
		for i, element := range splitList {
			fmt.Printf("Element %d is sending to Claude\n", i+1)
			err := anthropicStart(secondPrompt+"\n\n"+strings.Join(element, "\n"), value)
			if err != nil {
				return nil, err
			}

		}
	}
	return reviews, nil
}

func uploadFileToS3(filePath string, outputFileName string) error {
	region := "eu-central-1"

	file, err := os.Open(filePath)

	if err != nil {
		fmt.Println("Error opening file:", err)
		return err
	}
	defer file.Close()

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(region),
	})
	if err != nil {
		fmt.Println("Error creating session:", err)
		return err
	}

	svc := s3.New(sess)
	_, err = svc.PutObject(&s3.PutObjectInput{
		Bucket: aws.String(OUT_BUCKET_NAME),
		Key:    aws.String(outputFileName),
		Body:   file,
	})

	if err != nil {
		fmt.Println("Error uploading file:", err)
		return err
	}

	fmt.Println("File uploaded successfully!!!")
	return nil
}

func read_file(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	content, err := ioutil.ReadAll(file)
	if err != nil {
		return "", err
	}

	return string(content), nil
}

func lambdaHandler(ctx context.Context) (MyResponse, error) {
	date := time.Now().AddDate(0, 0, afterDays)
	dateString := date.Format("02-01-2006")

	OUTPUT_FILE = dateString + "_comments.txt"
	FILE_TO_WRITE = "/tmp/" + dateString + "_comments.txt"

	cfg, err := session.NewSession(&aws.Config{
		Region: aws.String("eu-central-1")},
	)
	if err != nil {
		fmt.Println("Error creating session:", err)
	}

	svc := s3.New(cfg)

	objectKey, err := getS3ObjectsEnd24Hours(svc)
	if err != nil {
		log.Fatalf("unable to list S3 objects, %v", err)
	}

	//objectKey := "client_review_sample.csv"
	fmt.Printf("Processing object: %s\n", objectKey)
	if objectKey != "" {
		csvContent, err := getObjectContentCSV(svc, objectKey)
		if err != nil {
			log.Fatalf("unable to get S3 object content, %v", err)
		}

		//read, err := read_file("../../../../reviews000.csv")
		//csvContent := read
		//fmt.Println("CSV Content:", csvContent)
		//os.Exit(0)
		_, err = extractReviews(csvContent, 1)
		if err != nil {
			log.Fatalf("error extracting reviews: %v", err)
		}

		//fmt.Println("retry: ", retry)
		if retry == 1 {
			fmt.Println("Retry is starting")
			content, err := getObjectFileContent(FILE_TO_WRITE)
			if err != nil {
				log.Fatalf("unable to get S3 object content, %v", err)
			}
			_, err = extractReviews(content, 2)
			if err != nil {
				log.Fatalf("error extracting reviews: %v", err)
			}
		}

		err = uploadFileToS3(FILE_TO_WRITE, OUTPUT_FILE)
		if err != nil {
			fmt.Println("Error uploading file:", err)
			return MyResponse{Message: "Error uploading file"}, err
		}

	} else {
		fmt.Println("No objects found in the last 24 hours.")
	}

	return MyResponse{Message: "Success"}, nil
}
