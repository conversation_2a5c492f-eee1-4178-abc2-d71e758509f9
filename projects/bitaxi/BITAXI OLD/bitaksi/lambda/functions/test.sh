#!/bin/bash

# Renk kodları
LIGHT_RED='\033[1;31m'
NO_COLOR='\033[0m'

# Süre ölçümünü başlat
start_time=$(LC_NUMERIC=C date +%s.%N)
datein=$(date +"%T.%N")
echo "Başlangıç zamanı: ${datein}"

# Python betiğini çalıştır
$1

# Süre ölçümünü bitir
end_time=$(LC_NUMERIC=C date +%s.%N)

# Süreyi hesapla (saniye cinsinden)
duration=$(echo "$end_time - $start_time" | LC_NUMERIC=C bc -l)

# <PERSON><PERSON>reyi saniye, dakika, saat ve salise olarak dönüştür
LC_NUMERIC=C seconds=$(printf "%.0f" "$duration")
minutes=$((seconds / 60))
hours=$((minutes / 60))
LC_NUMERIC=C milliseconds=$(printf "%.0f" "$(echo "($duration - $seconds) * 1000" | bc)")
seconds=$((seconds % 60))
minutes=$((minutes % 60))
hours=$((hours % 24))

# Çalışma süresini pembe turuncu (açık kırmızı) renkte yazdır
LC_NUMERIC=C printf "Çalışma süresi: ${LIGHT_RED}%02d saat %02d dakika %02d saniye %03d salise${NO_COLOR}\n" "$hours" "$minutes" "$seconds" "$milliseconds"
