# Terraform ignore rules
.DS_Store
.vscode/
layers/boto3-1.34.49/*
anthropic_bedrock_layer/*

# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log

# Exclude all .tfvars files, which might contain sensitive data, such as
# password, private keys, and other secrets. These should not be part of
# version control as they are data points which are potentially sensitive and
# subject to change depending on the environment.
*.tfvars
*.tfvars.json

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Ignore any .terraform.lock.hcl files, which may contain provider selections that
# should not be version controlled.
.terraform.lock.hcl

# Ignore any files that may contain sensitive data
secrets.tf
secrets.tfvars
secrets.tfvars.json

# Ignore .env files as they usually contain environment specific secrets
.env

# Ignore .tfplan files as they can contain sensitive data
*.tfplan

# Ignore .tgz files as they can be used for terraform module packaging
*.tgz

# out files log files txt files
*.txt
*.out
*.log
*.log.*

