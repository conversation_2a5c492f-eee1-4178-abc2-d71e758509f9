# Base image
FROM python:3.12-slim

# Set the working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    python3-dev \
    libhdf5-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy the requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# Set environment variable to ignore specific warnings
#ENV PYTHONWARNINGS="ignore:resume_download is deprecated"

# Copy the rest of the application into the container at /app
COPY ./lambda/functions/python/main.py /app

# Ensure the script has execution permissions
RUN chmod +x main.py

ENV AWS_ACCESS_KEY_ID=********************
ENV AWS_SECRET_ACCESS_KEY=ctsy6wn0UGQqP+6inrzIlkS+Y8M6eY+kejhGKhj6

## Run main.py when the container launches
CMD ["python3", "main.py"]