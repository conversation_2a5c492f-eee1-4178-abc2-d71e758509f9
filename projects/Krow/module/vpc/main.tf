resource "aws_vpc" "vpc" {
    cidr_block = var.cidr_block
    enable_dns_support = true
    enable_dns_hostnames = true

    tags = {
        Name = "krow-vpc"
    }
}

resource "aws_subnet" "public_subnet" {
    count = 2
    vpc_id = aws_vpc.vpc.id
    cidr_block = "${element(var.public_cidr_block, count.index)}"
    availability_zone = "${element(var.az, count.index)}"

    tags = {
        count = length(var.public_name)
        Name = var.public_name[count.index]
    }
}

resource "aws_subnet" "private_subnet" {
    count = 2
    vpc_id = aws_vpc.vpc.id
    cidr_block = "${element(var.private_cidr_block, count.index)}"
    availability_zone = "${element(var.az, count.index)}"

    tags = {
        count = length(var.private_name)
        Name = var.private_name[count.index]
    }    
}

resource "aws_internet_gateway" "igw" {
    vpc_id = aws_vpc.vpc.id

    tags = {
        Name = "krow-igw"
    }
}

resource "aws_eip" "nat_eip" {
    tags = {
        Name = "krow-eip"
    }
}

resource "aws_eip" "nat_eip2" {
    tags = {
        Name = "krow-eip-2"
    }
}

resource "aws_nat_gateway" "nat-gw" {
    allocation_id = aws_eip.nat_eip.id
    subnet_id = aws_subnet.public_subnet[0].id

    tags = {
        Name = "krow-nat"
    }

    depends_on = [aws_internet_gateway.igw]
}

resource "aws_nat_gateway" "nat-gw2" {
    allocation_id = aws_eip.nat_eip2.id
    subnet_id = aws_subnet.public_subnet[1].id

    tags = {
        Name = "krow-nat-2"
    }

    depends_on = [aws_internet_gateway.igw]
}

resource "aws_route_table" "public_route_table" {
    vpc_id = aws_vpc.vpc.id

    route {
        cidr_block = var.public_rt_cidr
        gateway_id = aws_internet_gateway.igw.id
    }

    tags = {
        Name = "krow-public-route-table"
    }
}

resource "aws_route_table" "private_route_table" {
    vpc_id = aws_vpc.vpc.id

    route {
        cidr_block = var.private_rt_cidr
        nat_gateway_id = aws_nat_gateway.nat-gw.id
    }
    
    tags = {
        Name = "krow-private-route-table"
    }
}

resource "aws_route_table" "private_route_table2" {
    vpc_id = aws_vpc.vpc.id

    route {
        cidr_block = var.private_rt_cidr
        nat_gateway_id = aws_nat_gateway.nat-gw2.id
    }
    
    tags = {
        Name = "krow-private-route-table-2"
    }
}

resource "aws_route_table_association" "public_rt_subnet_association_1" {
    subnet_id = aws_subnet.public_subnet[0].id
    route_table_id = aws_route_table.public_route_table.id
}
resource "aws_route_table_association" "public_rt_subnet_association_2" {
    subnet_id = aws_subnet.public_subnet[1].id
    route_table_id = aws_route_table.public_route_table.id
}

resource "aws_route_table_association" "private_rt_subnet_association_1" {
    subnet_id = aws_subnet.private_subnet[0].id
    route_table_id = aws_route_table.private_route_table.id
}

resource "aws_route_table_association" "private_rt_subnet_association_2" {
    subnet_id = aws_subnet.private_subnet[1].id
    route_table_id = aws_route_table.private_route_table2.id
}

# resource "aws_db_subnet_group" "rds_subnet_group" {
#     name = "krow-rds-subnet-group"
#     subnet_ids = [aws_subnet.private_subnet[1].id]
# }

resource "aws_security_group" "krow_alb_sg" {
  name        = "krow-alb-sg"
  description = "alb sg"
  vpc_id      = aws_vpc.vpc.id

  ingress {
    description     = "Allow HTTP traffic from anywhere"
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
  }

  ingress {
    description     = "Allow HTTP traffic from anywhere"
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}

resource "aws_security_group" "krow_ecs_task_sg" {
  name        = "krow-ecs-task-sg"
  description = "ecs task sg"
  vpc_id      = aws_vpc.vpc.id

  ingress {
    description = ""
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = []
  }
  ingress {
    description = ""
    from_port   = 4317
    protocol    = "tcp"
    self = false
    to_port = 4317
    cidr_blocks = ["0.0.0.0/0"]
  }
  ingress {
    description = ""
    from_port   = 16686
    protocol    = "tcp"
    self = false
    to_port = 16686
    security_groups  = ["sg-0890c1828c501ea81"]
    cidr_blocks = []
  }
  ingress {
    description = ""
    from_port   = 8080
    protocol    = "tcp"
    self = false
    to_port = 8080
    security_groups  = ["sg-0d71bcf787c6de6e5"]
    cidr_blocks = []
  }
  ingress {
    cidr_blocks = []
    description = ""
    from_port   = 80
    protocol    = "tcp"
    self = false
    security_groups  = ["sg-0d71bcf787c6de6e5"]
    to_port = 80
  }
  


  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
  
}

resource "aws_security_group" "krow_db_sg" {
  name        = "krow-db-sg"
  description = "My security group for RDS"
  vpc_id      = aws_vpc.vpc.id
  
  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Adjust this to your actual needs
  }
    egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}


resource "aws_security_group" "krow_redis_sg" {
  name        = "krow-redis-sg"
  description = "My security group for RDS"
  vpc_id      = aws_vpc.vpc.id
  
  ingress {
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Adjust this to your actual needs
  }
    egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}


resource "aws_security_group" "opensearch_sg" {
  name        = "opensearch-sg"
  description = "Allow inbound traffic to OpenSearch"
  vpc_id      = aws_vpc.vpc.id


  ingress {
    description = "VPC"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    self             = false
    cidr_blocks  = ["0.0.0.0/0"]
  }

  ingress {
    description = "VPC"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    security_groups  = ["sg-08e3cbb5414bd927d"]
    self             = false
  }

  ingress {
    cidr_blocks = []
    description = ""
    from_port   = 80
    protocol    = "tcp"
    self = false
    security_groups  = ["sg-08e3cbb5414bd927d"]
    to_port = 80
  }

  egress {
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    cidr_blocks     = ["0.0.0.0/0"]
  }

  tags = {
    Name = "opensearch-sg"
  }
}
