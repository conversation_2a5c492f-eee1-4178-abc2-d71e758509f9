output "vpc_id" {
    value = aws_vpc.vpc.id
}

output "private_subnet_id" {
    value = aws_subnet.private_subnet
}

output "public_subnet_id" {
    value = aws_subnet.public_subnet
}

output "sg_ecs_task_id" {
    value = aws_security_group.krow_ecs_task_sg.id
}

output "alb_sg_id" {
    value = aws_security_group.krow_alb_sg.id
}

output "db_sg_id" {
    value = aws_security_group.krow_db_sg.id
}
output "krow_redis_sg_id" {
    value = aws_security_group.krow_redis_sg.id
}

output "opensearch_sg_id" {
  value = aws_security_group.opensearch_sg.id
}



