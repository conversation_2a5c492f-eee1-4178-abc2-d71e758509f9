resource "aws_lb" "krow_alb" {
    name               = "krow-alb"
    load_balancer_type = "application"
    security_groups    = [var.alb_sg_id]
    subnets            = [var.public_subnet_id[0].id, var.public_subnet_id[1].id]
    enable_deletion_protection = false
}

resource "aws_lb_target_group" "alb_tg_ecs" {
    name     = "krow-alb-tg-ecs"
    port     = 3000
    protocol = "HTTP"
    target_type = "ip"
    vpc_id   = var.vpc_id
}

resource "aws_lb_listener" "alb_listener_ecs" {
    load_balancer_arn = aws_lb.krow_alb.id
    port     = "80"
    protocol = "HTTP"

    default_action {
        type = "forward"
        target_group_arn = aws_lb_target_group.alb_tg_ecs.id
    }
}

