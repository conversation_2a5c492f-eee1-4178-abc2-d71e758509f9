
resource "aws_elasticache_subnet_group" "krow_subnet_group" {
  name       = "krow-elasticache-subnet-group"
  description = "krow ElastiCache subnet group"
  subnet_ids = [var.private_subnet_id[1].id] # Replace with your actual subnet IDs
}

resource "aws_elasticache_cluster" "krow_elasticache" {
  cluster_id           = "krow-elasticache-cluster"
  engine               = "redis"  # Replace with the desired ElastiCache engine
  node_type         = "cache.t3.medium"
  num_cache_nodes      = 1
  port                 = 6379  # Adjust to the appropriate port for your engine
  parameter_group_name = "default.redis7"
  subnet_group_name    = aws_elasticache_subnet_group.krow_subnet_group.name
  security_group_ids    = [var.krow_redis_sg_id]
  # Add more ElastiCache configuration as needed
}