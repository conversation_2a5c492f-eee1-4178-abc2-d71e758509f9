resource "aws_ecs_cluster" "ecs_cluster" {
    name = "krow-cluster"
}
resource "aws_iam_role" "ecsTaskExecutionRole" {
  name               = "ecsTaskExecutionRole"
  assume_role_policy = "${data.aws_iam_policy_document.assume_role_policy.json}"
}
data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}
resource "aws_iam_role_policy_attachment" "ecsTaskExecutionRole_policy" {
  role       = "${aws_iam_role.ecsTaskExecutionRole.name}"
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}
# resource "aws_ecs_task_definition" "ecs_task" {
#   family                   = "krow-task" 
#   container_definitions = jsonencode([
#     {
#       name  = "my-container",
#       image = "your-repo/your-image:your-tag",  # Replace with your image (even a placeholder)
#     },
#   ])
  # container_definitions    = <<DEFINITION
  # [
  #   {
  #           "name": "krow-task",
  #           "image": "599468238415.dkr.ecr.eu-central-1.amazonaws.com/krow_app:latest",
  #           "cpu": 1024,
  #           "memory": 2048,
  #           "portMappings": [
  #               {
  #                   "name": "krow_app",
  #                   "containerPort": 3000,
  #                   "hostPort": 3000,
  #                   "protocol": "tcp"
  #               }
  #           ],
  #           "essential": true,
  #           "environment": [],
  #           "environmentFiles": [
  #               {
  #                   "value": "arn:aws:s3:::krowappbucket/.env",
  #                   "type": "s3"
  #               }
  #           ],
  #           "mountPoints": [],
  #           "volumesFrom": [],
  #           "logConfiguration": {
  #               "logDriver": "awslogs",
  #               "options": {
  #                   "awslogs-group": "krow-log",
  #                   "awslogs-region": "eu-central-1",
  #                   "awslogs-stream-prefix": "streaming"
  #               }
  #           }
  #       }
  # ]
  # DEFINITION
  # requires_compatibilities = ["FARGATE"] 
  # network_mode             = "awsvpc"    
  # memory                   = 2048        
  # cpu                      = 1024         
  # execution_role_arn       = "${aws_iam_role.ecsTaskExecutionRole.arn}"
  # task_role_arn            = "${aws_iam_role.ecsTaskExecutionRole.arn}"
# }

# resource "aws_ecs_service" "ecs_service" {
#   name            = "krow-service"                            
#   cluster         = "${aws_ecs_cluster.ecs_cluster.id}"             
#   task_definition = "${aws_ecs_task_definition.ecs_task.arn}" 
#   launch_type     = "FARGATE"
#   desired_count   = 1 

#   load_balancer {
#     target_group_arn = var.alb_tg_ecs
#     container_name   = "${aws_ecs_task_definition.ecs_task.family}"
#     container_port   = 3000 
#   }

  # network_configuration {
  #   subnets = [var.private_subnet_id[0].id]
  #   assign_public_ip = true                                                
  #   security_groups  = [var.sg_ecs_task_id] 
  # }
# }

