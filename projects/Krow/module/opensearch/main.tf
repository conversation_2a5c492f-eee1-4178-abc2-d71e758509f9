
data "aws_region" "current" {}

data "aws_caller_identity" "current" {}

resource "random_password" "opensearch_password" {
  length  = 16
  special = true
}

resource "aws_secretsmanager_secret" "opensearch_credentials" {
  name = "amazon-opensearch_password-krow-app"

}

resource "aws_secretsmanager_secret_version" "opensearch_credentials_version" {
  secret_id     = aws_secretsmanager_secret.opensearch_credentials.id
  secret_string = random_password.opensearch_password.result
}

resource "random_password" "amazon-opensearch_password" {
  length = 12
  special = false
}

resource "aws_elasticsearch_domain" "elasticsearch_domain" {
  domain_name           = "krow-opensearch"
  elasticsearch_version = "7.9"

  cluster_config {
    instance_type          = "m6g.xlarge.elasticsearch"
    instance_count         = 1
    zone_awareness_enabled = false
  }

  advanced_security_options {
    enabled                        = true
    internal_user_database_enabled = true
    master_user_options {
      master_user_name     = "krow"
      master_user_password = aws_secretsmanager_secret_version.opensearch_credentials_version.secret_string
    }
  }

  # to make it public, no other way
  # vpc_options {
  #   subnet_ids          = [var.public_subnet_id[0].id]
  #   security_group_ids = [var.opensearch_sg_id]
  # }

  ebs_options {
    ebs_enabled = true
    volume_size = 20
  }

  encrypt_at_rest {
    enabled = true
  }

  node_to_node_encryption {
    enabled = true
  }

  domain_endpoint_options {
    enforce_https       = true
    tls_security_policy = "Policy-Min-TLS-1-0-2019-07"
  }

  auto_tune_options {
    desired_state       = "DISABLED"
    rollback_on_disable = "NO_ROLLBACK"
  }

  access_policies = <<CONFIG
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        
        "Effect": "Allow",
        "Principal": {
          "AWS": "*"
        },
        "Action": "es:*",
        "Resource": "arn:aws:es:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:domain/krow-opensearch/*"
      },
      {
        
        "Effect": "Allow",
        "Principal": {
          "AWS": "*"
        },
        "Action": "logs:*",
        "Resource": "*"
      }
    ]
  }
  CONFIG
  #   access_policies = <<CONFIG
  # {
  #   "Version": "2012-10-17",
  #   "Statement": [
  #     {
  #       "Effect": "Allow",
  #       "Principal": {
  #         "AWS": "*"
  #       },
  #       "Action": "es:*",
  #       "Resource": "arn:aws:es:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:domain/${var.opensearch_domain}/*"
  #     },
  #     {
  #       "Effect": "Allow",
  #       "Principal": {
  #         "AWS": "*"
  #       },
  #       "Action": "logs:*",
  #       "Resource": "*"
  #     }
  #   ]
  # }
  # CONFIG

  tags = merge(
    { Domain = "krow-opensearch" },
  )
}
