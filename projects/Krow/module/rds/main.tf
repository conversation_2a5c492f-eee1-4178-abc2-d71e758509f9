resource "aws_rds_cluster" "smartfind_postgresql_rds_cluster" {
  cluster_identifier     = "krow-app-cluster"
  engine                 = "aurora-postgresql"
  engine_version         = "15.3"
  master_username        = "krow"
  master_password        = aws_secretsmanager_secret_version.amazon-rds-sv.secret_string
  kms_key_id             = aws_kms_key.rds_key.arn
  storage_encrypted      = true
  vpc_security_group_ids = [var.db_sg_id]
  db_subnet_group_name   = aws_db_subnet_group.rds_subnet_group.name
  skip_final_snapshot    = true
}

resource "aws_rds_cluster_instance" "smartfind_postgresql_rds_instance" {
  publicly_accessible    = true 
  apply_immediately      = true
  cluster_identifier     = aws_rds_cluster.smartfind_postgresql_rds_cluster.id
  engine                 = aws_rds_cluster.smartfind_postgresql_rds_cluster.engine
  engine_version         = aws_rds_cluster.smartfind_postgresql_rds_cluster.engine_version
  identifier             = "krow-app-rds-instance"
  instance_class         = "db.t3.medium"
}

resource "aws_db_subnet_group" "rds_subnet_group" {
  name       = "krow-app-vpc-aurora-subnet-group"
  subnet_ids = [var.public_subnet_id[0].id, var.public_subnet_id[1].id]

  tags = merge(
    { Name = "RDS Aurora Subnet Group" }
  )
}

resource "aws_kms_key" "rds_key" {
  description             = "krow app rds key"
  deletion_window_in_days = 30
}

resource "aws_secretsmanager_secret" "amazon-rds_passwordCRED" {
  name = "amazon-rds_passwordCRED-krow-app"
}

resource "aws_secretsmanager_secret_version" "amazon-rds-sv" {
  secret_id     = aws_secretsmanager_secret.amazon-rds_passwordCRED.id
  secret_string = random_password.amazon-rds_passwordCRED.result
}

resource "random_password" "amazon-rds_passwordCRED" {
  length = 12
  special = false
}
