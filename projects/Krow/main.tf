terraform {
  backend "s3" {
    bucket         = "krow-tf-state"
    key            = "krow.tf"
    region         = "eu-central-1"
    dynamodb_table = "krow-tf-state-lock"
  }
}

module "vpc" {
  source = "./module/vpc"
  # Define your VPC configuration here
}


# module "alb" {
#   source = "./module/alb"
#   vpc_id = module.vpc.vpc_id
#   alb_sg_id = module.vpc.alb_sg_id
#   public_subnet_id = module.vpc.public_subnet_id
#   # Define your ALB configuration here
# }

module "ecs" {
  source = "./module/ecs"
  vpc_id = module.vpc.vpc_id
  public_subnet_id = module.vpc.public_subnet_id
  private_subnet_id = module.vpc.private_subnet_id
  sg_ecs_task_id = module.vpc.sg_ecs_task_id
  # alb_sg_id = module.vpc.alb_sg_id
  # alb_tg_ecs = module.alb.alb_tg_ecs

}

module "rds" {
  source = "./module/rds"
  db_sg_id = module.vpc.db_sg_id
  public_subnet_id = module.vpc.public_subnet_id
}

module "elasticache" {
  source                        = "./module/elasticache"
  krow_redis_sg_id              = module.vpc.krow_redis_sg_id
  private_subnet_id             = module.vpc.private_subnet_id
}


module "opensearch" {
  source                        = "./module/opensearch"
  # env                           = var.env
  # region                        = "eu-central-1"
  # project_name                  = "krow-opensearch"
  opensearch_sg_id              = module.vpc.opensearch_sg_id
  public_subnet_id = module.vpc.public_subnet_id
  
 }