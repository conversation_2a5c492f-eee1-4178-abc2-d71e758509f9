resource "aws_cognito_user_pool" "cognito_userpool" {
    name = "${var.project_name}-Pool"

    admin_create_user_config {
        allow_admin_create_user_only = true
    }

    auto_verified_attributes = ["phone_number"]

    schema {
        attribute_data_type = "String"
        developer_only_attribute = false
        mutable = true
        name = "email"
        required = true
        string_attribute_constraints {
            min_length = "0"
            max_length = "2048"
        }
    }

    schema {
        attribute_data_type = "String"
        developer_only_attribute = false
        mutable = true
        name = "phone_number"
        required = true
        string_attribute_constraints {
            min_length = "0"
            max_length = "2048"
        }
    }

    schema {
        attribute_data_type = "String"
        developer_only_attribute = false
        mutable = true
        name = "birthdate"
        required = true
        string_attribute_constraints {
            min_length = "0"
            max_length = "2048"
        }
    }

    schema {
        attribute_data_type = "String"
        developer_only_attribute = false
        mutable = true
        name = "gender"
        required = true
        string_attribute_constraints {
            min_length = "0"
            max_length = "2048"
        }
    }

    schema {
        attribute_data_type = "String"
        developer_only_attribute = false
        mutable = true
        name = "given_name"
        required = true
        string_attribute_constraints {
        min_length = "0"
        max_length = "2048"
        }
    }

    schema {
        attribute_data_type = "String"
        developer_only_attribute = false
        mutable = true
        name = "name"
        required = true
        string_attribute_constraints {
            min_length = "0"
            max_length = "2048"
        }
    }

    schema {
        attribute_data_type = "String"
        developer_only_attribute = false
        mutable = true
        name = "profile"
        required = true
        string_attribute_constraints {
            min_length = "0"
            max_length = "2048"
        }
    }

    schema {
        attribute_data_type = "String"
        developer_only_attribute = false
        mutable = true
        name = "preferred_username"
        required = true
        string_attribute_constraints {
            min_length = "0"
            max_length = "2048"
        }
    }

    password_policy {
        minimum_length    = 8
        require_lowercase = true
        require_uppercase = true
        require_numbers   = true
        require_symbols   = true
        temporary_password_validity_days = 15
    }

    mfa_configuration = "ON"

    sms_configuration {
        external_id = "external-id"
        sns_caller_arn = var.cognito_sms_role_arn
    }

    email_configuration {
        email_sending_account = "COGNITO_DEFAULT"
        from_email_address = "<EMAIL>"
        reply_to_email_address = "<EMAIL>"
        # source_arn =
    }

    verification_message_template {
        default_email_option = "CONFIRM_WITH_CODE"
        email_message_by_link = "Click the link below to verify your email address: {##Verify Email##}"
        email_subject_by_link = "Your verification link"
        sms_message = "Your verification code is {####}"
    }

    tags = {
        Name = "${var.project_name}-Pool"
    }
}

resource "aws_cognito_user_pool_client" "cognito_userpool_client" {
    name         = "${var.project_name}-userPoolClient"
    user_pool_id = aws_cognito_user_pool.cognito_userpool.id


    explicit_auth_flows = [
        "ALLOW_REFRESH_TOKEN_AUTH",
        "ALLOW_USER_SRP_AUTH"
    ]

    refresh_token_validity = 30

    token_validity_units {
        access_token  = "minutes"
        id_token      = "minutes"
        refresh_token = "days"
    }

    access_token_validity  = 60
    id_token_validity      = 60

    prevent_user_existence_errors = "ENABLED"
    enable_token_revocation       = true

    read_attributes = [
        "address",
        "birthdate",
        "email",
        "email_verified",
        "family_name",
        "gender",
        "given_name",
        "locale",
        "middle_name",
        "name",
        "nickname",
        "phone_number",
        "phone_number_verified",
        "picture",
        "preferred_username",
        "profile",
        "updated_at",
        "website",
        "zoneinfo"
    ]

    write_attributes = [
        "address",
        "birthdate",
        "email",
        "family_name",
        "gender",
        "given_name",
        "locale",
        "middle_name",
        "name",
        "nickname",
        "phone_number",
        "picture",
        "preferred_username",
        "profile",
        "website",
        "zoneinfo"
    ]
}
