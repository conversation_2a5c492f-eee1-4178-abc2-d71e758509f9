# Create Parameter Group
resource "aws_docdb_cluster_parameter_group" "docdb_parameter_group" {
    name        = replace(lower("${var.project_name}-docdb-parameter-group"), "/[^a-z0-9-]/", "-")
    family      = "docdb3.6"
    description = replace(lower("${var.project_name} DocumentDB cluster parameter group"), "/[^a-z0-9-]/", "-")

    parameter {
        name  = "tls"
        value = "enabled"
    }
}

# Create Subnet Group
resource "aws_docdb_subnet_group" "docdb_private_subnet_group" {
    name       = replace(lower("${var.project_name}-docdb-private-subnet-group"), "/[^a-z0-9-]/", "-")
    subnet_ids = [for subnet in var.private_subnets : subnet.id]

    tags = {
        Name = replace(lower("${var.project_name}-docdb-private-subnet-group"), "/[^a-z0-9-]/", "-")
    }
}

# Create Cluster
resource "aws_docdb_cluster" "docdb_cluster" {
    cluster_identifier      = "${var.docdb_cluster_identifier}"
    engine                  = "docdb"
    master_username         = "${var.docdb_username}"
    master_password         = "${var.docdb_password}"
    db_subnet_group_name    = aws_docdb_subnet_group.docdb_private_subnet_group.id
    vpc_security_group_ids  = [var.documentdb_sg]
    skip_final_snapshot     = true
    apply_immediately       = true
    kms_key_id              = var.docdb_kms_key_arn
    # cluster_parameter_group_name = aws_docdb_cluster_parameter_group.docdb_parameter_group.id

    tags = {
        Name = replace(lower("${var.project_name}-docdb-cluster"), "/[^a-z0-9-]/", "-")
    }
}

# Create Cluster Instances
resource "aws_docdb_cluster_instance" "docdb_instance" {
    count                 = var.subnet_count
    identifier            = "${var.docdb_cluster_identifier}-${count.index}"
    cluster_identifier    = aws_docdb_cluster.docdb_cluster.id
    instance_class        = "db.r5.large"
    engine                = aws_docdb_cluster.docdb_cluster.engine
    apply_immediately     = true
    # db_subnet_group_name  = aws_docdb_subnet_group.docdb_private_subnet_group.id
    tags = {
        Name = "${var.docdb_cluster_identifier}-${count.index}"
    }
}
