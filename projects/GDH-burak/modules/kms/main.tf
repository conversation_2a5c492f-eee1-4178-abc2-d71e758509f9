resource "aws_kms_key" "s3_kms_key" {
    description              = "KMS key for S3"
    key_usage                = "ENCRYPT_DECRYPT"
    customer_master_key_spec = "SYMMETRIC_DEFAULT"
    enable_key_rotation      = true
    deletion_window_in_days  = 10
}

resource "aws_kms_alias" "alias_s3" {
    name          = "alias/${var.project_name}-s3-key"
    target_key_id = aws_kms_key.s3_kms_key.id
}

# resource "aws_kms_key" "docdb_kms_key" {
#     description              = "KMS key for DocumentDB"
#     key_usage                = "ENCRYPT_DECRYPT"
#     customer_master_key_spec = "SYMMETRIC_DEFAULT"
#     enable_key_rotation      = true
#     deletion_window_in_days  = 10
# }

# resource "aws_kms_alias" "alias_docdb" {
#     name          = "alias/${var.project_name}-docdb-key"
#     target_key_id = aws_kms_key.docdb_kms_key.id
# }



resource "aws_kms_key" "postgres_kms_key" {
    description              = "KMS key for postgres"
    key_usage                = "ENCRYPT_DECRYPT"
    customer_master_key_spec = "SYMMETRIC_DEFAULT"
    enable_key_rotation      = true
    deletion_window_in_days  = 10
}

resource "aws_kms_alias" "alias_postgres" {
    name          = "alias/${var.project_name}-postgres-key"
    target_key_id = aws_kms_key.postgres_kms_key.id
}




# resource "aws_kms_key" "redis_kms_key" {
#     description              = "KMS key for redis"
#     key_usage                = "ENCRYPT_DECRYPT"
#     customer_master_key_spec = "SYMMETRIC_DEFAULT"
#     enable_key_rotation      = true
#     deletion_window_in_days  = 10
# }

# resource "aws_kms_alias" "alias_redis" {
#     name          = "alias/${var.project_name}-redis-key"
#     target_key_id = aws_kms_key.redis_kms_key.id
# }

# resource "aws_kms_key" "mq_kms_key" {
#     description              = "KMS key for MQ"
#     key_usage                = "ENCRYPT_DECRYPT"
#     customer_master_key_spec = "SYMMETRIC_DEFAULT"
#     enable_key_rotation      = true
#     deletion_window_in_days  = 10
# }

# resource "aws_kms_alias" "alias_mq" {
#     name          = "alias/${var.project_name}-mq-key"
#     target_key_id = aws_kms_key.mq_kms_key.id
# }