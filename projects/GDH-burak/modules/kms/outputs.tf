# output "docdb_kms_key_arn" {
#     description = "ARN of the KMS key for DocumentDB"
#     value       = aws_kms_key.docdb_kms_key.arn
# }

output "postgres_kms_key_arn" {
    description = "ARN of the KMS key for postgres"
    value       = aws_kms_key.postgres_kms_key.arn
}

# output "redis_kms_key_arn" {
#     description = "ARN of the KMS key for redis"
#     value       = aws_kms_key.redis_kms_key.arn
# # }

# output "mq_kms_key_arn" {
#     description = "ARN of the KMS key for MQ"
#     value       = aws_kms_key.mq_kms_key.arn
# }

output "s3_kms_key_arn" {
    description = "ARN of the KMS key for S3"
    value       = aws_kms_key.s3_kms_key.arn
}