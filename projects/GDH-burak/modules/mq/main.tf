resource "aws_mq_broker" "mq_broker" {
    auto_minor_version_upgrade = true

    broker_name           = "${var.project_name}-mq-broker"
    deployment_mode       = "ACTIVE_STANDBY_MULTI_AZ"
    engine_type           = "ActiveMQ" # It must be changed
    engine_version        = "5.17.6"
    host_instance_type    = "mq.t3.micro" # It must be changed
    publicly_accessible   = false
    security_groups       = [var.mq_sg]
    storage_type          = "EBS"
    subnet_ids            = [for subnet in var.private_subnets : subnet.id]

    logs {
        audit = true
        general = true
    }

    maintenance_window_start_time {
        day_of_week = "MONDAY"
        time_of_day = "03:00"
        time_zone   = "UTC"
    }

    user {
        username = "${var.mq_username}"
        password = "${var.mq_password}"
    }

    # broker_storage_info {
    #     storage_type = "EBS"
    #     ebs_storage_info {
    #         volume_size = 80
    #     }
    # }

    encryption_options {
        kms_key_id = var.mq_kms_key_arn
        use_aws_owned_key = false
    }

    configuration {
        id       = aws_mq_configuration.mq_configuration.id
        revision = aws_mq_configuration.mq_configuration.latest_revision
    }
}

resource "aws_mq_configuration" "mq_configuration" {
    name          = "${var.project_name}-mq-configuration"
    engine_type   = "ActiveMQ"
    engine_version = "5.15.14"

    data = <<-EOT
        <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
        <broker xmlns="http://activemq.apache.org/schema/core">
            <plugins>
                <statisticsBrokerPlugin/>
            </plugins>
        </broker>
    EOT
}