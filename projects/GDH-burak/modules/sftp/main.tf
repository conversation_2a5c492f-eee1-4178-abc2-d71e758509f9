# Create AWS Transfer Family server
resource "aws_transfer_server" "sftp_server" {
    identity_provider_type = "SERVICE_MANAGED"
    endpoint_type          = "VPC"
    protocols              = ["SFTP"]

    endpoint_details {
        vpc_id             = var.vpc_id
        subnet_ids         = [for subnet in var.public_subnets : subnet.id]
        security_group_ids = [var.sftp_sg]
    }

    logging_role               = var.sftp_transfer_logging_role_arn
    # cloudwatch_log_group_arn   = var.sftp_log_group_arn

    tags = {
        Name = "${var.project_name} SFTP Server"
    }
}

# Create a Transfer Family user
resource "aws_transfer_user" "sftp_transfer_user" {
    server_id = aws_transfer_server.sftp_server.id
    user_name = "${var.project_name}-SFTP-User"
    role      = var.sftp_transfer_user_role_arn

    home_directory = "/example-bucket" # Must be changed
    policy         = data.aws_iam_policy_document.transfer_user_policy.json

    tags = {
        Name = "${var.project_name}-SFTP-User"
    }
}