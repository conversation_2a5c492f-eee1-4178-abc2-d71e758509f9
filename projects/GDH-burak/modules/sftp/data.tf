# Create an IAM policy for the Transfer Family user
data "aws_iam_policy_document" "transfer_user_policy" {
    statement {
        actions = [
            "s3:ListBucket"
        ]

        resources = [
            var.sftp_s3_bucket_arn
        ]
    }

    statement {
        actions = [
            "s3:GetObject",
            "s3:PutObject"
        ]

        resources = [
            "${var.sftp_s3_bucket_arn}/*"
        ]
    }
}