resource "aws_instance" "bastion-host" {
    ami                                  = var.bastion_host_image_id
    associate_public_ip_address          = true # It is necessary to connect bastion host for SSH tunnel
    instance_type                        = "t3.micro"
    # key_name                             = var.bastion_host_keypair
    subnet_id                            = var.public_subnets[0].id
    vpc_security_group_ids               = [var.bastion_host_sg]
  
    root_block_device {
        volume_size = 10
        volume_type = "gp2"
        
        delete_on_termination = false
        encrypted             = true
    }

    tags = {
        Name = "${var.project_name}-bastionhost"
    }
}