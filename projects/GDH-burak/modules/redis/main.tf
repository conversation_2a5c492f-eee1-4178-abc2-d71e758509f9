resource "aws_elasticache_subnet_group" "private_subnet_group" {
    name       = replace(lower("redis-subnet-group-${var.project_name}"), "/[^a-z0-9-]/", "-")
    subnet_ids = [for subnet in var.private_subnets : subnet.id]
}

resource "aws_elasticache_replication_group" "redis_replication" {
    at_rest_encryption_enabled = true
    automatic_failover_enabled = true
    description                = "redis cluster ${var.project_name} for MULTI AZs"
    engine_version             = "7.1"
    kms_key_id                 = var.redis_kms_key_arn
    multi_az_enabled           = true
    node_type                  = "cache.t3.micro"
    parameter_group_name       = replace(lower("${var.project_name}-redis-parametergroup"), "/[^a-z0-9-]/", "-")
    port                       = 6379
    replication_group_id       = replace(lower("${var.project_name}-redis-replication-group"), "/[^a-z0-9-]/", "-")
    security_group_ids         = [var.redis_sg]
    snapshot_retention_limit   = 7
    snapshot_window            = "01:00-03:00"
    subnet_group_name          = aws_elasticache_subnet_group.private_subnet_group.name
    transit_encryption_enabled = false

    replicas_per_node_group    = 1
    num_node_groups            = 1
}
