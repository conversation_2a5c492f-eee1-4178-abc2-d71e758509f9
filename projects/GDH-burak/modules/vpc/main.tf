resource "aws_vpc" "vpc" {
    cidr_block           = var.cidr_block
    enable_dns_hostnames = true
    enable_dns_support   = true
    tags = merge(
        { Name = "${var.project_name}-vpc" }
    )
}

resource "aws_internet_gateway" "igw" {
    vpc_id = aws_vpc.vpc.id
    tags = merge(
        { Name = "${var.project_name}-igw" }
    )
}

resource "aws_subnet" "public_subnets" {
    availability_zone       = "${var.region}${element(var.azs, count.index)}"
    cidr_block              = element(var.public_subnet_cidrs, count.index)
    count                   = var.subnet_count
    vpc_id                  = aws_vpc.vpc.id
    tags = {
        Name = "${var.project_name}-public-subnet-${count.index + 1}"
    }
}

resource "aws_subnet" "private_subnets" {
    availability_zone = "${var.region}${element(var.azs, count.index)}"
    cidr_block        = element(var.private_subnet_cidrs, count.index)
    count             = var.subnet_count
    vpc_id            = aws_vpc.vpc.id
    tags = {
        Name = "${var.project_name}-private-subnet-${count.index + 1}"
    }
}

resource "aws_eip" "eips" {
    count = var.subnet_count
    depends_on = [
        aws_internet_gateway.igw
    ]
    tags = merge(
        { Name = "${var.project_name}-eIP-${count.index + 1}" }
    )
}
resource "aws_nat_gateway" "nat_gateways" {
    allocation_id = aws_eip.eips[count.index].id
    count         = var.subnet_count
    subnet_id     = aws_subnet.public_subnets[count.index].id
    tags = merge(
        { Name = "${var.project_name}-NAT-gateway-${count.index + 1}" }
    )
}

resource "aws_route_table" "public_route_table" {
    vpc_id = aws_vpc.vpc.id
    route {
        cidr_block = "0.0.0.0/0"
        gateway_id = aws_internet_gateway.igw.id
    }
    tags = merge(
        { Name = "${var.project_name}-public-route-table" }
    )
}

resource "aws_route_table" "private_route_tables" {
    count  = var.subnet_count
    vpc_id = aws_vpc.vpc.id
    route {
        cidr_block     = "0.0.0.0/0"
        nat_gateway_id = aws_nat_gateway.nat_gateways[count.index].id
    }
    tags = merge(
        { Name = "${var.project_name}-private-route-table-${count.index + 1}" }
    )
}

resource "aws_route_table_association" "public_table_association" {
    count          = var.subnet_count
    route_table_id = aws_route_table.public_route_table.id
    subnet_id      = aws_subnet.public_subnets[count.index].id
}

resource "aws_route_table_association" "private_table_association" {
    count          = var.subnet_count
    route_table_id = aws_route_table.private_route_tables[count.index].id
    subnet_id      = aws_subnet.private_subnets[count.index].id
}

# Security groups

resource "aws_security_group" "bastion_host_sg" {
    description = "${var.project_name} Bastion sg"
    name        = "${var.project_name}-bastion-host-sg"
    vpc_id      = aws_vpc.vpc.id

    ingress {
        from_port   = 22
        to_port     = 22
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
    }
    egress {
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
    }
    tags = {
        Name = "${var.project_name}-bastion-host-sg"
    }
}

resource "aws_security_group" "control_plane_sg" { # It will be Linux machine
    description = "${var.project_name} Control Plane sg"
    name        = "${var.project_name}-control-plane-sg"
    vpc_id      = aws_vpc.vpc.id

    ingress {
        from_port   = 22
        to_port     = 22
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
    }
    egress {
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
    }
}

# resource "aws_security_group" "documentdb_sg" {
#     description = "${var.project_name}-documentdb sg"
#     name        = "${var.project_name}-documentdb-sg"
#     vpc_id      = aws_vpc.vpc.id
#     ingress {
#         from_port       = 27017
#         to_port         = 27017
#         protocol        = "tcp"
#         security_groups = [aws_security_group.bastion_host_sg.id]
#     }
#     egress {
#         from_port   = 0
#         to_port     = 0
#         protocol    = "-1"
#         cidr_blocks = ["0.0.0.0/0"]
#     }

#     tags = {
#         Name = "${var.project_name}-documentdb-sg"
#     }
# }

# resource "aws_security_group" "mq_sg" {
#     description = "${var.project_name}-mq sg"
#     name        = "${var.project_name}-mq-sg"
#     vpc_id      = aws_vpc.vpc.id
#     ingress {
#         from_port       = 61614
#         to_port         = 61614
#         protocol        = "tcp"
#         security_groups = [aws_security_group.bastion_host_sg.id]
#     }
#     egress {
#         from_port   = 0
#         to_port     = 0
#         protocol    = "-1"
#         cidr_blocks = ["0.0.0.0/0"]
#     }
# }

resource "aws_security_group" "postgresql_sg" {
    description = "${var.project_name}-postgresql sg"
    name        = "${var.project_name}-postgresql-sg"
    vpc_id      = aws_vpc.vpc.id
    ingress {
        from_port       = 5432
        to_port         = 5432
        protocol        = "tcp"
        security_groups = [aws_security_group.bastion_host_sg.id]
    }
    egress {
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
    }
    tags = {
        Name = "${var.project_name}-postgresql-sg"
    }
}

# resource "aws_security_group" "redis_sg" {
#     description = "${var.project_name} Security group for redis"
#     name        = "${var.project_name}-redis-sg"
#     vpc_id      = aws_vpc.vpc.id
#     ingress {
#         description     = "Allow traffic from bastion host"
#         from_port       = 6379
#         to_port         = 6379
#         protocol        = "tcp"
#         security_groups = [aws_security_group.bastion_host_sg.id]
#     }
#     egress {
#         description = "All traffic outbound"
#         from_port   = 0
#         to_port     = 0
#         protocol    = "-1"
#         cidr_blocks = ["0.0.0.0/0"]
#     }
#     tags = {
#         Name = "${var.project_name}-redis-sg"
#     }
# }

# resource "aws_security_group" "sftp_sg" {
#     description = "${var.project_name} Security group for sftp"
#     name        = "${var.project_name}-sftp-sg"
#     vpc_id      = aws_vpc.vpc.id
#     ingress {
#         description     = "Allow traffic from bastion host"
#         from_port       = 22
#         to_port         = 22
#         protocol        = "tcp"
#         security_groups = [aws_security_group.bastion_host_sg.id]
#         cidr_blocks     = ["0.0.0.0/0"]
#     }
#     egress {
#         description = "All traffic outbound"
#         from_port   = 0
#         to_port     = 0
#         protocol    = "-1"
#         cidr_blocks = ["0.0.0.0/0"]
#     }
# }