output "vpc_id" {
    value = aws_vpc.vpc.id
}

output "vpc_cidr_block" {
    value = aws_vpc.vpc.cidr_block
}

output "public_subnets" {
    value = aws_subnet.public_subnets
}

output "private_subnets" {
    value = aws_subnet.private_subnets
}

output "bastion_host_sg" {
    value = aws_security_group.bastion_host_sg.id
}

output "control_plane_sg" {
    value = aws_security_group.control_plane_sg.id
}

# output "documentdb_sg" {
#     value = aws_security_group.documentdb_sg.id
# }

# output "mq_sg" {
#     value = aws_security_group.mq_sg.id
# }

output "postgresql_sg" {
    value = aws_security_group.postgresql_sg.id
}

# output "redis_sg" {
#     value = aws_security_group.redis_sg.id
# }

# output "sftp_sg" {
#     value = aws_security_group.sftp_sg.id
# }