output "cluster_security_group_id" {
    description = "Security group ID for the EKS cluster"
    value       = module.eks_cluster.cluster_security_group_id
}

output "node_security_group_id" {
    description = "Security group ID for the EKS worker nodes"
    value       = module.eks_cluster.node_security_group_id
}

output "eks_prod_oidc_provider_url" {
    description = "The URL of the OIDC provider associated with the EKS cluster"
    value       = module.eks_cluster.oidc_provider
}

output "eks_prod_oidc_provider_arn" {
    description = "The ARN of the OIDC provider associated with the EKS cluster"
    value       = module.eks_cluster.oidc_provider_arn
}