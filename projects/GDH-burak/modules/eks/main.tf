resource "aws_launch_template" "eks_node" {
    name_prefix   = "${var.project_name}EKSNode"
    image_id      = var.eks_node_image_id
    instance_type = "t3.medium" # Must be changed

    tag_specifications {
        resource_type = "instance"
        tags = {
            Name = "${var.cluster_name}EKSNode"
        }
    }
}

module "eks_cluster" {
    source  = "terraform-aws-modules/eks/aws"
    version = "~> 19.15"

    cluster_name                    = "${var.cluster_name}"
    cluster_version                 = "1.30"
    cluster_endpoint_public_access  = true

    iam_role_name            = "EKSClusterRole" 
    iam_role_use_name_prefix = false                           

    subnet_ids = [var.private_subnets[0].id, var.private_subnets[1].id]
    vpc_id     = var.vpc_id

    eks_managed_node_groups = {
        managed = {
            iam_role_name              = "EKSNodeGroupRole"
            iam_role_use_name_prefix   = false                   
            use_custom_launch_template = true
            launch_template_id         = aws_launch_template.eks_node.id
            version                    = "$Latest"
            
            instance_types = ["t3.medium"] # Must be changed

            min_size     = 2
            max_size     = 6
            desired_size = 4

            labels = { 
                Which = "managed"
            }
        }
    }

    tags = {
        Blueprint  = "${var.project_name}EKSCluster"
        GithubRepo = "github.com/aws-ia/terraform-aws-eks-blueprints"
    }
}

module "eks_blueprints_addons" {
    source  = "aws-ia/eks-blueprints-addons/aws"
    version = "~> 1.0"

    cluster_name      = module.eks_cluster.cluster_name
    cluster_endpoint  = module.eks_cluster.cluster_endpoint
    cluster_version   = module.eks_cluster.cluster_version
    oidc_provider_arn = module.eks_cluster.oidc_provider_arn

    eks_addons = {
        aws-ebs-csi-driver = {
            most_recent              = false
            addon_version            = "v1.33.0-eksbuild.1"
            service_account_role_arn = module.ebs_csi_driver_irsa.iam_role_arn
        }
        coredns = {
            most_recent              = false
            addon_version            = "v1.11.1-eksbuild.9" 
        }
        vpc-cni = {
            most_recent              = false
            addon_version            = "v1.18.3-eksbuild.1" 
        }
        kube-proxy = {
            most_recent              = false
            addon_version            = "v1.30.0-eksbuild.3" 
        }
    }
    enable_aws_load_balancer_controller = true
    enable_cluster_autoscaler           = true
}

provider "helm" {
    kubernetes {
        host                   = module.eks_cluster.cluster_endpoint
        cluster_ca_certificate = base64decode(module.eks_cluster.cluster_certificate_authority_data)

        exec {
            api_version = "client.authentication.k8s.io/v1beta1"
            command     = "aws"
            # This requires the awscli to be installed locally where Terraform is executed
            args = ["eks", "get-token", "--cluster-name", module.eks_cluster.cluster_name]
        }
    }
}


module "ebs_csi_driver_irsa" {
    source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
    version = "~> 5.20"

    role_name_prefix = "EBSCSIDriver"

    attach_ebs_csi_policy = true

    oidc_providers = {
        main = {
            provider_arn               = module.eks_cluster.oidc_provider_arn
            namespace_service_accounts = ["kube-system:ebs-csi-controller-sa"]
        }
    }
}