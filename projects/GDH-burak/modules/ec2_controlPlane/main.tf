resource "aws_instance" "control_plane" {
    ami                                  = var.control_plane_image_id
    associate_public_ip_address          = true # It is necessary to connect control plane for SSH tunnel
    subnet_id                            = var.public_subnets[1].id
    # key_name                             = var.control_plane_keypair
    vpc_security_group_ids               = [var.control_plane_sg]
    instance_type                        = "t3.medium" # or t3.small
  
    root_block_device {
        volume_size = 40
        volume_type = "gp2"
        
        delete_on_termination = true
        encrypted             = true
    }

    tags = {
        Name = "${var.project_name}-controlPlane"
    }
}