resource "aws_s3_bucket" "sftp_s3_bucket" {
    bucket        = "${var.project_name}-sftp"
    force_destroy = true

    tags = {
        Name = "${var.project_name}-sftp"
    }
}

resource "aws_s3_bucket_acl" "sftp_s3_bucket_acl" {
    bucket = aws_s3_bucket.sftp_s3_bucket.id
    acl    = "private"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "sftp_s3_bucket_sse" {
    bucket = aws_s3_bucket.sftp_s3_bucket.id

    rule {
        apply_server_side_encryption_by_default {
            kms_master_key_id  = var.s3_kms_key_arn
            sse_algorithm      = "aws:kms"
        }
    }
}