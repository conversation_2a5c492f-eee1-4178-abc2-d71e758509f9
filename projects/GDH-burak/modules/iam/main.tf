# Create an IAM role for SFTP logging
resource "aws_iam_role" "sftp_transfer_logging_role" {
    name = "SFTPTransferLoggingRole"

    assume_role_policy = jsonencode({
        Version = "2012-10-17",
        Statement = [
            {
                Effect = "Allow",
                Principal = {
                    Service = "transfer.amazonaws.com"
                },
                Action = "sts:AssumeRole"
            }
        ]
    })
}

resource "aws_iam_role_policy" "sftp_transfer_logging_policy" {
    name   = "SFTPTransferLoggingPolicy"
    role   = aws_iam_role.sftp_transfer_logging_role.id
    policy = jsonencode({
        Version = "2012-10-17",
        Statement = [
            {
                Effect = "Allow",
                Action = [
                    "logs:CreateLogGroup",
                    "logs:CreateLogStream",
                    "logs:PutLogEvents"
                ],
                Resource = "*"
            }
        ]
    })
}

# Create an IAM role for the Transfer Family user
resource "aws_iam_role" "sftp_transfer_user_role" {
    name = "SFTPTransferUserRole"

    assume_role_policy = jsonencode({
        Version = "2012-10-17",
        Statement = [
            {
                Effect = "Allow",
                Principal = {
                    Service = "transfer.amazonaws.com"
                },
                Action = "sts:AssumeRole"
            }
        ]
    })
}

resource "aws_iam_role_policy" "transfer_user_role_policy" {
    name   = "SFTPTransferUserRolePolicy"
    role   = aws_iam_role.sftp_transfer_user_role.id
    policy = data.aws_iam_policy_document.transfer_user_policy.json
}


# resource "aws_iam_role" "cognito_sms_role" {
#     name = "CognitoSMSRole"

#     assume_role_policy = jsonencode({
#         Version = "2012-10-17",
#         Statement = [
#             {
#                 Action = "sts:AssumeRole",
#                 Effect = "Allow",
#                 Principal = {
#                     Service = "cognito-idp.amazonaws.com"
#                 }
#             }
#         ]
#     })

#     inline_policy {
#         name = "cognito_sms_policy"
#         policy = jsonencode({
#             Version = "2012-10-17",
#             Statement = [
#                 {
#                     Action = [
#                         "sns:Publish"
#                     ],
#                     Effect = "Allow",
#                     Resource = "*"
#                 }
#             ]
#         })
#     }
# }