# module "cognito" {
#     source = "./modules/cognito"

#     cognito_sms_role_arn = module.iam.cognito_sms_role_arn
#     project_name         = var.project_name
# }

# module "documentdb" {
#     source = "./modules/documentdb"

#     docdb_cluster_identifier = var.docdb_cluster_identifier
#     docdb_kms_key_arn        = module.kms.docdb_kms_key_arn
#     docdb_password           = var.docdb_password
#     docdb_username           = var.docdb_username
#     project_name             = var.project_name
#     subnet_count             = var.subnet_count
#     private_subnets          = module.vpc.private_subnets
#     documentdb_sg            = module.vpc.documentdb_sg
# }

# module "mq" {
#     source = "./modules/mq"

#     mq_kms_key_arn  = module.kms.mq_kms_key_arn
#     mq_password     = var.mq_password
#     mq_username     = var.mq_username
#     mq_sg           = module.vpc.mq_sg
#     project_name    = var.project_name
#     private_subnets = module.vpc.private_subnets
# }

# module "redis" {
#     source = "./modules/redis"

#     project_name      = var.project_name
#     private_subnets   = module.vpc.private_subnets
#     redis_kms_key_arn = module.kms.redis_kms_key_arn
#     redis_sg          = module.vpc.redis_sg
# }