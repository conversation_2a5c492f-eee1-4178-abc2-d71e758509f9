# Default variables for the project
variable "region" {
    type        = string
    description = "The deployment region"
    default     = "eu-central-1"
}

variable "subnet_count" {
    type    = number
    default = 2
}

variable "project_name" {
    type    = string
    default = "posteffect"
}

# Bastion variables
variable "bastion_host_image_id" {
    type        = string
    description = "The bastion host image id"
    default     = "ami-00060fac2f8c42d30" # It must be changed
}

# Control Plane variables
variable "control_plane_image_id" {
    type        = string
    description = "The control plane image id"
    default     = "ami-00060fac2f8c42d30" # It must be changed
}

# VPC variables
variable "cidr_block" {
    type        = string
    description = "CIDR range of vpc"
    default     = "10.0.0.0/16"
}

variable "azs" {
    type        = list(string)
    description = "Availability Zones"
    default     = ["a","b"]
}

variable "public_subnet_cidrs" {
    type        = list(string)
    description = "Public Subnet CIDR values"
    default     = ["10.0.0.0/24", "********/24"]
}

variable "private_subnet_cidrs" {
    type        = list(string)
    description = "Private Subnet CIDR values"
    default     = ["********/24", "********/24"]
}






# # MQ variables
# variable "mq_password" {
#     type        = string
#     description = "The mq password"
#     default     = "gdhdigital123!!" # It must be changed
# }

# variable "mq_username" {
#     type        = string
#     description = "The mq username"
#     default     = "gdh_digital" # It must be changed
# }