#!/bin/bash

# # Initialize Terraform
# terraform init
# if [ $? -ne 0 ]; then
#     echo "Terraform init failed"
#     exit 1
# fi

# echo "Terraform init completed successfully"

# # Create Terraform plan and save it to a file
# terraform plan -out=tfplan
# if [ $? -ne 0 ]; then
#     echo "Terraform plan failed"
#     exit 1
# fi

# echo "Terraform plan completed successfully"

# # Apply the saved plan
# terraform apply "tfplan"
# if [ $? -ne 0 ]; then
#     echo "Terraform apply failed"
#     exit 1
# fi

# echo "Terraform apply completed successfully"

# Retrieve the necessary outputs from Terraform
RDS_ENDPOINT=$(terraform output -raw rds_endpoint)
EKS_SECURITY_GROUP=$(terraform output -raw eks_cluster_security_group)
EKS_OIDC_PROVIDER=$(terraform output -raw eks_oidc_provider)

echo "RDS Endpoint: $RDS_ENDPOINT"
echo "EKS Cluster Security Group: $EKS_SECURITY_GROUP"
echo "EKS Cluster OIDC Provider: $EKS_OIDC_PROVIDER"

# # Update kubeconfig to interact with the EKS cluster
# aws eks --region eu-central-1 update-kubeconfig --name <posteffect-cluster>

# # Get the list of pods in the EKS cluster
# kubectl get pods --all-namespaces
