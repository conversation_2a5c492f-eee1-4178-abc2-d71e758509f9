# module "bastion" {
#     source = "./modules/bastion"

#     bastion_host_image_id      = var.bastion_host_image_id
#     project_name               = var.project_name
#     public_subnets             = module.vpc.public_subnets
#     bastion_host_sg            = module.vpc.bastion_host_sg
# }

# module "cloudwatch" {
#     source = "./modules/cloudwatch"

#     project_name = var.project_name
# }

# module "ec2_controlPlane" { # It is used to be like bastion host. It might be not necessary.
#     source = "./modules/ec2_controlPlane"

#     control_plane_image_id = var.control_plane_image_id
#     project_name           = var.project_name
#     public_subnets         = module.vpc.public_subnets
#     control_plane_sg       = module.vpc.control_plane_sg
# }

# module "ecr" { # It is for storing docker images for kubernetes
#     source = "./modules/ecr"
# }

module "eks" {
  source = "./modules/eks"

  cluster_name      = var.cluster_name
  eks_node_image_id = var.eks_node_image_id
  private_subnets   = module.vpc.private_subnets
  project_name      = var.project_name
  vpc_id            = module.vpc.vpc_id
}

# module "iam" {
#     source = "./modules/iam"

#     sftp_s3_bucket_arn  = module.s3.sftp_s3_bucket_arn
# }

module "kms" { # Create s3 and postgres kms keys
  source = "./modules/kms"

  project_name = var.project_name
}

module "postgresql" {
  source = "./modules/postgresql"

  postgres_kms_key_arn = module.kms.postgres_kms_key_arn
  postgresql_db_name   = var.postgresql_db_name
  postgresql_password  = var.postgresql_password
  postgresql_username  = var.postgresql_username
  private_subnets      = module.vpc.private_subnets
  project_name         = var.project_name
  postgresql_sg        = module.vpc.postgresql_sg
}

# module "sftp" {
#     source = "./modules/sftp"

#     project_name                 = var.project_name
#     public_subnets               = module.vpc.public_subnets
#     sftp_s3_bucket_arn           = module.s3.sftp_s3_bucket_arn
#     sftp_sg                      = module.vpc.sftp_sg
#     sftp_transfer_user_role_arn  = module.iam.sftp_transfer_user_role_arn
#     sftp_log_group_arn           = module.cloudwatch.sftp_log_group_arn
#     vpc_id                       = module.vpc.vpc_id

#     sftp_transfer_logging_role_arn  = module.iam.sftp_transfer_logging_role_arn
# }

# module "s3" {
#     source = "./modules/s3"

#     project_name   = var.project_name
#     s3_kms_key_arn = module.kms.s3_kms_key_arn
# }

module "vpc" {
  source = "./modules/vpc"

  azs                  = var.azs
  cidr_block           = var.cidr_block
  private_subnet_cidrs = var.private_subnet_cidrs
  project_name         = var.project_name
  public_subnet_cidrs  = var.public_subnet_cidrs
  region               = var.region
  subnet_count         = var.subnet_count
}
