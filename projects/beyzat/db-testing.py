from sqlalchemy import create_engine, text

# Replace 'saidmustafa' with your macOS username
engine = create_engine(
    'postgresql+psycopg2://saidmustafa@localhost:5432/postgres')

try:
    with engine.connect() as connection:
        # Use `text` to execute raw SQL
        result = connection.execute(text("SELECT 1"))
        print("Connection successful:", result.fetchone())
except Exception as e:
    print("Connection failed:", e)
