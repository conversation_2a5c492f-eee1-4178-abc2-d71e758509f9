import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.sql import text  # For executing raw SQL queries


def execute_query(database, sql_query):
    """
    Executes an SQL query and returns the result as a DataFrame or a success/error message.

    Args:
        database (str): The database to connect to.
        sql_query (str): The SQL query to execute.

    Returns:
        pd.DataFrame or str: Query result as a DataFrame, or a success/error message.
    """
    try:
        # Adjust the database connection string to match your credentials
        engine = create_engine(
            f'postgresql+psycopg2://saidmustafa@localhost:5432/{database}'
        )
        with engine.connect() as connection:
            # Execute the SQL query
            result = connection.execute(text(sql_query))

            # Check if the query returns rows
            if result.returns_rows:
                # Convert result to a Pandas DataFrame
                return pd.DataFrame(result.fetchall(), columns=result.keys())

            # Return a success message for non-SELECT queries
            return "Query executed successfully."
    except SQLAlchemyError as e:
        # Handle SQLAlchemy-specific errors
        return f"Database error: {str(e)}"


def main():
    # Connect to the 'test_db1' database and list all tables in the public schema
    database_name = "test_db1"
    query = """SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';"""

    print(f"Executing Query on Database '{database_name}': {query}")
    result = execute_query(database_name, query)
    if isinstance(result, pd.DataFrame):
        print("Query Result:")
        print(result)
    else:
        print("Result:")
        print(result)


if __name__ == "__main__":
    main()
