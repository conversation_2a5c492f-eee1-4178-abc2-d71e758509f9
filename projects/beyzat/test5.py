import json
import streamlit as st
import boto3
import re
import pandas as pd
from sqlalchemy import create_engine, inspect
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.sql import text

# AWS Bedrock Client Initialization


def bedrock_client():
    try:
        return boto3.client('bedrock-runtime', region_name='us-east-1')
    except Exception as e:
        st.error(f"Failed to initialize AWS Bedrock client: {str(e)}")
        return None

# Function to send query to AWS Bedrock


def query_bedrock(prompt):
    bedrock_runtime = bedrock_client()
    if not bedrock_runtime:
        return "Error: Bedrock client not initialized."

    model_id = "anthropic.claude-3-5-sonnet-20240620-v1:0"

    system_prompt = """
    You are an AI assistant specialized in aiding data analysts for PostgreSQL databases. Your primary responsibility is to interpret user queries, provide PostgreSQL commands based on the user's input, and assist with database analysis. Use the following format:

    - Database Name: `#?#<DATABASE_NAME>DATABASE_NAME_HERE#?#`
    - Schema Name: `#?#<SCHEMA_NAME>SCHEMA_NAME_HERE#?#`
    - SQL Command: `#?#<COMMAND>SQL_COMMAND_HERE#?#`

    If database or schema names are not explicitly mentioned in the query, skip their inclusion.
    """

    template = f"""
    System Prompt: {system_prompt}
    User Query: {prompt}
    """

    payload_body = {
        "anthropic_version": "bedrock-2023-05-31",
        "max_tokens": 1000,
        "messages": [{"role": "user", "content": template.strip()}],
    }

    try:
        response = bedrock_runtime.invoke_model(
            modelId=model_id,
            body=json.dumps(payload_body),
            contentType="application/json",
            accept="application/json",
        )
        response_body = json.loads(response["body"].read().decode("utf-8"))
        content_list = response_body.get("content", [])
        return content_list[0].get("text", "No response generated.") if content_list else "No response generated."
    except Exception as e:
        return f"An error occurred while querying Bedrock: {str(e)}"

# Function to extract SQL command


def extract_sql_command(response):
    match = re.search(r'#\?#<COMMAND>(.*?)#\?#', response, re.DOTALL)
    return match.group(1).strip() if match else None

# Function to gather table metadata


def get_table_metadata(database):
    try:
        engine = create_engine(
            f'postgresql+psycopg2://saidmustafa@localhost:5432/{database}'
        )
        inspector = inspect(engine)

        metadata_info = []
        for schema in inspector.get_schema_names():
            for table_name in inspector.get_table_names(schema=schema):
                columns = inspector.get_columns(table_name, schema=schema)
                primary_keys = inspector.get_pk_constraint(
                    table_name, schema=schema).get('constrained_columns', [])
                foreign_keys = inspector.get_foreign_keys(
                    table_name, schema=schema)
                indexes = inspector.get_indexes(table_name, schema=schema)

                table_info = {
                    "schema": schema,
                    "table_name": table_name,
                    "columns": [col['name'] for col in columns],
                    "primary_keys": primary_keys,
                    "foreign_keys": [
                        {"column": fk['constrained_columns'], "references": fk['referred_table']} for fk in foreign_keys
                    ],
                    "indexes": indexes
                }
                metadata_info.append(table_info)

        return metadata_info
    except SQLAlchemyError as e:
        return f"Database error while fetching metadata: {str(e)}"

# Function to format table metadata into a string


def format_metadata(metadata):
    if isinstance(metadata, str):
        return metadata

    formatted = "\n".join(
        [
            f"Schema: {table['schema']}\nTable: {table['table_name']}\nColumns: {', '.join(table['columns'])}\n" +
            f"Primary Keys: {', '.join(table['primary_keys']) if table['primary_keys'] else 'None'}\n" +
            f"Foreign Keys: {', '.join([f"{fk['column']} -> {fk['references']}" for fk in table['foreign_keys']]) if table['foreign_keys'] else 'None'}\n" +
            f"Indexes: {', '.join(
                [index['name'] for index in table['indexes']]) if table['indexes'] else 'None'}\n"
            for table in metadata
        ]
    )
    return formatted

# Function to execute SQL query


def execute_query(database, sql_query):
    try:
        connection_string = f'postgresql+psycopg2://saidmustafa@localhost:5432/{
            database}' if database else 'postgresql+psycopg2://saidmustafa@localhost:5432/postgres'
        engine = create_engine(connection_string)
        with engine.connect() as connection:
            result = connection.execute(text(sql_query))
            if result.returns_rows:
                return pd.DataFrame(result.fetchall(), columns=result.keys())
            return "Query executed successfully."
    except SQLAlchemyError as e:
        return f"Database error while executing query: {str(e)}"

# Function to extract database name from response


def extract_database_name(response):
    match = re.search(r'#\?#<DATABASE_NAME>(.*?)#\?#', response, re.DOTALL)
    return match.group(1).strip() if match else None


# Streamlit UI Setup
st.title("Data Analyst AI Assistant (DAN)")

# Sidebar for chat history
st.sidebar.title("Chat History")
if 'history' not in st.session_state:
    st.session_state['history'] = []


def update_history(user, sql_query, result):
    # If the result is a DataFrame, store it directly; otherwise, leave as is
    if isinstance(result, pd.DataFrame):
        st.session_state['history'].append(
            {"user": user, "sql_query": sql_query, "result": result}
        )
    else:
        st.session_state['history'].append(
            {"user": user, "sql_query": sql_query, "result": result}
        )


def display_chat_history():
    for chat in st.session_state['history']:
        st.markdown(f"**User Query:** {chat['user']}")
        st.markdown(f"**SQL Command:** `{chat['sql_query']}`")

        # Handle results
        if isinstance(chat['result'], str) and chat['result'].startswith('{'):
            try:
                # Parse the result as a DataFrame from JSON-like string
                result_df = pd.read_json(chat['result'])
                st.table(result_df)  # Render as a table
            except ValueError:
                st.markdown(f"**Result:** {chat['result']}")
        elif isinstance(chat['result'], pd.DataFrame):
            st.table(chat['result'])  # Directly render DataFrame as a table
        else:
            st.markdown(f"**Result:** {chat['result']}")
        st.markdown("---")


st.subheader("Chat")
user_input = st.text_area("Ask the AI (as a data analyst):", key="user_input")
if st.button("Submit") and user_input:

    # Query the AI model
    model_response = query_bedrock(user_input)

    # Extract the database name from the response
    database_name = extract_database_name(model_response)

    if database_name:
        # Gather table metadata for the extracted database
        table_metadata = get_table_metadata(database_name)
        formatted_metadata = format_metadata(table_metadata)

        # Append metadata to the user's query
        augmented_prompt = f"{user_input}\n\nDatabase Metadata:\n{
            formatted_metadata}"

        # Query the AI model again with metadata
        augmented_response = query_bedrock(augmented_prompt)

        # Extract the SQL command from the response
        sql_query = extract_sql_command(augmented_response)
        if sql_query:

            # Execute the SQL query on the database
            result = execute_query(database_name, sql_query)

            if isinstance(result, pd.DataFrame):
                st.write(result)
                update_history(user_input, sql_query, result.to_json())
            else:
                st.error(result)
                update_history(user_input, sql_query, result)
        else:
            st.error("No valid SQL command detected.")
    else:
        # Execute SQL even if no database name is found
        sql_query = extract_sql_command(model_response)
        if sql_query:

            # Execute the SQL query on the default database
            result = execute_query(None, sql_query)

            if isinstance(result, pd.DataFrame):
                st.write(result)
                update_history(user_input, sql_query, result.to_json())
            else:
                st.error(result)
                update_history(user_input, sql_query, result)
        else:
            st.error("No valid SQL command detected.")

# Display chat history
with st.sidebar:
    display_chat_history()

# Display Result
st.subheader("Result")
if 'result' in st.session_state:
    st.write(st.session_state['result'])
