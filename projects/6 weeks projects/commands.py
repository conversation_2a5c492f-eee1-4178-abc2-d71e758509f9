# import streamlit as st

# # Set the layout to wide to use the full width of the browser window
# st.set_page_config(layout="wide")

# # Define a function to display commands with execution buttons


# def display_commands_with_execution(commands):
#     for idx, command in enumerate(commands):
#         col1, col2 = st.columns([4, 1])
#         with col1:
#             st.code(command, language='bash')
#         with col2:
            # if st.button(f"Execute", key=f"execute_{idx}"):
            #     st.success(f"Executed: {command}")


# # Sample list of CLI commands
# cli_commands = [
#     "pip install streamlit",
#     "streamlit run app.py",
#     "conda install -c conda-forge numpy",
# ]

# # Create a two-column layout with a 4:2 ratio
# col1, col2 = st.columns([4, 2])

# # Left column with other content
# with col1:
#     st.title("Welcome to Streamlit")
#     st.write("This is the left column with additional content.")
#     st.text_input("Enter your name:")
#     st.slider("Select a number:", 1, 100, 50)

# # Right column with CLI commands
# with col2:
#     st.title("CLI Commands with Execution")
#     st.write("Click the 'Execute' button to simulate command execution.")
#     display_commands_with_execution(cli_commands)
