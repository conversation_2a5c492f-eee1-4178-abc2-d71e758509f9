import streamlit as st
from datetime import datetime
import time as t
import backend  # Import backend functions and helpers
import cli_executor  # Import CLI executor functions

# Constants and Initial Configurations
MAX_API_CALLS = 50
RESPONSE_DELAY = 0.05  # Delay between words in the response generator

# API Call Count Management Functions


def get_call_count():
    """Get the current API call count from session state."""
    if "api_call_count" not in st.session_state:
        st.session_state.api_call_count = 0
    return st.session_state.api_call_count


def increment_call_count():
    """Increment the API call count in session state."""
    st.session_state.api_call_count += 1


def reset_call_count():
    """Reset the API call count in session state."""
    st.session_state.api_call_count = 0

# Response Generator


def response_generator(response):
    """Generate words from the response with a slight delay between each."""
    sentences = response.split("\n\n")
    for sentence in sentences:
        words = sentence.split()
        for word in words:
            yield word + " "
            t.sleep(RESPONSE_DELAY)
        yield "\n\n"

# Handle AI Response



def handle_ai_response(response, session_name):
    """Process and display the AI response, including handling CLI commands."""
    with st.chat_message("ai"):
        st.write_stream(response_generator(response))

    st.session_state.messages.append({"role": "ai", "content": response})
    backend.save_conversation(session_name)

    if cli_executor.contains_double_ampersand(response):
        cli_results = cli_executor.process_and_get_results(response)
        st.session_state.messages.append(
            {"role": "aws", "content": cli_results})

        with st.chat_message("aws"):
            st.markdown(cli_results)

        with st.spinner("Processing CLI results..."):
            additional_response = backend.gen(
                history=st.session_state.messages,
                prompt=(
                    "I am AWS: If you encounter any errors, avoid reusing the same commands. "
                    "Use &^ ^& to include any AWS CLI commands you want to run. "
                    "Otherwise, do not use CLI syntax in your input. "
                    "Here's what I got from the AWS CLI: {cli_results}"
                ),
                prompter="AWS"
            )

        with st.chat_message("ai"):
            st.markdown(additional_response)

        st.session_state.messages.append(
            {"role": "ai", "content": additional_response})
        backend.save_conversation(session_name)

        if cli_executor.contains_double_ampersand(additional_response):
            handle_ai_response(additional_response, session_name)

# Main function for the Streamlit app


def main():
    initialize_session_state()
    st.sidebar.title("AWS Gen.ai")

    if st.sidebar.button("Logout"):
        backend.clear_session()
        reset_call_count()

    if "session_name" not in st.session_state:
        st.session_state["session_name"] = datetime.now()

    session_name = st.session_state["session_name"]
    st.sidebar.title(session_name)

    if get_call_count() >= MAX_API_CALLS:
        st.sidebar.warning("Maximum API calls reached.")
        return

    if "messages" not in st.session_state:
        st.session_state.messages = []

    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    prompt = st.chat_input("What's up?")

    if prompt:
        st.session_state.messages.append({"role": "user", "content": prompt})

        with st.chat_message("user"):
            st.markdown(prompt)

        increment_call_count()
        with st.spinner("Generating..."):
            response = backend.gen(
                history=st.session_state.messages,
                prompt=prompt,
                prompter="USER"
            )

        handle_ai_response(response, session_name)


if __name__ == "__main__":
    main()
