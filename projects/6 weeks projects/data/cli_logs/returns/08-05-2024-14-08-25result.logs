[
    {
        "command": "aws ec2 describe-vpcs --region default",
        "status": "failure",
        "error": "Could not connect to the endpoint URL: \"https://ec2.default.amazonaws.com/\""
    }
]
[
    {
        "command": "aws ec2 describe-vpcs --region us-east-1",
        "status": "success",
        "output": "{\n    \"Vpcs\": [\n        {\n            \"CidrBlock\": \"10.0.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-0a4055cb94e71739b\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-0250b256730918973\",\n                    \"CidrBlock\": \"10.0.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": false\n        },\n        {\n            \"CidrBlock\": \"172.31.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-db9c3db4\",\n                    \"CidrBlock\": \"172.31.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": true,\n            \"Tags\": [\n                {\n                    \"Key\": \"Name\",\n                    \"Value\": \"Default VPC\"\n                }\n            ]\n        },\n        {\n            \"CidrBlock\": \"10.0.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-0bff8d6ffe6219cf8\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-05dae0f62f0e0614d\",\n                    \"CidrBlock\": \"10.0.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": false,\n            \"Tags\": [\n                {\n                    \"Key\": \"department\",\n                    \"Value\": \"Menemen\"\n                }\n            ]\n        }\n    ]\n}"
    }
]
[
    {
        "command": "aws ec2 create-key-pair --key-name skyloop-key --query 'KeyMaterial' --output text > skyloop-key.pem",
        "status": "success",
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
]
