[{"command": "aws s3 ls", "status": "success", "output": "2023-05-02 16:18:14 aa-textract-in\n2023-05-03 09:48:57 aa-textract-layer\n2023-05-02 16:19:28 aa-textract-output\n2023-05-12 10:20:56 aawtextract\n2024-05-08 10:24:06 amazon-connect-e5deb1b8170d\n2023-01-02 15:10:05 cf-templates-a7141j0doj8x-eu-central-1\n2023-05-17 11:54:48 cf-templates-a7141j0doj8x-us-west-1\n2022-07-27 14:07:24 cf-templates-a7141j0doj8x-us-west-2\n2022-02-08 14:00:24 conductor-cloudformationbucket-1m8jn3ze4mwkt\n2022-02-08 14:00:25 conductor-websitebucket-1dynoqfcji7ce\n2022-12-07 17:29:10 config-bucket-438162393827\n2021-09-24 13:17:36 cw-syn-results-438162393827-us-east-1\n2023-11-03 11:53:26 ekohesap\n2022-06-07 20:12:18 eksanywhereskyloop\n2022-12-03 19:14:55 election-438162393827\n2023-03-07 14:26:07 isbi-giris-karti\n2022-01-26 06:49:26 kopiax\n2022-03-11 16:36:26 kopiax-web\n2023-09-12 15:25:11 kris-deneme-bucket\n2024-05-13 11:09:26 makbulut-sightverse-attemp\n2024-02-06 11:51:44 mongo-ps\n2022-07-14 21:32:02 pipeline-bucket-sec-compliance-438162393827\n2022-12-20 10:48:39 rekognition-video-console-demo-fra-438162393827-1671522514\n2023-03-30 11:40:14 rekognition-video-console-demo-iad-438162393827-1680165593\n2024-02-15 11:56:35 skyloop-logo\n2021-01-27 05:26:25 skyloop.cloud\n2023-05-03 10:32:21 test-textract-lambdaframeworks\n2023-12-25 14:20:07 textract-image-input-test\n2023-05-25 13:35:51 textract-queries-bucket-2\n2023-01-03 11:07:14 tf-state-skyloop\n2023-03-27 13:32:36 tkk-test\n2023-02-21 14:11:36 tuzla-acil-apk\n2021-01-04 20:05:04 www.skyloop.cloud"}, {"command": "aws s3 ls", "status": "success", "output": "2023-05-02 16:18:14 aa-textract-in\n2023-05-03 09:48:57 aa-textract-layer\n2023-05-02 16:19:28 aa-textract-output\n2023-05-12 10:20:56 aawtextract\n2024-05-08 10:24:06 amazon-connect-e5deb1b8170d\n2023-01-02 15:10:05 cf-templates-a7141j0doj8x-eu-central-1\n2023-05-17 11:54:48 cf-templates-a7141j0doj8x-us-west-1\n2022-07-27 14:07:24 cf-templates-a7141j0doj8x-us-west-2\n2022-02-08 14:00:24 conductor-cloudformationbucket-1m8jn3ze4mwkt\n2022-02-08 14:00:25 conductor-websitebucket-1dynoqfcji7ce\n2022-12-07 17:29:10 config-bucket-438162393827\n2021-09-24 13:17:36 cw-syn-results-438162393827-us-east-1\n2023-11-03 11:53:26 ekohesap\n2022-06-07 20:12:18 eksanywhereskyloop\n2022-12-03 19:14:55 election-438162393827\n2023-03-07 14:26:07 isbi-giris-karti\n2022-01-26 06:49:26 kopiax\n2022-03-11 16:36:26 kopiax-web\n2023-09-12 15:25:11 kris-deneme-bucket\n2024-05-13 11:09:26 makbulut-sightverse-attemp\n2024-02-06 11:51:44 mongo-ps\n2022-07-14 21:32:02 pipeline-bucket-sec-compliance-438162393827\n2022-12-20 10:48:39 rekognition-video-console-demo-fra-438162393827-1671522514\n2023-03-30 11:40:14 rekognition-video-console-demo-iad-438162393827-1680165593\n2024-02-15 11:56:35 skyloop-logo\n2021-01-27 05:26:25 skyloop.cloud\n2023-05-03 10:32:21 test-textract-lambdaframeworks\n2023-12-25 14:20:07 textract-image-input-test\n2023-05-25 13:35:51 textract-queries-bucket-2\n2023-01-03 11:07:14 tf-state-skyloop\n2023-03-27 13:32:36 tkk-test\n2023-02-21 14:11:36 tuzla-acil-apk\n2021-01-04 20:05:04 www.skyloop.cloud"}, {"command": "aws s3 ls", "status": "success", "output": "2023-05-02 16:18:14 aa-textract-in\n2023-05-03 09:48:57 aa-textract-layer\n2023-05-02 16:19:28 aa-textract-output\n2023-05-12 10:20:56 aawtextract\n2024-05-08 10:24:06 amazon-connect-e5deb1b8170d\n2023-01-02 15:10:05 cf-templates-a7141j0doj8x-eu-central-1\n2023-05-17 11:54:48 cf-templates-a7141j0doj8x-us-west-1\n2022-07-27 14:07:24 cf-templates-a7141j0doj8x-us-west-2\n2022-02-08 14:00:24 conductor-cloudformationbucket-1m8jn3ze4mwkt\n2022-02-08 14:00:25 conductor-websitebucket-1dynoqfcji7ce\n2022-12-07 17:29:10 config-bucket-438162393827\n2021-09-24 13:17:36 cw-syn-results-438162393827-us-east-1\n2023-11-03 11:53:26 ekohesap\n2022-06-07 20:12:18 eksanywhereskyloop\n2022-12-03 19:14:55 election-438162393827\n2023-03-07 14:26:07 isbi-giris-karti\n2022-01-26 06:49:26 kopiax\n2022-03-11 16:36:26 kopiax-web\n2023-09-12 15:25:11 kris-deneme-bucket\n2024-05-13 11:09:26 makbulut-sightverse-attemp\n2024-02-06 11:51:44 mongo-ps\n2022-07-14 21:32:02 pipeline-bucket-sec-compliance-438162393827\n2022-12-20 10:48:39 rekognition-video-console-demo-fra-438162393827-1671522514\n2023-03-30 11:40:14 rekognition-video-console-demo-iad-438162393827-1680165593\n2024-02-15 11:56:35 skyloop-logo\n2021-01-27 05:26:25 skyloop.cloud\n2023-05-03 10:32:21 test-textract-lambdaframeworks\n2023-12-25 14:20:07 textract-image-input-test\n2023-05-25 13:35:51 textract-queries-bucket-2\n2023-01-03 11:07:14 tf-state-skyloop\n2023-03-27 13:32:36 tkk-test\n2023-02-21 14:11:36 tuzla-acil-apk\n2021-01-04 20:05:04 www.skyloop.cloud"}, {"command": "aws s3 ls", "status": "success", "output": "2023-05-02 16:18:14 aa-textract-in\n2023-05-03 09:48:57 aa-textract-layer\n2023-05-02 16:19:28 aa-textract-output\n2023-05-12 10:20:56 aawtextract\n2024-05-08 10:24:06 amazon-connect-e5deb1b8170d\n2023-01-02 15:10:05 cf-templates-a7141j0doj8x-eu-central-1\n2023-05-17 11:54:48 cf-templates-a7141j0doj8x-us-west-1\n2022-07-27 14:07:24 cf-templates-a7141j0doj8x-us-west-2\n2022-02-08 14:00:24 conductor-cloudformationbucket-1m8jn3ze4mwkt\n2022-02-08 14:00:25 conductor-websitebucket-1dynoqfcji7ce\n2022-12-07 17:29:10 config-bucket-438162393827\n2021-09-24 13:17:36 cw-syn-results-438162393827-us-east-1\n2023-11-03 11:53:26 ekohesap\n2022-06-07 20:12:18 eksanywhereskyloop\n2022-12-03 19:14:55 election-438162393827\n2023-03-07 14:26:07 isbi-giris-karti\n2022-01-26 06:49:26 kopiax\n2022-03-11 16:36:26 kopiax-web\n2023-09-12 15:25:11 kris-deneme-bucket\n2024-05-13 11:09:26 makbulut-sightverse-attemp\n2024-02-06 11:51:44 mongo-ps\n2022-07-14 21:32:02 pipeline-bucket-sec-compliance-438162393827\n2022-12-20 10:48:39 rekognition-video-console-demo-fra-438162393827-1671522514\n2023-03-30 11:40:14 rekognition-video-console-demo-iad-438162393827-1680165593\n2024-02-15 11:56:35 skyloop-logo\n2021-01-27 05:26:25 skyloop.cloud\n2023-05-03 10:32:21 test-textract-lambdaframeworks\n2023-12-25 14:20:07 textract-image-input-test\n2023-05-25 13:35:51 textract-queries-bucket-2\n2023-01-03 11:07:14 tf-state-skyloop\n2023-03-27 13:32:36 tkk-test\n2023-02-21 14:11:36 tuzla-acil-apk\n2021-01-04 20:05:04 www.skyloop.cloud"}]