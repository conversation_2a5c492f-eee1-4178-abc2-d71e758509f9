[
    {
        "command": "aws ec2 describe-vpcs",
        "status": "success",
        "output": "{\n    \"Vpcs\": [\n        {\n            \"CidrBlock\": \"10.0.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-0a4055cb94e71739b\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-0250b256730918973\",\n                    \"CidrBlock\": \"10.0.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": false\n        },\n        {\n            \"CidrBlock\": \"172.31.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-db9c3db4\",\n                    \"CidrBlock\": \"172.31.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": true,\n            \"Tags\": [\n                {\n                    \"Key\": \"Name\",\n                    \"Value\": \"Default VPC\"\n                }\n            ]\n        },\n        {\n            \"CidrBlock\": \"10.0.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-0bff8d6ffe6219cf8\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-05dae0f62f0e0614d\",\n                    \"CidrBlock\": \"10.0.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": false,\n            \"Tags\": [\n                {\n                    \"Key\": \"department\",\n                    \"Value\": \"Menemen\"\n                }\n            ]\n        }\n    ]\n}"
    }
]
[
    {
        "command": "aws ec2 describe-vpcs --region us-east-1",
        "status": "success",
        "output": "{\n    \"Vpcs\": [\n        {\n            \"CidrBlock\": \"10.0.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-0a4055cb94e71739b\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-0250b256730918973\",\n                    \"CidrBlock\": \"10.0.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": false\n        },\n        {\n            \"CidrBlock\": \"172.31.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-db9c3db4\",\n                    \"CidrBlock\": \"172.31.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": true,\n            \"Tags\": [\n                {\n                    \"Key\": \"Name\",\n                    \"Value\": \"Default VPC\"\n                }\n            ]\n        },\n        {\n            \"CidrBlock\": \"10.0.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-0bff8d6ffe6219cf8\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-05dae0f62f0e0614d\",\n                    \"CidrBlock\": \"10.0.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": false,\n            \"Tags\": [\n                {\n                    \"Key\": \"department\",\n                    \"Value\": \"Menemen\"\n                }\n            ]\n        }\n    ]\n}"
    }
]
[
    {
        "command": "aws ec2 create-vpc --cidr-block 10.0.0.0/16 --region us-east-1",
        "status": "success",
        "output": "{\n    \"Vpc\": {\n        \"CidrBlock\": \"10.0.0.0/16\",\n        \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n        \"State\": \"pending\",\n        \"VpcId\": \"vpc-0ca13574e3c79b7cb\",\n        \"OwnerId\": \"438162393827\",\n        \"InstanceTenancy\": \"default\",\n        \"Ipv6CidrBlockAssociationSet\": [],\n        \"CidrBlockAssociationSet\": [\n            {\n                \"AssociationId\": \"vpc-cidr-assoc-01bfae65b0f467fa7\",\n                \"CidrBlock\": \"10.0.0.0/16\",\n                \"CidrBlockState\": {\n                    \"State\": \"associated\"\n                }\n            }\n        ],\n        \"IsDefault\": false\n    }\n}"
    },
    {
        "command": "aws ec2 create-subnet --vpc-id vpc-0a4055cb94e71739b --cidr-block 10.0.0.0/24 --availability-zone us-east-1a --region us-east-1",
        "status": "success",
        "output": "{\n    \"Subnet\": {\n        \"AvailabilityZone\": \"us-east-1a\",\n        \"AvailabilityZoneId\": \"use1-az2\",\n        \"AvailableIpAddressCount\": 251,\n        \"CidrBlock\": \"10.0.0.0/24\",\n        \"DefaultForAz\": false,\n        \"MapPublicIpOnLaunch\": false,\n        \"State\": \"available\",\n        \"SubnetId\": \"subnet-038fbc6a1c74db9d3\",\n        \"VpcId\": \"vpc-0a4055cb94e71739b\",\n        \"OwnerId\": \"438162393827\",\n        \"AssignIpv6AddressOnCreation\": false,\n        \"Ipv6CidrBlockAssociationSet\": [],\n        \"SubnetArn\": \"arn:aws:ec2:us-east-1:438162393827:subnet/subnet-038fbc6a1c74db9d3\",\n        \"EnableDns64\": false,\n        \"Ipv6Native\": false,\n        \"PrivateDnsNameOptionsOnLaunch\": {\n            \"HostnameType\": \"ip-name\",\n            \"EnableResourceNameDnsARecord\": false,\n            \"EnableResourceNameDnsAAAARecord\": false\n        }\n    }\n}"
    },
    {
        "command": "aws ec2 create-subnet --vpc-id vpc-0a4055cb94e71739b --cidr-block ********/24 --availability-zone us-east-1b --region us-east-1",
        "status": "success",
        "output": "{\n    \"Subnet\": {\n        \"AvailabilityZone\": \"us-east-1b\",\n        \"AvailabilityZoneId\": \"use1-az4\",\n        \"AvailableIpAddressCount\": 251,\n        \"CidrBlock\": \"********/24\",\n        \"DefaultForAz\": false,\n        \"MapPublicIpOnLaunch\": false,\n        \"State\": \"available\",\n        \"SubnetId\": \"subnet-01e68131e755ba0e0\",\n        \"VpcId\": \"vpc-0a4055cb94e71739b\",\n        \"OwnerId\": \"438162393827\",\n        \"AssignIpv6AddressOnCreation\": false,\n        \"Ipv6CidrBlockAssociationSet\": [],\n        \"SubnetArn\": \"arn:aws:ec2:us-east-1:438162393827:subnet/subnet-01e68131e755ba0e0\",\n        \"EnableDns64\": false,\n        \"Ipv6Native\": false,\n        \"PrivateDnsNameOptionsOnLaunch\": {\n            \"HostnameType\": \"ip-name\",\n            \"EnableResourceNameDnsARecord\": false,\n            \"EnableResourceNameDnsAAAARecord\": false\n        }\n    }\n}"
    },
    {
        "command": "aws ec2 create-subnet --vpc-id vpc-0a4055cb94e71739b --cidr-block ********/24 --availability-zone us-east-1a --region us-east-1",
        "status": "success",
        "output": "{\n    \"Subnet\": {\n        \"AvailabilityZone\": \"us-east-1a\",\n        \"AvailabilityZoneId\": \"use1-az2\",\n        \"AvailableIpAddressCount\": 251,\n        \"CidrBlock\": \"********/24\",\n        \"DefaultForAz\": false,\n        \"MapPublicIpOnLaunch\": false,\n        \"State\": \"available\",\n        \"SubnetId\": \"subnet-07478e276c7c7ddcc\",\n        \"VpcId\": \"vpc-0a4055cb94e71739b\",\n        \"OwnerId\": \"438162393827\",\n        \"AssignIpv6AddressOnCreation\": false,\n        \"Ipv6CidrBlockAssociationSet\": [],\n        \"SubnetArn\": \"arn:aws:ec2:us-east-1:438162393827:subnet/subnet-07478e276c7c7ddcc\",\n        \"EnableDns64\": false,\n        \"Ipv6Native\": false,\n        \"PrivateDnsNameOptionsOnLaunch\": {\n            \"HostnameType\": \"ip-name\",\n            \"EnableResourceNameDnsARecord\": false,\n            \"EnableResourceNameDnsAAAARecord\": false\n        }\n    }\n}"
    },
    {
        "command": "aws ec2 create-subnet --vpc-id vpc-0a4055cb94e71739b --cidr-block ********/24 --availability-zone us-east-1b --region us-east-1",
        "status": "success",
        "output": "{\n    \"Subnet\": {\n        \"AvailabilityZone\": \"us-east-1b\",\n        \"AvailabilityZoneId\": \"use1-az4\",\n        \"AvailableIpAddressCount\": 251,\n        \"CidrBlock\": \"********/24\",\n        \"DefaultForAz\": false,\n        \"MapPublicIpOnLaunch\": false,\n        \"State\": \"available\",\n        \"SubnetId\": \"subnet-0a8e8e86714b0348c\",\n        \"VpcId\": \"vpc-0a4055cb94e71739b\",\n        \"OwnerId\": \"438162393827\",\n        \"AssignIpv6AddressOnCreation\": false,\n        \"Ipv6CidrBlockAssociationSet\": [],\n        \"SubnetArn\": \"arn:aws:ec2:us-east-1:438162393827:subnet/subnet-0a8e8e86714b0348c\",\n        \"EnableDns64\": false,\n        \"Ipv6Native\": false,\n        \"PrivateDnsNameOptionsOnLaunch\": {\n            \"HostnameType\": \"ip-name\",\n            \"EnableResourceNameDnsARecord\": false,\n            \"EnableResourceNameDnsAAAARecord\": false\n        }\n    }\n}"
    },
    {
        "command": "aws eks create-cluster --name my-eks-cluster --region us-east-1 --vpc-configuration subnetIds=subnet-0a8f2f31c6d13785f,subnet-0d6aa91a7d1c55a74,subnet-0b4c09bfda421d8e1,subnet-0e4a9c4ecc5ac8ca3,securityGroupIds=sg-054e8cfe6e2214b08",
        "status": "failure",
        "error": "usage: aws [options] <command> <subcommand> [<subcommand> ...] [parameters]\nTo see help text, you can run:\n\n  aws help\n  aws <command> help\n  aws <command> <subcommand> help\n\naws: error: the following arguments are required: --role-arn, --resources-vpc-config"
    }
]
