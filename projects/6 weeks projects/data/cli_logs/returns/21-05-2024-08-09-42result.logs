[
    {
        "command": "aws ec2 describe-vpcs --region us-east-1",
        "status": "success",
        "output": "{\n    \"Vpcs\": [\n        {\n            \"CidrBlock\": \"10.0.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-0a4055cb94e71739b\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-0250b256730918973\",\n                    \"CidrBlock\": \"10.0.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": false\n        },\n        {\n            \"CidrBlock\": \"172.31.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-db9c3db4\",\n                    \"CidrBlock\": \"172.31.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": true,\n            \"Tags\": [\n                {\n                    \"Key\": \"Name\",\n                    \"Value\": \"Default VPC\"\n                }\n            ]\n        },\n        {\n            \"CidrBlock\": \"10.0.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-0bff8d6ffe6219cf8\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-05dae0f62f0e0614d\",\n                    \"CidrBlock\": \"10.0.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": false,\n            \"Tags\": [\n                {\n                    \"Key\": \"department\",\n                    \"Value\": \"Menemen\"\n                }\n            ]\n        }\n    ]\n}"
    }
]
[
    {
        "command": "aws ec2 describe-instances --region us-east-1",
        "status": "success",
        "output": "{\n    \"Reservations\": []\n}"
    }
]
[
    {
        "command": "aws ec2 describe-instances --region us-west-2",
        "status": "success",
        "output": "{\n    \"Reservations\": [\n        {\n            \"Groups\": [],\n            \"Instances\": [\n                {\n                    \"AmiLaunchIndex\": 0,\n                    \"ImageId\": \"ami-0c0ec0a3a3a4c34c0\",\n                    \"InstanceId\": \"i-06f3bb5c17dfd87b8\",\n                    \"InstanceType\": \"t2.medium\",\n                    \"KeyName\": \"test_ip\",\n                    \"LaunchTime\": \"2024-05-20T10:21:17+00:00\",\n                    \"Monitoring\": {\n                        \"State\": \"disabled\"\n                    },\n                    \"Placement\": {\n                        \"AvailabilityZone\": \"us-west-2a\",\n                        \"GroupName\": \"\",\n                        \"Tenancy\": \"default\"\n                    },\n                    \"Platform\": \"windows\",\n                    \"PrivateDnsName\": \"ip-172-31-46-151.us-west-2.compute.internal\",\n                    \"PrivateIpAddress\": \"*************\",\n                    \"ProductCodes\": [],\n                    \"PublicDnsName\": \"\",\n                    \"State\": {\n                        \"Code\": 80,\n                        \"Name\": \"stopped\"\n                    },\n                    \"StateTransitionReason\": \"User initiated (2024-05-20 11:20:13 GMT)\",\n                    \"SubnetId\": \"subnet-58f33212\",\n                    \"VpcId\": \"vpc-15f7bc6d\",\n                    \"Architecture\": \"x86_64\",\n                    \"BlockDeviceMappings\": [\n                        {\n                            \"DeviceName\": \"/dev/sda1\",\n                            \"Ebs\": {\n                                \"AttachTime\": \"2024-05-20T06:27:48+00:00\",\n                                \"DeleteOnTermination\": true,\n                                \"Status\": \"attached\",\n                                \"VolumeId\": \"vol-04a4ea389961925c8\"\n                            }\n                        }\n                    ],\n                    \"ClientToken\": \"9cfab87c-f529-4e25-afe7-4ef5ce23dc2d\",\n                    \"EbsOptimized\": false,\n                    \"EnaSupport\": true,\n                    \"Hypervisor\": \"xen\",\n                    \"NetworkInterfaces\": [\n                        {\n                            \"Attachment\": {\n                                \"AttachTime\": \"2024-05-20T06:27:48+00:00\",\n                                \"AttachmentId\": \"eni-attach-07a2087e3e4359b2a\",\n                                \"DeleteOnTermination\": true,\n                                \"DeviceIndex\": 0,\n                                \"Status\": \"attached\",\n                                \"NetworkCardIndex\": 0\n                            },\n                            \"Description\": \"\",\n                            \"Groups\": [\n                                {\n                                    \"GroupName\": \"ip-test-sg\",\n                                    \"GroupId\": \"sg-05d44cf8a327a9613\"\n                                }\n                            ],\n                            \"Ipv6Addresses\": [],\n                            \"MacAddress\": \"06:71:ed:b8:88:93\",\n                            \"NetworkInterfaceId\": \"eni-0a5d7fb50a8ba7689\",\n                            \"OwnerId\": \"438162393827\",\n                            \"PrivateDnsName\": \"ip-172-31-46-151.us-west-2.compute.internal\",\n                            \"PrivateIpAddress\": \"*************\",\n                            \"PrivateIpAddresses\": [\n                                {\n                                    \"Primary\": true,\n                                    \"PrivateDnsName\": \"ip-172-31-46-151.us-west-2.compute.internal\",\n                                    \"PrivateIpAddress\": \"*************\"\n                                }\n                            ],\n                            \"SourceDestCheck\": true,\n                            \"Status\": \"in-use\",\n                            \"SubnetId\": \"subnet-58f33212\",\n                            \"VpcId\": \"vpc-15f7bc6d\",\n                            \"InterfaceType\": \"interface\"\n                        }\n                    ],\n                    \"RootDeviceName\": \"/dev/sda1\",\n                    \"RootDeviceType\": \"ebs\",\n                    \"SecurityGroups\": [\n                        {\n                            \"GroupName\": \"ip-test-sg\",\n                            \"GroupId\": \"sg-05d44cf8a327a9613\"\n                        }\n                    ],\n                    \"SourceDestCheck\": true,\n                    \"StateReason\": {\n                        \"Code\": \"Client.UserInitiatedShutdown\",\n                        \"Message\": \"Client.UserInitiatedShutdown: User initiated shutdown\"\n                    },\n                    \"Tags\": [\n                        {\n                            \"Key\": \"Name\",\n                            \"Value\": \"fire_wall_test\"\n                        }\n                    ],\n                    \"VirtualizationType\": \"hvm\",\n                    \"CpuOptions\": {\n                        \"CoreCount\": 2,\n                        \"ThreadsPerCore\": 1\n                    },\n                    \"CapacityReservationSpecification\": {\n                        \"CapacityReservationPreference\": \"open\"\n                    },\n                    \"HibernationOptions\": {\n                        \"Configured\": false\n                    },\n                    \"MetadataOptions\": {\n                        \"State\": \"applied\",\n                        \"HttpTokens\": \"required\",\n                        \"HttpPutResponseHopLimit\": 2,\n                        \"HttpEndpoint\": \"enabled\",\n                        \"HttpProtocolIpv6\": \"disabled\",\n                        \"InstanceMetadataTags\": \"disabled\"\n                    },\n                    \"EnclaveOptions\": {\n                        \"Enabled\": false\n                    },\n                    \"PlatformDetails\": \"Windows\",\n                    \"UsageOperation\": \"RunInstances:0002\",\n                    \"UsageOperationUpdateTime\": \"2024-05-20T06:27:48+00:00\",\n                    \"PrivateDnsNameOptions\": {\n                        \"HostnameType\": \"ip-name\",\n                        \"EnableResourceNameDnsARecord\": false,\n                        \"EnableResourceNameDnsAAAARecord\": false\n                    },\n                    \"MaintenanceOptions\": {\n                        \"AutoRecovery\": \"default\"\n                    },\n                    \"CurrentInstanceBootMode\": \"legacy-bios\"\n                }\n            ],\n            \"OwnerId\": \"438162393827\",\n            \"ReservationId\": \"r-06ec926d026cbea66\"\n        }\n    ]\n}"
    },
    {
        "command": "aws ec2 describe-instances --region us-east-1 --filters \"Name=instance-state-name,Values=running\"",
        "status": "success",
        "output": "{\n    \"Reservations\": []\n}"
    }
]
[
    {
        "command": "aws ec2 describe-instances --region us-east-1 --filters \"Name=instance-state-name,Values=running\"",
        "status": "success",
        "output": "{\n    \"Reservations\": []\n}"
    }
]
[
    {
        "command": "aws ec2 describe-instances --region us-east-1",
        "status": "success",
        "output": "{\n    \"Reservations\": []\n}"
    },
    {
        "command": "aws ec2 describe-instances --region us-west-2",
        "status": "success",
        "output": "{\n    \"Reservations\": [\n        {\n            \"Groups\": [],\n            \"Instances\": [\n                {\n                    \"AmiLaunchIndex\": 0,\n                    \"ImageId\": \"ami-0c0ec0a3a3a4c34c0\",\n                    \"InstanceId\": \"i-06f3bb5c17dfd87b8\",\n                    \"InstanceType\": \"t2.medium\",\n                    \"KeyName\": \"test_ip\",\n                    \"LaunchTime\": \"2024-05-20T10:21:17+00:00\",\n                    \"Monitoring\": {\n                        \"State\": \"disabled\"\n                    },\n                    \"Placement\": {\n                        \"AvailabilityZone\": \"us-west-2a\",\n                        \"GroupName\": \"\",\n                        \"Tenancy\": \"default\"\n                    },\n                    \"Platform\": \"windows\",\n                    \"PrivateDnsName\": \"ip-172-31-46-151.us-west-2.compute.internal\",\n                    \"PrivateIpAddress\": \"*************\",\n                    \"ProductCodes\": [],\n                    \"PublicDnsName\": \"\",\n                    \"State\": {\n                        \"Code\": 80,\n                        \"Name\": \"stopped\"\n                    },\n                    \"StateTransitionReason\": \"User initiated (2024-05-20 11:20:13 GMT)\",\n                    \"SubnetId\": \"subnet-58f33212\",\n                    \"VpcId\": \"vpc-15f7bc6d\",\n                    \"Architecture\": \"x86_64\",\n                    \"BlockDeviceMappings\": [\n                        {\n                            \"DeviceName\": \"/dev/sda1\",\n                            \"Ebs\": {\n                                \"AttachTime\": \"2024-05-20T06:27:48+00:00\",\n                                \"DeleteOnTermination\": true,\n                                \"Status\": \"attached\",\n                                \"VolumeId\": \"vol-04a4ea389961925c8\"\n                            }\n                        }\n                    ],\n                    \"ClientToken\": \"9cfab87c-f529-4e25-afe7-4ef5ce23dc2d\",\n                    \"EbsOptimized\": false,\n                    \"EnaSupport\": true,\n                    \"Hypervisor\": \"xen\",\n                    \"NetworkInterfaces\": [\n                        {\n                            \"Attachment\": {\n                                \"AttachTime\": \"2024-05-20T06:27:48+00:00\",\n                                \"AttachmentId\": \"eni-attach-07a2087e3e4359b2a\",\n                                \"DeleteOnTermination\": true,\n                                \"DeviceIndex\": 0,\n                                \"Status\": \"attached\",\n                                \"NetworkCardIndex\": 0\n                            },\n                            \"Description\": \"\",\n                            \"Groups\": [\n                                {\n                                    \"GroupName\": \"ip-test-sg\",\n                                    \"GroupId\": \"sg-05d44cf8a327a9613\"\n                                }\n                            ],\n                            \"Ipv6Addresses\": [],\n                            \"MacAddress\": \"06:71:ed:b8:88:93\",\n                            \"NetworkInterfaceId\": \"eni-0a5d7fb50a8ba7689\",\n                            \"OwnerId\": \"438162393827\",\n                            \"PrivateDnsName\": \"ip-172-31-46-151.us-west-2.compute.internal\",\n                            \"PrivateIpAddress\": \"*************\",\n                            \"PrivateIpAddresses\": [\n                                {\n                                    \"Primary\": true,\n                                    \"PrivateDnsName\": \"ip-172-31-46-151.us-west-2.compute.internal\",\n                                    \"PrivateIpAddress\": \"*************\"\n                                }\n                            ],\n                            \"SourceDestCheck\": true,\n                            \"Status\": \"in-use\",\n                            \"SubnetId\": \"subnet-58f33212\",\n                            \"VpcId\": \"vpc-15f7bc6d\",\n                            \"InterfaceType\": \"interface\"\n                        }\n                    ],\n                    \"RootDeviceName\": \"/dev/sda1\",\n                    \"RootDeviceType\": \"ebs\",\n                    \"SecurityGroups\": [\n                        {\n                            \"GroupName\": \"ip-test-sg\",\n                            \"GroupId\": \"sg-05d44cf8a327a9613\"\n                        }\n                    ],\n                    \"SourceDestCheck\": true,\n                    \"StateReason\": {\n                        \"Code\": \"Client.UserInitiatedShutdown\",\n                        \"Message\": \"Client.UserInitiatedShutdown: User initiated shutdown\"\n                    },\n                    \"Tags\": [\n                        {\n                            \"Key\": \"Name\",\n                            \"Value\": \"fire_wall_test\"\n                        }\n                    ],\n                    \"VirtualizationType\": \"hvm\",\n                    \"CpuOptions\": {\n                        \"CoreCount\": 2,\n                        \"ThreadsPerCore\": 1\n                    },\n                    \"CapacityReservationSpecification\": {\n                        \"CapacityReservationPreference\": \"open\"\n                    },\n                    \"HibernationOptions\": {\n                        \"Configured\": false\n                    },\n                    \"MetadataOptions\": {\n                        \"State\": \"applied\",\n                        \"HttpTokens\": \"required\",\n                        \"HttpPutResponseHopLimit\": 2,\n                        \"HttpEndpoint\": \"enabled\",\n                        \"HttpProtocolIpv6\": \"disabled\",\n                        \"InstanceMetadataTags\": \"disabled\"\n                    },\n                    \"EnclaveOptions\": {\n                        \"Enabled\": false\n                    },\n                    \"PlatformDetails\": \"Windows\",\n                    \"UsageOperation\": \"RunInstances:0002\",\n                    \"UsageOperationUpdateTime\": \"2024-05-20T06:27:48+00:00\",\n                    \"PrivateDnsNameOptions\": {\n                        \"HostnameType\": \"ip-name\",\n                        \"EnableResourceNameDnsARecord\": false,\n                        \"EnableResourceNameDnsAAAARecord\": false\n                    },\n                    \"MaintenanceOptions\": {\n                        \"AutoRecovery\": \"default\"\n                    },\n                    \"CurrentInstanceBootMode\": \"legacy-bios\"\n                }\n            ],\n            \"OwnerId\": \"438162393827\",\n            \"ReservationId\": \"r-06ec926d026cbea66\"\n        }\n    ]\n}"
    }
]
