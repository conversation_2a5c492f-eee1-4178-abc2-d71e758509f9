[
    {
        "command": "aws s3 ls",
        "status": "success",
        "output": "2023-05-02 16:18:14 aa-textract-in\n2023-05-03 09:48:57 aa-textract-layer\n2023-05-02 16:19:28 aa-textract-output\n2023-05-12 10:20:56 aawtextract\n2024-05-08 10:24:06 amazon-connect-e5deb1b8170d\n2023-01-02 15:10:05 cf-templates-a7141j0doj8x-eu-central-1\n2023-05-17 11:54:48 cf-templates-a7141j0doj8x-us-west-1\n2022-07-27 14:07:24 cf-templates-a7141j0doj8x-us-west-2\n2022-02-08 14:00:24 conductor-cloudformationbucket-1m8jn3ze4mwkt\n2022-02-08 14:00:25 conductor-websitebucket-1dynoqfcji7ce\n2022-12-07 17:29:10 config-bucket-438162393827\n2021-09-24 13:17:36 cw-syn-results-438162393827-us-east-1\n2023-11-03 11:53:26 ekohesap\n2022-06-07 20:12:18 eksanywhereskyloop\n2022-12-03 19:14:55 election-438162393827\n2023-03-07 14:26:07 isbi-giris-karti\n2022-01-26 06:49:26 kopiax\n2022-03-11 16:36:26 kopiax-web\n2023-09-12 15:25:11 kris-deneme-bucket\n2024-02-06 11:51:44 mongo-ps\n2022-07-14 21:32:02 pipeline-bucket-sec-compliance-438162393827\n2022-12-20 10:48:39 rekognition-video-console-demo-fra-438162393827-1671522514\n2023-03-30 11:40:14 rekognition-video-console-demo-iad-438162393827-1680165593\n2024-02-15 11:56:35 skyloop-logo\n2021-01-27 05:26:25 skyloop.cloud\n2023-05-03 10:32:21 test-textract-lambdaframeworks\n2023-12-25 14:20:07 textract-image-input-test\n2023-05-25 13:35:51 textract-queries-bucket-2\n2023-01-03 11:07:14 tf-state-skyloop\n2023-03-27 13:32:36 tkk-test\n2023-02-21 14:11:36 tuzla-acil-apk\n2021-01-04 20:05:04 www.skyloop.cloud"
    }
]
[
    {
        "command": "aws ec2 describe-vpcs",
        "status": "success",
        "output": "{\n    \"Vpcs\": [\n        {\n            \"CidrBlock\": \"10.0.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-0a4055cb94e71739b\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-0250b256730918973\",\n                    \"CidrBlock\": \"10.0.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": false\n        },\n        {\n            \"CidrBlock\": \"**********/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-db9c3db4\",\n                    \"CidrBlock\": \"**********/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": true,\n            \"Tags\": [\n                {\n                    \"Key\": \"Name\",\n                    \"Value\": \"Default VPC\"\n                }\n            ]\n        },\n        {\n            \"CidrBlock\": \"10.0.0.0/16\",\n            \"DhcpOptionsId\": \"dopt-0c3cde0f9fdbbea78\",\n            \"State\": \"available\",\n            \"VpcId\": \"vpc-0bff8d6ffe6219cf8\",\n            \"OwnerId\": \"438162393827\",\n            \"InstanceTenancy\": \"default\",\n            \"CidrBlockAssociationSet\": [\n                {\n                    \"AssociationId\": \"vpc-cidr-assoc-05dae0f62f0e0614d\",\n                    \"CidrBlock\": \"10.0.0.0/16\",\n                    \"CidrBlockState\": {\n                        \"State\": \"associated\"\n                    }\n                }\n            ],\n            \"IsDefault\": false,\n            \"Tags\": [\n                {\n                    \"Key\": \"department\",\n                    \"Value\": \"Menemen\"\n                }\n            ]\n        }\n    ]\n}"
    }
]
[
    {
        "command": "aws ec2 create-key-pair --key-name my-key-pair --query 'KeyMaterial' --output text > my-key-pair.pem",
        "status": "success",
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
]
[
    {
        "command": "aws ec2 create-security-group --group-name skyloop-sg-4 --description \"Security group for Skyloop application\" --vpc-id vpc-eef52193",
        "status": "success",
        "output": "{\n    \"GroupId\": \"sg-0c777ede7b81e89d5\"\n}"
    }
]
[
    {
        "command": "aws ec2 run-instances --image-id ami-0022f774911c1d690 --count 1 --instance-type t2.micro --key-name my-key-pair --security-group-ids sg-0c777ede7b81e89d5 --subnet-id subnet-0f6b423374d241058 --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=skyloop-test-ai}]'",
        "status": "failure",
        "error": "An error occurred (InvalidSubnetID.NotFound) when calling the RunInstances operation: The subnet ID 'subnet-0f6b423374d241058' does not exist"
    }
]
[
    {
        "command": "aws ec2 describe-subnets --filters \"Name=vpc-id,Values=vpc-eef52193\"",
        "status": "success",
        "output": "{\n    \"Subnets\": [\n        {\n            \"AvailabilityZone\": \"us-east-1f\",\n            \"AvailabilityZoneId\": \"use1-az5\",\n            \"AvailableIpAddressCount\": 4089,\n            \"CidrBlock\": \"***********/20\",\n            \"DefaultForAz\": true,\n            \"MapPublicIpOnLaunch\": true,\n            \"MapCustomerOwnedIpOnLaunch\": false,\n            \"State\": \"available\",\n            \"SubnetId\": \"subnet-9ef68490\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"AssignIpv6AddressOnCreation\": false,\n            \"Ipv6CidrBlockAssociationSet\": [],\n            \"SubnetArn\": \"arn:aws:ec2:us-east-1:438162393827:subnet/subnet-9ef68490\",\n            \"EnableDns64\": false,\n            \"Ipv6Native\": false,\n            \"PrivateDnsNameOptionsOnLaunch\": {\n                \"HostnameType\": \"ip-name\",\n                \"EnableResourceNameDnsARecord\": false,\n                \"EnableResourceNameDnsAAAARecord\": false\n            }\n        },\n        {\n            \"AvailabilityZone\": \"us-east-1c\",\n            \"AvailabilityZoneId\": \"use1-az6\",\n            \"AvailableIpAddressCount\": 4088,\n            \"CidrBlock\": \"***********/20\",\n            \"DefaultForAz\": true,\n            \"MapPublicIpOnLaunch\": true,\n            \"MapCustomerOwnedIpOnLaunch\": false,\n            \"State\": \"available\",\n            \"SubnetId\": \"subnet-2c008573\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"AssignIpv6AddressOnCreation\": false,\n            \"Ipv6CidrBlockAssociationSet\": [],\n            \"SubnetArn\": \"arn:aws:ec2:us-east-1:438162393827:subnet/subnet-2c008573\",\n            \"EnableDns64\": false,\n            \"Ipv6Native\": false,\n            \"PrivateDnsNameOptionsOnLaunch\": {\n                \"HostnameType\": \"ip-name\",\n                \"EnableResourceNameDnsARecord\": false,\n                \"EnableResourceNameDnsAAAARecord\": false\n            }\n        },\n        {\n            \"AvailabilityZone\": \"us-east-1b\",\n            \"AvailabilityZoneId\": \"use1-az4\",\n            \"AvailableIpAddressCount\": 4089,\n            \"CidrBlock\": \"***********/20\",\n            \"DefaultForAz\": true,\n            \"MapPublicIpOnLaunch\": true,\n            \"MapCustomerOwnedIpOnLaunch\": false,\n            \"State\": \"available\",\n            \"SubnetId\": \"subnet-42d49c0f\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"AssignIpv6AddressOnCreation\": false,\n            \"Ipv6CidrBlockAssociationSet\": [],\n            \"SubnetArn\": \"arn:aws:ec2:us-east-1:438162393827:subnet/subnet-42d49c0f\",\n            \"EnableDns64\": false,\n            \"Ipv6Native\": false,\n            \"PrivateDnsNameOptionsOnLaunch\": {\n                \"HostnameType\": \"ip-name\",\n                \"EnableResourceNameDnsARecord\": false,\n                \"EnableResourceNameDnsAAAARecord\": false\n            }\n        },\n        {\n            \"AvailabilityZone\": \"us-east-1a\",\n            \"AvailabilityZoneId\": \"use1-az2\",\n            \"AvailableIpAddressCount\": 4088,\n            \"CidrBlock\": \"***********/20\",\n            \"DefaultForAz\": true,\n            \"MapPublicIpOnLaunch\": true,\n            \"MapCustomerOwnedIpOnLaunch\": false,\n            \"State\": \"available\",\n            \"SubnetId\": \"subnet-5a38bf7b\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"AssignIpv6AddressOnCreation\": false,\n            \"Ipv6CidrBlockAssociationSet\": [],\n            \"SubnetArn\": \"arn:aws:ec2:us-east-1:438162393827:subnet/subnet-5a38bf7b\",\n            \"EnableDns64\": false,\n            \"Ipv6Native\": false,\n            \"PrivateDnsNameOptionsOnLaunch\": {\n                \"HostnameType\": \"ip-name\",\n                \"EnableResourceNameDnsARecord\": false,\n                \"EnableResourceNameDnsAAAARecord\": false\n            }\n        },\n        {\n            \"AvailabilityZone\": \"us-east-1d\",\n            \"AvailabilityZoneId\": \"use1-az1\",\n            \"AvailableIpAddressCount\": 4089,\n            \"CidrBlock\": \"**********/20\",\n            \"DefaultForAz\": true,\n            \"MapPublicIpOnLaunch\": true,\n            \"MapCustomerOwnedIpOnLaunch\": false,\n            \"State\": \"available\",\n            \"SubnetId\": \"subnet-127cc474\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"AssignIpv6AddressOnCreation\": false,\n            \"Ipv6CidrBlockAssociationSet\": [],\n            \"SubnetArn\": \"arn:aws:ec2:us-east-1:438162393827:subnet/subnet-127cc474\",\n            \"EnableDns64\": false,\n            \"Ipv6Native\": false,\n            \"PrivateDnsNameOptionsOnLaunch\": {\n                \"HostnameType\": \"ip-name\",\n                \"EnableResourceNameDnsARecord\": false,\n                \"EnableResourceNameDnsAAAARecord\": false\n            }\n        },\n        {\n            \"AvailabilityZone\": \"us-east-1e\",\n            \"AvailabilityZoneId\": \"use1-az3\",\n            \"AvailableIpAddressCount\": 4088,\n            \"CidrBlock\": \"***********/20\",\n            \"DefaultForAz\": true,\n            \"MapPublicIpOnLaunch\": true,\n            \"MapCustomerOwnedIpOnLaunch\": false,\n            \"State\": \"available\",\n            \"SubnetId\": \"subnet-3c99560d\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"OwnerId\": \"438162393827\",\n            \"AssignIpv6AddressOnCreation\": false,\n            \"Ipv6CidrBlockAssociationSet\": [],\n            \"SubnetArn\": \"arn:aws:ec2:us-east-1:438162393827:subnet/subnet-3c99560d\",\n            \"EnableDns64\": false,\n            \"Ipv6Native\": false,\n            \"PrivateDnsNameOptionsOnLaunch\": {\n                \"HostnameType\": \"ip-name\",\n                \"EnableResourceNameDnsARecord\": false,\n                \"EnableResourceNameDnsAAAARecord\": false\n            }\n        }\n    ]\n}"
    }
]
[
    {
        "command": "aws ec2 run-instances --image-id ami-0022f774911c1d690 --count 1 --instance-type t2.micro --key-name my-key-pair --security-group-ids sg-0c777ede7b81e89d5 --subnet-id subnet-9ef68490 --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=skyloop-test-ai}]'",
        "status": "success",
        "output": "{\n    \"Groups\": [],\n    \"Instances\": [\n        {\n            \"AmiLaunchIndex\": 0,\n            \"ImageId\": \"ami-0022f774911c1d690\",\n            \"InstanceId\": \"i-00aee64591cbabf39\",\n            \"InstanceType\": \"t2.micro\",\n            \"KeyName\": \"my-key-pair\",\n            \"LaunchTime\": \"2024-05-08T11:38:51+00:00\",\n            \"Monitoring\": {\n                \"State\": \"disabled\"\n            },\n            \"Placement\": {\n                \"AvailabilityZone\": \"us-east-1f\",\n                \"GroupName\": \"\",\n                \"Tenancy\": \"default\"\n            },\n            \"PrivateDnsName\": \"ip-172-31-68-218.ec2.internal\",\n            \"PrivateIpAddress\": \"*************\",\n            \"ProductCodes\": [],\n            \"PublicDnsName\": \"\",\n            \"State\": {\n                \"Code\": 0,\n                \"Name\": \"pending\"\n            },\n            \"StateTransitionReason\": \"\",\n            \"SubnetId\": \"subnet-9ef68490\",\n            \"VpcId\": \"vpc-eef52193\",\n            \"Architecture\": \"x86_64\",\n            \"BlockDeviceMappings\": [],\n            \"ClientToken\": \"489d0e32-72ae-4110-abc8-0d98864594d4\",\n            \"EbsOptimized\": false,\n            \"EnaSupport\": true,\n            \"Hypervisor\": \"xen\",\n            \"NetworkInterfaces\": [\n                {\n                    \"Attachment\": {\n                        \"AttachTime\": \"2024-05-08T11:38:51+00:00\",\n                        \"AttachmentId\": \"eni-attach-018832b5dc6e4b72f\",\n                        \"DeleteOnTermination\": true,\n                        \"DeviceIndex\": 0,\n                        \"Status\": \"attaching\",\n                        \"NetworkCardIndex\": 0\n                    },\n                    \"Description\": \"\",\n                    \"Groups\": [\n                        {\n                            \"GroupName\": \"skyloop-sg-4\",\n                            \"GroupId\": \"sg-0c777ede7b81e89d5\"\n                        }\n                    ],\n                    \"Ipv6Addresses\": [],\n                    \"MacAddress\": \"16:ff:ce:06:d0:7d\",\n                    \"NetworkInterfaceId\": \"eni-06a2983f59dcacefd\",\n                    \"OwnerId\": \"438162393827\",\n                    \"PrivateDnsName\": \"ip-172-31-68-218.ec2.internal\",\n                    \"PrivateIpAddress\": \"*************\",\n                    \"PrivateIpAddresses\": [\n                        {\n                            \"Primary\": true,\n                            \"PrivateDnsName\": \"ip-172-31-68-218.ec2.internal\",\n                            \"PrivateIpAddress\": \"*************\"\n                        }\n                    ],\n                    \"SourceDestCheck\": true,\n                    \"Status\": \"in-use\",\n                    \"SubnetId\": \"subnet-9ef68490\",\n                    \"VpcId\": \"vpc-eef52193\",\n                    \"InterfaceType\": \"interface\"\n                }\n            ],\n            \"RootDeviceName\": \"/dev/xvda\",\n            \"RootDeviceType\": \"ebs\",\n            \"SecurityGroups\": [\n                {\n                    \"GroupName\": \"skyloop-sg-4\",\n                    \"GroupId\": \"sg-0c777ede7b81e89d5\"\n                }\n            ],\n            \"SourceDestCheck\": true,\n            \"StateReason\": {\n                \"Code\": \"pending\",\n                \"Message\": \"pending\"\n            },\n            \"Tags\": [\n                {\n                    \"Key\": \"Name\",\n                    \"Value\": \"skyloop-test-ai\"\n                }\n            ],\n            \"VirtualizationType\": \"hvm\",\n            \"CpuOptions\": {\n                \"CoreCount\": 1,\n                \"ThreadsPerCore\": 1\n            },\n            \"CapacityReservationSpecification\": {\n                \"CapacityReservationPreference\": \"open\"\n            },\n            \"MetadataOptions\": {\n                \"State\": \"pending\",\n                \"HttpTokens\": \"optional\",\n                \"HttpPutResponseHopLimit\": 1,\n                \"HttpEndpoint\": \"enabled\",\n                \"HttpProtocolIpv6\": \"disabled\",\n                \"InstanceMetadataTags\": \"disabled\"\n            },\n            \"EnclaveOptions\": {\n                \"Enabled\": false\n            },\n            \"PrivateDnsNameOptions\": {\n                \"HostnameType\": \"ip-name\",\n                \"EnableResourceNameDnsARecord\": false,\n                \"EnableResourceNameDnsAAAARecord\": false\n            },\n            \"MaintenanceOptions\": {\n                \"AutoRecovery\": \"default\"\n            },\n            \"CurrentInstanceBootMode\": \"legacy-bios\"\n        }\n    ],\n    \"OwnerId\": \"438162393827\",\n    \"ReservationId\": \"r-0e5f2c03d23363a79\"\n}"
    }
]
