import os
import json
import time
import boto3
import streamlit as st
from botocore.exceptions import NoCredentialsError, ClientError
import app

# Constants and Configuration
AWS_REGION = 'us-east-1'  # Update to your region
# MODEL_ID = "meta.llama3-8b-instruct-v1:0"  # Validate your model ID
MODEL_ID = "anthropic.claude-3-haiku-20240307-v1:0"
ACCEPT = "application/json"
CONTENT_TYPE = "application/json"

KILLSWITCH_PATH = "data/request_count.json"
REQUEST_THRESHOLD = 20  # Maximum allowed requests
INITIAL_PROMPT_PATH = "data/given_information/initial_prompt.txt"
TEMP_FILE_PATH = "data/cli_logs/.sample.json"

# Initialize AWS Bedrock Client
bedrock_client = boto3.client(
    service_name='bedrock-runtime',
    region_name=AWS_REGION
)

# Function to clear Streamlit session state and remove temporary files


def clear_session():
    """Clears Streamlit session state and removes temporary files."""
    # Clear all session state
    st.session_state.clear()

    # Remove any temporary file if it exists
    if os.path.exists(TEMP_FILE_PATH):
        os.remove(TEMP_FILE_PATH)

    # Rerun Streamlit app to reset state
    st.experimental_rerun()

# Function to manage the killswitch (request limit)


def manage_killswitch():
    """Checks the request count to determine if the limit is reached."""
    if not os.path.exists(KILLSWITCH_PATH):
        with open(KILLSWITCH_PATH, "w") as file:
            json.dump({"count": 0}, file)

    with open(KILLSWITCH_PATH, "r+") as file:
        data = json.load(file)
        current_count = data["count"]

        if current_count >= REQUEST_THRESHOLD:
            st.warning(
                "API request limit reached. Please reset the killswitch.")
            return True  # Limit reached

        # Increment the request count
        data["count"] += 1
        file.seek(0)
        json.dump(data, file)
        file.truncate()

    return False  # Limit not reached, continue processing

# Function to generate a response from AWS Bedrock


def gen(history, prompt, prompter):
    """Generates a response using AWS Bedrock with a killswitch check."""
    if manage_killswitch():
        return ""  # Stop processing if limit reached

    # Read additional information for the model
    try:
        with open(INITIAL_PROMPT_PATH, "r") as file:
            initial_prompt = file.read()
    except FileNotFoundError:
        st.error("Required data file not found. Check the initial prompt path.")
        return ""

    # Create the request body for AWS Bedrock
    # request_body = {
    #     "prompt": f"Instructions:\n{initial_prompt}\nHistory:\n{history}\nPrompt:\n{prompter}: {prompt}",
    #     "max_gen_len": 512,
    #     "temperature": 0.5,
    #     "top_p": 0.9,
    # }

    request_body = {
        "anthropic_version": "bedrock-2023-05-31",
        "max_tokens": 1024,
        "messages": [
            {
                "role": "user",
                "content": [{"type": "text", "text": f"Instructions:\n{initial_prompt}\nHistory:\n{history}\nPrompt:\n{prompter}: {prompt}"}],
            }
        ],
    }

    body_str = json.dumps(request_body)

    # Invoke the model and handle the response
    try:
        response = bedrock_client.invoke_model(
            body=body_str,
            modelId=MODEL_ID,
            accept=ACCEPT,
            contentType=CONTENT_TYPE,
        )

        # Retrieve the generated text
        response_data = json.loads(response['body'].read())
        content = response_data.get("content", [])
        if content:
            # Assuming the content array has at least one text item
            generated_text = content[0].get("text", "No text found")

        # print("AI: ", generated_text)
        # generated_text = response_data.get("generation", "")
        # print(response_data)
        # print(generated_text)
        return generated_text  # Return the generated text

    except NoCredentialsError:
        st.error("AWS credentials are missing or invalid.")
        return ""

    except ClientError as client_error:
        st.error(f"An AWS client error occurred: {str(client_error)}")
        return ""

    except Exception as general_error:
        st.error(f"An unexpected error occurred: {str(general_error)}")
        return ""

# Function to stream responses for a conversational experience


def save_conversation(session_name):
    """Saves the conversation history to a file."""

    if session_name and "messages" in st.session_state:
        # Ensure the directory exists
        os.makedirs(f"data/history/", exist_ok=True)

        file_path = f"data/history/{session_name}.json"
        try:
            with open(file_path, "w") as file:
                json.dump(st.session_state.messages, file)
        except Exception as general_error:
            st.error(
                f"An error occurred while saving the conversation: {str(general_error)}")
