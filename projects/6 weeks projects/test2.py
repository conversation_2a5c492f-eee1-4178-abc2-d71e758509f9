import streamlit as st
from datetime import datetime
import time as t
import backend
from cli_executor import *
from command_datastructure import *


# Constants
API_CALL_LIMIT = 50
WORD_DELAY = 0.00

with open("reply.txt", 'r') as file:
    # Read the contents of the file
    reply = file.read()


def main():
    # Set page layout to wide
    st.set_page_config(layout="wide")

    # Session state initialization
    initialize_session_state(st, datetime)

    # Sidebar setup
    st.sidebar.title("AWS Gen.ai")
    if st.sidebar.button("Logout"):
        backend.clear_session()
        reset_call_count(st)

    if get_call_count(st) >= API_CALL_LIMIT:
        st.sidebar.warning("Maximum API calls reached.")
        return

    session_name = st.session_state["session_name"]
    st.sidebar.title(f"Session: {session_name}")

    # Get user prompt input at the bottom of the left column
    prompt = st.chat_input("What's up?")

    # Initialize response to ensure it has a value before use
    response = None

    if prompt:
        # Add user's message to the session state and display it
        st.session_state["messages"].append(
            {"role": "user", "content": prompt}
        )
        with st.chat_message("user"):
            st.markdown(prompt)

        increment_call_count(st)

        # Generate AI response
        with st.spinner("Generating..."):
            response = reply

    # Handle AI response if it exists
    if response:
        increment_call_count(st)
        handle_ai_response(response, session_name, st)


def initialize_session_state(st, datetime):
    if "api_call_count" not in st.session_state:
        st.session_state["api_call_count"] = 0
    if "session_name" not in st.session_state:
        st.session_state["session_name"] = datetime.now().strftime(
            "%Y-%m-%d %H:%M:%S")
    if "messages" not in st.session_state:
        st.session_state["messages"] = []
    if "cli_commands" not in st.session_state:
        st.session_state["executed_commands"] = LinkedList()


def get_call_count(st):
    return st.session_state["api_call_count"]


def increment_call_count(st):
    st.session_state["api_call_count"] += 1


def reset_call_count(st):
    st.session_state["api_call_count"] = 0


def display_commands_with_execution(st, commands, prompt_number):

    for command in enumerate(commands):
        col1, col2 = st.columns([4, 1])
        with col1:
            st.code(command, language='bash')
        with col2:
            if st.button(f"RUN", key=f"execute_{prompt_number}"):
                st.success(f"Executed: {command}")


def response_generator(response, word_delay):
    """
    Generates text from a response with a delay to simulate typing.
    """
    for paragraph in response.split("\n\n"):
        for word in paragraph.split():
            yield word + " "
            t.sleep(word_delay)
        yield "\n\n"


def handle_ai_response(response, session_name, st):
    """
    Handles AI responses, updating session state and processing CLI commands if needed.
    """

    with st.chat_message("ai"):
        st.write_stream(response_generator(response, WORD_DELAY))

    st.session_state["messages"].append({"role": "ai", "content": response})
    backend.save_conversation(session_name)

    if contains_double_ampersand(response):
        increment_call_count(st)
        for i, command in enumerate(extract_commands(response)):
            st.session_state["executed_commands"].insert(
                command, i, get_call_count(st)
            )



        command_display = st.session_state["executed_commands"].find_by_order(
            get_call_count(st))
        with st.chat_message("ai"):
            # st.write_stream(command_display)
            display_commands_with_execution(
                st, command_display, get_call_count(st))



if __name__ == "__main__":
    main()
