Resources:
  StarterEC2Instance:
    Type: "AWS::EC2::Instance"
    Properties:
      InstanceType: "t2.micro"
      KeyName: "starter-key"
      ImageId: !Ref AmazonLinux2AMI
      SecurityGroupIds:
        - "sg-0fa24e2fa60f03114"
      SubnetId: !Ref DefaultSubnet

  DefaultSubnet:
    Type: "AWS::EC2::Subnet"
    Properties:
      VpcId: "vpc-eef52193"
      CidrBlock: "**********/20"
      AvailabilityZone: !Select [0, !GetAZs]
      MapPublicIpOnLaunch: true

  AmazonLinux2AMI:
    Type: "AWS::SSM::Parameter::Value<AWS::EC2::Image::Id>"
    Default: "/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-gp2"

Outputs:
  InstanceId:
    Description: "ID of the EC2 instance"
    Value: !Ref StarterEC2Instance
