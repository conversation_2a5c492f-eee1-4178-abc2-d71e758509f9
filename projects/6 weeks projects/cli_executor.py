import re
import subprocess
import json
import os
from datetime import datetime

# Set the default output files for CLI logs
time = datetime.now().strftime("%d-%m-%Y-%H-%M-%S")
cli_result_path = f"data/cli_logs/logs/{time}cli_result.logs"
result_path = f"data/cli_logs/returns/{time}result.logs"

# Function to extract text enclosed in '&^' and '^&'


def extract_double_ampersand_text(text):
    pattern = re.compile(r"&\^(.+?)\^&")  # Adjusted regex for new delimiters
    return pattern.findall(text)

# Function to check if text contains placeholders with angle brackets '<...>'


def contains_double_ampersand(text):
    """
    Checks if the given text contains &^ ^& delimiters.
    """
    pattern = re.compile(r"&\^.+?\^&")
    return bool(pattern.search(text))


def contains_placeholders(text):
    pattern = re.compile(r"<.+?>")
    return bool(pattern.search(text))

# Function to execute a list of CLI commands with error handling


def run_commands(commands):
    results = []

    for command in commands:
        try:
            # Execute the command with shell=True for output redirection
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,  # Capture stdout
                stderr=subprocess.PIPE,  # Capture stderr
                shell=True,  # Required for output redirection
                text=True,
            )

            # Wait for the process to complete and get stdout/stderr
            stdout, stderr = process.communicate()

            # Check if the command was successful
            if process.returncode == 0:
                # Handle output redirection
                redirect_match = re.search(r"> (\S+)$", command.strip())
                if redirect_match:
                    output_file = redirect_match.group(1)
                    if os.path.exists(output_file):
                        # Read the content of the redirected output file
                        with open(output_file, 'r') as f:
                            file_output = f.read().strip()
                        output = f"Output redirected to '{output_file}':\n{file_output}"
                    else:
                        output = "Output file not found or empty."
                else:
                    output = stdout.strip() if stdout else "No output returned."

                results.append({
                    "command": command,
                    "status": "success",
                    "output": output,
                })
            else:
                # Handle command failure
                results.append({
                    "command": command,
                    "status": "failure",
                    "error": stderr.strip() if stderr else "Unknown error",
                })
        except Exception as e:
            results.append({
                "command": command,
                "status": "error",
                "error": str(e).strip(),
            })

    return results

# Function to save results to a file in JSON format


def save_results(results, path):
    # Ensure the directory leading to the file exists
    if not os.path.exists(os.path.dirname(path)):
        os.makedirs(os.path.dirname(path), exist_ok=True)

    with open(path, "a") as f:
        json.dump(results, f, indent=4)
        f.write("\n")  # Ensure newline after each entry

# Function to process text to extract commands and placeholders, then execute commands


def process_and_get_results(text):
    # Extract commands enclosed in '&^' and '^&'
    commands = extract_double_ampersand_text(text)

    # Separate commands into those to run and those with placeholders
    commands_to_run = []
    commands_with_placeholders = []
    found_placeholder = False

    # Loop through commands and separate those with placeholders
    for command in commands:
        if found_placeholder or contains_placeholders(command):
            found_placeholder = True
            commands_with_placeholders.append(command)
        else:
            commands_to_run.append(command)

    # Execute the commands that can be run
    results = run_commands(commands_to_run)

    # If there are commands with placeholders, append a message
    if commands_with_placeholders:
        results.append({
            "message": "The following commands couldn't be run because they contain placeholders or rely on data from other commands:",
        })
        results.extend([{"command": cmd, "status": "not executed"}
                       for cmd in commands_with_placeholders])

    # Save results
    save_results(results, result_path)
    return results


def extract_commands(text):
    # Extract commands enclosed in '&^' and '^&'
    commands = extract_double_ampersand_text(text)

    # Separate commands into those to run and those with placeholders
    commands_to_run = []
    commands_with_placeholders = []
    found_placeholder = False

    # Loop through commands and separate those with placeholders
    for command in commands:
        if found_placeholder or contains_placeholders(command):
            found_placeholder = True
            commands_with_placeholders.append(command)
        else:
            commands_to_run.append(command)

    # Execute the commands that can be run
    return commands_to_run
