import json
import boto3
from botocore.exceptions import NoCredentialsError, ClientError

# AWS Bedrock configuration
region_name = 'us-east-1'  # Change to your region if needed
# Ensure this is the correct model ID
model_id = "meta.llama3-8b-instruct-v1:0"
accept = "application/json"
content_type = "application/json"


# print(full)

# Create the request body
request_body = {
    "prompt": f"hello how old are you",
    "max_gen_len": 100,
    "temperature": 0.1,
    "top_p": 0.9
}

# Convert request body to JSON string
body_str = json.dumps(request_body)
print(body_str)

# Initialize the Bedrock client
bedrock_client = boto3.client(
    service_name='bedrock-runtime'
)



response = bedrock_client.invoke_model(
    body=body_str, modelId=model_id, accept=accept, contentType=content_type
)

# Parse the response
# Properly read the body

# print(response["body"].read())
response_data = json.loads(response["body"].read())

# Extract the generated text
generated_text = response_data.get("generation", "")
print(generated_text)
