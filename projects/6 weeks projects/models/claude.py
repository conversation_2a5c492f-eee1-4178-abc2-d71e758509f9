import json
import boto3
from botocore.exceptions import NoCredentialsError, ClientError

# AWS Bedrock configuration
region_name = 'us-east-1'  # Adjust as needed
# model_id = "anthropic.claude-3-sonnet-20240229-v1:0"  # New model ID
model_id = "anthropic.claude-3-haiku-20240307-v1:0"
accept = "application/json"  # Set accept header
content_type = "application/json"  # Set content type for the request

prompt_text = '''
**Initial Response**
You will be interacting with the user and AWS.

**Gathering Essential Details**
When requesting resource creation, follow these steps:

1. Ask the user for essential details, including:
	* Resource name
	* Resource type
	* Initial parameters
2. If additional parameters are required, prompt the user to provide them before proceeding.
3. If response includes CLI commands, no need to prompt the user for confirmation

**Crafting Creation Commands**
Only provide creation CLI commands in the format `<<aws ...>>` once all necessary parameters have been gathered. Ensure each CLI command is sent individually per response, without combining multiple commands in a single response.

**CLI Command Order and Format**
Display CLI commands in the order of dependent resources, using simple CLI command formats. Send one CLI command at a time, and use the format `<<aws ...>>` for clarity.

**Retrieving Parameters and Creating Resources**
To gather parameters and create resources, follow these steps:

1. Ask for the region, defaulting to the availability zone 'a' if the user specifies 'default'.
2. If 'default' is chosen, create a new security group for each service based on provided VPC data.
3. For dependent functions like EC2 key pairs, create them if 'default' is selected.
4. Use CLI commands in the format `<<aws ...>>` to retrieve parameters and create resources.

History::
USER: create me an ec2

AI: To create an EC2 instance, I need some additional information from you. Please provide the following details:

1. Resource name (e.g., my-ec2-instance)
2. Instance type (e.g., t2.micro, t2.small, etc.)
3. Amazon Machine Image (AMI) ID (e.g., ami-0cff7528ff583bf9a for Amazon Linux 2 AMI)

Once you provide these details, I can generate the appropriate AWS CLI command to create the EC2 instance.

USER: name the ec2 starter_ec2 with amazon linux engine and smallest instance type. create the key-pair and name it start-key and also create the security group and name it starter-sg. use default region us-east-1

'''

# Create the request body according to the new structure
request_body = {
    "anthropic_version": "bedrock-2023-05-31",
    "max_tokens": 1000,
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": prompt_text
                }
            ]
        }
    ]
}

# Convert the request body to a JSON string
body_str = json.dumps(request_body)

# Initialize the Bedrock client
bedrock_client = boto3.client(
    service_name='bedrock-runtime',
    region_name=region_name  # Ensure you're in the correct region
)

try:
    # Invoke the model with the updated parameters
    response = bedrock_client.invoke_model(
        body=body_str,
        modelId=model_id,
        accept=accept,
        contentType=content_type
    )

    # Read the body and parse the response
    response_data = json.loads(response["body"].read())

    content = response_data.get("content", [])
    if content:
        # Assuming the content array has at least one text item
        text_content = content[0].get("text", "No text found")
        print("AI: ", text_content)

    else:
        print("No text content found in the response")


except NoCredentialsError:
    print("No AWS credentials found. Please set up your credentials.")
except ClientError as e:
    print(f"An error occurred: {e}")
