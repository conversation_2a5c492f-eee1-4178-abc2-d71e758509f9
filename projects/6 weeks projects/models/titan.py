import json
import boto3
from botocore.exceptions import NoCredentialsError, ClientError

# AWS Bedrock configuration
region_name = 'us-east-1'  # Change to your region if needed
model_id = "amazon.titan-text-express-v1"  # Updated model ID
accept = "application/json"  # Updated accept header
content_type = "application/json"  # Content type for the request

prompt_text = '''
**Initial Response**
You will be interacting with the user and AWS.

**Gathering Essential Details**
When requesting resource creation, follow these steps:

1. Ask the user for essential details, including:
	* Resource name
	* Resource type
	* Initial parameters
2. If additional parameters are required, prompt the user to provide them before proceeding.
3. If response includes CLI commands, no need to prompt the user for confirmation

**Crafting Creation Commands**
Only provide creation CLI commands in the format `<<aws ...>>` once all necessary parameters have been gathered. Ensure each CLI command is sent individually, without combining multiple commands in a single response.

**CLI Command Order and Format**
Display CLI commands in the order of dependent resources, using simple CLI command formats. Send one CLI command at a time, and use the format `<<aws ...>>` for clarity.

**Retrieving Parameters and Creating Resources**
To gather parameters and create resources, follow these steps:

1. Ask for the region, defaulting to the availability zone 'a' if the user specifies 'default'.
2. If 'default' is chosen, create a new security group for each service based on provided VPC data.
3. For dependent functions like EC2 key pairs, create them if 'default' is selected.
4. Use CLI commands in the format `<<aws ...>>` to retrieve parameters and create resources.

History::
USER: create me an ec2

Bot: Hello. I can help you with that. I just need a little more information. Can you please provide the following?

- The name of the resource
- The type of the resource
- Any initial parameters for the resource

USER: name the ec2 starter_ec2 with amazon linux engine and smallest instance type. create the key-pair and name it start-key and also create the security group and name it starter-sg. use default region us-east-1

Bot: Thank you for that information. I have a few more questions before I can create the EC2 instance. Can you please provide the following?

- The name of the security group
- The name of the key pair
- The VPC ID

If you do not know the VPC ID, I can help you retrieve it.

USER: create the key-pair and name it start-key and also create the security group and name it starter-sg. get the vpc id form my account


'''

# Create the request body with specific text generation configuration
request_body = {
    "inputText": prompt_text,
    "textGenerationConfig": {
        "maxTokenCount": 4096,
        "stopSequences": [],
        "temperature": 0.2,
        "topP": 1
    }
}

# Convert request body to JSON string
body_str = json.dumps(request_body)

# Initialize the Bedrock client
bedrock_client = boto3.client(
    service_name='bedrock-runtime',
    region_name=region_name  # Ensure the correct AWS region
)

try:
    # Invoke the model with the given parameters
    response = bedrock_client.invoke_model(
        body=body_str,
        modelId=model_id,
        accept=accept,
        contentType=content_type
    )

    # Properly read the body
    response_data = json.loads(response["body"].read())

    results = response_data.get("results", [])
    if results:
        output_text = results[0].get("outputText", "No text found")
        print("Generated Text:")
        print(output_text)


except NoCredentialsError:
    print("No AWS credentials found. Please set up your credentials.")
except ClientError as e:
    print(f"An error occurred: {e}")
