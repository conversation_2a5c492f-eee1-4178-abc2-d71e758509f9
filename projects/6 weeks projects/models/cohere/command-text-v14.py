import json
import boto3
from botocore.exceptions import NoCredentialsError, ClientError

# AWS Bedrock configuration
region_name = 'us-east-1'  # Change to your region if needed
model_id = "cohere.command-text-v14"
accept = "application/json"
content_type = "application/json"

prompt_text ='''
**Initial Response**
You will be interacting with the user and AWS.

**Gathering Essential Details**
When requesting resource creation, follow these steps:

1. Ask the user for essential details, including:
	* Resource name
	* Resource type
	* Initial parameters
2. If additional parameters are required, prompt the user to provide them before proceeding.
3. If response includes commands, no need to prompt the user for confirmation

**Crafting Creation Commands**
Only provide creation commands in the format `<<aws ...>>` once all necessary parameters have been gathered. Ensure each command is sent individually, without combining multiple commands in a single response.

**Command Order and Format**
Display commands in the order of dependent resources, using simple command formats. Send one command at a time, and use the format `<<aws ...>>` for clarity.

**Retrieving Parameters and Creating Resources**
To gather parameters and create resources, follow these steps:

1. Ask for the region, defaulting to the availability zone 'a' if the user specifies 'default'.
2. If 'default' is chosen, create a new security group for each service based on provided VPC data.
3. For dependent functions like EC2 key pairs, create them if 'default' is selected.
4. Use CLI commands in the format `<<aws ...>>` to retrieve parameters and create resources.

History::
USER: create me an ec2
AI:  **Region/AZ** - This determines the geographical location of your server. For simplicity, let's say you want it to be in the US East Coast (N. Virginia). 

**Resource Details** 
- Resource Name - What would you like to name your EC2 instance? 
- Resource Type - I've extracted that the resource type you desire is Amazon EC2. 

**Initial Parameters** 
- Do you want to use a pre-existing Amazon Machine Image (AMI) for your instance? (e.g. Linux distribution, Windows Server, etc.) 
- Allow me to guide you through the different options available to you. 

**Additional Parameters** 
- You may also need to specify an instance type, which determines the computing resources assigned to your instance. 

**Confirmation of Command** 
Would you like me to provide the command to create the EC2 instance? 

USER: name the ec2 starter_ec2 with amazon linux engine and smallest instance type. create the key-pair and name it start-key and also create the security group and name it start-sg

'''

# Create the request body
request_body = {
    "prompt": prompt_text,
    "max_tokens": 400,
    "temperature": 0.15,
    "p": 0.01,
    "k": 50,
    "stop_sequences": []
}

# Convert request body to JSON string
body_str = json.dumps(request_body)

# Initialize the Bedrock client
bedrock_client = boto3.client(
    service_name='bedrock-runtime',
    region_name=region_name  # Ensure the correct AWS region
)

try:
    response = bedrock_client.invoke_model(
        body=body_str,
        modelId=model_id,
        accept=accept,
        contentType=content_type
    )

    # Properly read the body
    response_data = json.loads(response["body"].read())
    # print(response_data)
    # Extract the generated text
    generations = response_data.get("generations", [])[
        0].get('text', 'No text found')
    print(generations)

except NoCredentialsError:
    print("No AWS credentials found. Please set up your credentials.")
except ClientError as e:
    print(f"An error occurred: {e}")
