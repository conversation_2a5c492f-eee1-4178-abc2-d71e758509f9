import json
import boto3
from botocore.exceptions import NoCredentialsError, ClientError

# AWS Bedrock configuration
region_name = 'us-east-1'  # Change to your region if needed
# model_id = "cohere.command-r-plus-v1:0"  # Updated model ID
model_id = "cohere.command-r-v1:0"
accept = "*/*"  # Changed accept header
content_type = "application/json"


def print_formatted_chat_history(chat_history):
    for entry in chat_history:
        role = entry.get('role', 'UNKNOWN')
        message = entry.get('message', '')
        # Add a space between messages
        print(f"{role.upper()}: {message}\n")



prompt_text ='''Follow this guideLine
**Initial Response**
You will be interacting with the user and AWS.

**Gathering Essential Details**
When requesting resource creation, follow these steps:

1. Ask the user for essential details, including:
	* Resource name
	* Resource type
	* Initial parameters
2. If additional parameters are required, prompt the user to provide them before proceeding.
3. If response includes commands, no need to prompt the user for confirmation

**Crafting Creation Commands**
Only provide creation commands in the format `<<aws ...>>` once all necessary parameters have been gathered. Ensure each command is sent individually, without combining multiple commands in a single response.

**Command Order and Format**
Display commands in the order of dependent resources, using simple command formats. Send one command at a time, and use the format `<<aws ...>>` for clarity.

**Retrieving Parameters and Creating Resources**
To gather parameters and create resources, follow these steps:

1. Ask for the region, defaulting to the availability zone 'a' if the user specifies 'default'.
2. If 'default' is chosen, create a new security group for each service based on provided VPC data.
3. For dependent functions like EC2 key pairs, create them if 'default' is selected.
4. Use CLI commands in the format `<<aws ...>>` to retrieve parameters and create resources.
'''



# Create the request body
request_body = {
    "chat_history": [
        {"role": "USER", "message": prompt_text},
        {"role": "USER", "message": 'Create me an ec2'},
        {"role": "CHATBOT", "message": '''Sure! To create an EC2 instance, I'll need the following details:

- Resource name (a name for your EC2 instance)
- Resource type (specific type of EC2 instance, e.g. t2.micro, t3.medium, etc.)
- Initial parameters (details like the AMI ID, VPC, and subnet)

Can you provide me with this information to get started?

Generated Text:
Sure! To create an EC2 instance, I'll need the following details:

- Resource name (a name for your EC2 instance)
- Resource type (specific type of EC2 instance, e.g. t2.micro, t3.medium, etc.)
- Initial parameters (details like the AMI ID, VPC, and subnet)

Can you provide me with this information to get started?'''}
    ],
    "message": '''send the commands in order per text,name the ec2 starter_ec2 with amazon linux engine and smallest instance type. create the key-pair and name it start-key and also create the security group and name it start-sg'''
}

# Convert request body to JSON string
body_str = json.dumps(request_body)

# Initialize the Bedrock client
bedrock_client = boto3.client(
    service_name='bedrock-runtime',
    region_name=region_name  # Ensure the correct AWS region
)

try:
    response = bedrock_client.invoke_model(
        body=body_str,
        modelId=model_id,
        accept=accept,
        contentType=content_type
    )

    # Properly read the body
    response_data = json.loads(response["body"].read())
    print("Chat History:")
    print_formatted_chat_history(response_data['chat_history'])

    print("Generated Text:")
    print(response_data['text'])
    

except NoCredentialsError:
    print("No AWS credentials found. Please set up your credentials.")
except ClientError as e:
    print(f"An error occurred: {e}")






