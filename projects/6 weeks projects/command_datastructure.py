class Node:
    def __init__(self, command, place, order):
        self.command = command
        self.place = place
        self.order = order
        self.next = None


class LinkedList:
    def __init__(self):
        self.head = None

    def insert(self, command, place, order):
        new_node = Node(command, place, order)
        if not self.head:
            self.head = new_node
        else:
            current = self.head
            while current.next:
                current = current.next
            current.next = new_node

    def remove(self, command, place, order):
        if not self.head:
            return

        # If the node to be removed is the head
        if (self.head.command == command and
            self.head.place == place and
                self.head.order == order):
            self.head = self.head.next
            return

        # Traverse the list to find the node to remove
        current = self.head
        while current.next:
            if (current.next.command == command and
                current.next.place == place and
                    current.next.order == order):
                current.next = current.next.next
                return
            current = current.next

    def find_by_order(self, order):
        current = self.head
        results = []
        while current:
            if current.order == order:
                results.append(current.command)
            current = current.next
        return results

    def print_list(self):
        current = self.head
        while current:
            print(
                f"Command: {current.command}, Place: {current.place}, Order: {current.order}")
            current = current.next


# Example usage
# ll = LinkedList()
# ll.insert("start", 1, 10)
# ll.insert("process", 2, 20)
# ll.insert("end", 3, 30)
# ll.insert("check", 4, 10)
# ll.insert("finalize", 5, 20)

# print("Original list:")
# ll.print_list()

# ll.remove("process", 2, 20)

# print("\nList after removing 'process':")
# ll.print_list()

# ll.remove("start", 1, 10)

# print("\nList after removing 'start':")
# ll.print_list()

# orders_equal_to_10 = ll.find_by_order(10)
# print("\nNodes with order equal to 10:")
# for node in orders_equal_to_10:
#     print(f"Command: {node.command}, Place: {node.place}, Order: {node.order}")
