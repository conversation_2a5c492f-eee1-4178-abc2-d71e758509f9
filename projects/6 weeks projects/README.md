project_code/
├── app.py # Streamlit frontend (UI)
├── backend.py # Backend logic
├── cli_executor.py # CLI logic
├── main.py # Main entry point to run the Streamlit app
├── data/
│ ├── history/ # Historical data or logs
│ ├── given_information/ # Provided data or configurations
│ │ ├── cli_initial_prompt.txt # Initial CLI prompts or setup information
│ │ ├── initial_prompt.txt # General initial prompts or setup data
│ │ ├── vpc_details.txt # VPC configuration or details
│ └── cli_logs/ # Logs from CLI operations
├── README.md # Project description and instructions
├── requirements.txt # External dependencies
├── .gitignore # Git ignore file (if using Git)

# Project Readme

## Overview

This project aims to create a chat interface where users can interact with an AI to perform tasks such as creating EC2 instances on AWS.

## Checklist

1. **Use Boto3 to Connect to Bedrock:** (DONE)
2. **Create UI:** (DONE)
3. **Take Response from Bedrock and Run the Commands**
4. **Create Custom Response to the User**
5. **Take Service Parameters from Users upon Their First Prompt**

## Example Interaction

"user) create me a ec2"
"AI) give me the parameters such as ec2 name, instance type, AMI type, ..."
"user) name is test, type is t3.micro, AMI is ..."
"AI) Thank you. Please wait patiently until done..."
"AI) EC2 has been created. Is there anything further needed?"

## Dependencies

- aws account credentials
- Python 3.x
- Boto3
- streamlit

## Usage

- streamlit run app_ui.py
