import streamlit as st
import backend  # Import backend functions and helpers
import cli_executor  # Import CLI executor functions
from datetime import datetime
import time as t
# from datetime import *
# from time import *

# Call count management functions


def get_call_count():
    if "api_call_count" not in st.session_state:
        st.session_state.api_call_count = 0
    return st.session_state.api_call_count


def increment_call_count():
    st.session_state.api_call_count += 1


def reset_call_count():
    st.session_state.api_call_count = 0


def response_generator(response):
    sentences = response.split("\n\n")  # Split the response into sentences
    for sentence in sentences:
        words = sentence.split()
        for word in words:
            yield word + " "
            t.sleep(0.05)
        yield "\n\n"  # Add a newline after each sentence


def handle_ai_response(response, session_name):
    # Display the AI response first in the chat
    with st.chat_message("ai"):
        st.write_stream(response_generator(response))
    # Add the AI response to chat history
    st.session_state.messages.append({"role": "ai", "content": response})

    # Save the conversation after processing
    backend.save_conversation(session_name)

    # If the response contains CLI commands, process them
    if cli_executor.contains_double_ampersand(response):
        # Extract and execute CLI commands
        cli_results = cli_executor.process_and_get_results(response)

        # Add CLI results to conversation
        st.session_state.messages.append(
            {"role": "aws", "content": cli_results})

        # Display CLI results in the chat
        with st.chat_message("aws"):
            st.markdown(cli_results)

        # Get AI's interpretation of CLI results
        with st.spinner("Processing CLI results..."):
            additional_response = backend.gen(
                history=st.session_state.messages,
                prompt=f"I am AWS: If you encounter an errors, avoid reusing the same commands. Use &^ ^& to include any AWS CLI commands you want to run. Otherwise, do not use CLI syntax in your input.Here are the CLI command results. If a command returns placeholders, there might be missing information. If no results are shown, it could mean there's nothing to display or the command needs to be corrected.To proceed, you can either provide the missing information or suggest corrected commands, do not include --output in your commands. Here's what I got from the AWS CLI: {cli_results}", prompter="AWS"
            )

        # Display the additional AI response
        with st.chat_message("ai"):
            st.markdown(additional_response)

        # Add additional response to the conversation
        st.session_state.messages.append(
            {"role": "ai", "content": additional_response})

        # Save the conversation again after processing
        backend.save_conversation(session_name)

        # Recursive call if there's more CLI commands in additional_response
        if cli_executor.contains_double_ampersand(additional_response):
            handle_ai_response(additional_response, session_name)


# Main function for the Streamlit app
def main():
    st.sidebar.title("AWS Gen.ai")

    if st.sidebar.button("Logout"):
        backend.clear_session()
        reset_call_count()  # Reset the call count on logout

    if "session_name" not in st.session_state:
        st.session_state.session_name = datetime.now()

    session_name = st.session_state.session_name
    st.sidebar.title(session_name)

    if get_call_count() >= 50:
        st.sidebar.warning("Maximum API calls reached.")
        return

    if "messages" not in st.session_state:
        st.session_state.messages = []

    # Display previous messages from session state
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    prompt = st.chat_input("What's up?")

    if prompt:
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})

        # Display the user message
        with st.chat_message("user"):
            st.markdown(prompt)

        # Increment call count and generate AI response
        increment_call_count()
        with st.spinner("Generating..."):
            response = backend.gen(
                history=st.session_state.messages, prompt=prompt, prompter="USER")

        # Handle AI response, ensuring the generated text appears first
        handle_ai_response(response, session_name)


if __name__ == "__main__":
    main()
