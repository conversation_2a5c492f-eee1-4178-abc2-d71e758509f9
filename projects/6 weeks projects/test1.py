import streamlit as st
from datetime import datetime
import time as t
import backend
import cli_executor


# Constants
API_CALL_LIMIT = 50
WORD_DELAY = 0.0001

with open("reply.txt", 'r') as file:
    # Read the contents of the file
    reply = file.read()


def main(st, datetime, t, backend, cli_executor):
    # Set page layout to wide
    st.set_page_config(layout="wide")

    # Session state initialization
    initialize_session_state(st, datetime)

    # Sidebar setup
    st.sidebar.title("AWS Gen.ai")
    if st.sidebar.button("Logout"):
        backend.clear_session()
        reset_call_count(st)

    if get_call_count(st) >= API_CALL_LIMIT:
        st.sidebar.warning("Maximum API calls reached.")
        return

    session_name = st.session_state["session_name"]
    st.sidebar.title(f"Session: {session_name}")

    # Get user prompt input at the bottom of the left column
    prompt = st.chat_input("What's up?")

    # Use expandable content in the left column for chat messages

    # Display existing messages in a scrollable section
    for message in st.session_state["messages"]:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # Initialize response to ensure it has a value before use
    response = None

    if prompt:
        # Add user's message to the session state and display it
        st.session_state["messages"].append(
            {"role": "user", "content": prompt}
        )
        with st.chat_message("user"):
            st.markdown(prompt)

        increment_call_count(st)

        # Generate AI response
        with st.spinner("Generating..."):
            # response = backend.gen(
            #     history=st.session_state["messages"], prompt=prompt, prompter="USER"
            # )
            response = reply

    # Handle AI response if it exists
    if response:
        handle_ai_response(response, session_name, st)


def initialize_session_state(st, datetime):
    if "api_call_count" not in st.session_state:
        st.session_state["api_call_count"] = 0
    if "session_name" not in st.session_state:
        st.session_state["session_name"] = datetime.now().strftime(
            "%Y-%m-%d %H:%M:%S")
    if "messages" not in st.session_state:
        st.session_state["messages"] = []
    if "cli_commands" not in st.session_state:
        st.session_state["cli_commands"] = [
            "pip install streamlit",
            "streamlit run app.py",
            "conda install -c conda-forge numpy",
            "pip install streamlit",
            "streamlit run app.py",
            "conda install -c conda-forge numpy",
            "pip install streamlit",
            "streamlit run app.py",
            "conda install -c conda-forge numpy",
            "pip install streamlit",
            "streamlit run app.py",
            "conda install -c conda-forge numpy",
            "pip install streamlit",
            "streamlit run app.py",
            "conda install -c conda-forge numpy",
            "pip install streamlit",
            "streamlit run app.py",
            "conda install -c conda-forge numpy",
        ]


def get_call_count(st):
    return st.session_state["api_call_count"]


def increment_call_count(st):
    st.session_state["api_call_count"] += 1


def reset_call_count(st):
    st.session_state["api_call_count"] = 0


def display_commands_with_execution(st, commands):
    """
    Displays CLI commands with a button to execute each command.
    """
    for idx, command in enumerate(commands):
        col1, col2 = st.columns([4, 1])
        with col1:
            st.code(command, language='bash')
        with col2:
            if st.button(f"RUN", key=f"execute_{idx}"):
                st.success(f"Executed: {command}")


def response_generator(response, word_delay):
    """
    Generates text from a response with a delay to simulate typing.
    """
    for paragraph in response.split("\n\n"):
        for word in paragraph.split():
            yield word + " "
            t.sleep(word_delay)
        yield "\n\n"


def handle_ai_response(response, session_name, st):
    """
    Handles AI responses, updating session state and processing CLI commands if needed.
    """
    with st.chat_message("ai"):
        st.write_stream(response_generator(response, WORD_DELAY))
    #     display_commands_with_execution(
    #         st, cli_executor.extract_commands(response))

    # st.session_state["messages"].append({"role": "ai", "content": response})
    # backend.save_conversation(session_name)

    # if cli_executor.contains_double_ampersand(response):
    #     cli_results = cli_executor.process_and_get_results(response)

    #     with st.chat_message("aws"):
    #         st.markdown(cli_results)

    #     st.session_state["messages"].append(
    #         {"role": "aws", "content": cli_results})
    #     backend.save_conversation(session_name)

    #     with st.spinner("Processing CLI results..."):
    #         additional_response = backend.gen(
    #             history=st.session_state["messages"],
    #             prompt=f"Here's what the CLI command output returned: {cli_results}.",
    #             prompter="AWS",
    #         )

    #     with st.chat_message("ai"):
    #         st.markdown(additional_response)

    #     st.session_state["messages"].append(
    #         {"role": "ai", "content": additional_response})
    #     backend.save_conversation(session_name)

    #     if cli_executor.contains_double_ampersand(additional_response):
    #         handle_ai_response(additional_response, session_name, st)


if __name__ == "__main__":
    main(st, datetime, t, backend, cli_executor)
