from final_fine_tuning.program.src.data_processing.job_posting_generator import Job<PERSON>ostingGenerator
from final_fine_tuning.program.src.data_processing.cv_generator import CVGenerator
from final_fine_tuning.program.src.data_processing.cv_evaluator import CVEvaluator
from final_fine_tuning.program.src.utils.data_sorter import DataOrganizer
from final_fine_tuning.program.src.data_processing.job_data_processor import JobPostingsDataProcessor
from final_fine_tuning.program.src.utils.data_checker import DataChecker
from final_fine_tuning.program.src.utils.evaluations_combiner import EvaluationsCombiner

##################################################
##################################################
# VARIABLES
##################################################

start_chunk = 16
stop_chunk = 20

##################################################
##################################################
# DATA ORGANIZER
##################################################

type_P = "eval"  # job, cv, eval

# organizer = DataOrganizer(start_chunk=start_chunk,
#                           stop_chunk=stop_chunk, data_type=type_P)
# organizer.organize_data()

##################################################
##################################################
# DATA CHECKER
##################################################

# PATH
custom_path = 'data/evaluations/evaluations_chunk'
checker = DataChecker()

# Example checks:
# checker.check_num_jobs_in_chunks(custom_path, start_chunk, stop_chunk)
# checker.check_cvs_lists_length(custom_path, start_chunk, stop_chunk)

# Example operations with pulled indices:
pull_custom_path = 'data/job/job_postings_chunk'
delete_custom_path = 'data/cvs/cvs_chunk'

chunks = [3, 12, 13, 14, 15, 16, 17, 18, 19, 20]
indices_for_chunks = [
    [5, 7, 9, 17, 19],
    [5, 7, 15],
    [3, 4, 11],
    [3, 8, 20],
    [17, 21],
    [6, 22],
    [15, 17],
    [22],
    [4, 12, 14],
    [7, 13, 16, 20]
]

# DataChecker.pull_indices_from_chunks(chunks, indices_for_chunks, pull_custom_path)
# DataChecker.delete_indices_from_chunks(chunks, indices_for_chunks, delete_custom_path)

# Example evaluation:
# checker.evaluate_cvs_in_chunks(custom_path, start_chunk, stop_chunk)

##################################################
##################################################
# EVALUATIONS COMBINER
##################################################

combiner = EvaluationsCombiner(start_chunk=start_chunk,
                               stop_chunk=stop_chunk,
                               input_prefix_path="data/evaluations/evaluations_chunk",
                               output_file_path="data/final/final.json")
combiner.combine_evaluations()
