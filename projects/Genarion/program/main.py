from src.data_processing.job_data_processor import JobPostingsDataProcessor
from src.data_processing.job_posting_generator import JobPostingGenerator
from src.data_processing.cv_generator import CVGenerator
from src.data_processing.cv_evaluator import CVEvaluator


##################################################
##################################################
# VARIABLES
##################################################

start_chunk = 5
stop_chunk = 5


##################################################
##################################################
# DATAFRAME GENERATION
##################################################

num_chunks = 20  # Set your desired number of chunks

processor = JobPostingsDataProcessor(num_chunks)
processor.process()

##################################################
##################################################
# JOB POSTER GENERATION
##################################################

input_raw_prefix_path = "data/raw/chunks/data_chunk"
output_prefix_path = "data/job/job_postings_chunk"

generator = JobPostingGenerator(start_chunk=start_chunk,
                                stop_chunk=stop_chunk,
                                input_prefix_path=input_raw_prefix_path,
                                output_prefix_path=output_prefix_path)
generator.process_job_postings()

##################################################
##################################################
# CV GENERATION
##################################################

input_prefix_path = "data/job/job_postings_chunk"
output_prefix_path = "data/cvs/cvs_chunk"

generator = CVGenerator(start_chunk=start_chunk,
                        stop_chunk=stop_chunk,
                        input_prefix_path=input_prefix_path,
                        output_prefix_path=output_prefix_path)
generator.process_cv_generation()

##################################################
##################################################
# EVAL GENERATION
##################################################

input_prefix_path = "data/cvs/cvs_chunk"
output_prefix_path = "data/evaluations/evaluations_chunk"

evaluator = CVEvaluator(start_chunk=start_chunk,
                        stop_chunk=stop_chunk,
                        input_prefix_path=input_prefix_path,
                        output_prefix_path=output_prefix_path)
evaluator.process_evaluations()
