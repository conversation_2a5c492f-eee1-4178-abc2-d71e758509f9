import os
import json
import logging


class DataChecker:
    def __init__(self):
        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(message)s')

    @staticmethod
    def check_num_jobs_in_chunks(path_prefix, start_chunk=1, stop_chunk=20):
        for chunk_number in range(start_chunk, stop_chunk + 1):
            json_file_path = f'{path_prefix}_{chunk_number}.json'

            try:
                with open(json_file_path, 'r') as file:
                    data = json.load(file)

                num_jobs = len(data.get('data', []))

                if num_jobs == 25:
                    logging.info(
                        f"Chunk {chunk_number}: Perfect! There are exactly 25 jobs.")
                else:
                    logging.info(
                        f"Chunk {chunk_number}: There are {num_jobs} jobs, not 25.")
                    missing_indices = [i for i in range(25) if i not in [
                        job['index'] for job in data.get('data', [])]]
                    logging.info(
                        f"Chunk {chunk_number}: Missing indices: {missing_indices}")

            except FileNotFoundError:
                logging.warning(f"{json_file_path} does not exist.")
            except json.JSONDecodeError:
                logging.error(f"Error decoding JSON from {json_file_path}.")

    @staticmethod
    def check_cvs_lists_length(path_prefix, start_chunk=1, stop_chunk=20):
        for chunk_number in range(start_chunk, stop_chunk + 1):
            json_file_path = f'{path_prefix}_{chunk_number}.json'

            try:
                with open(json_file_path, 'r') as file:
                    data = json.load(file)

                for job in data.get('data', []):
                    if len(job.get('cvs', [])) != 5:
                        logging.info(
                            f'Chunk {chunk_number}, index {job["index"]} does not have 5 elements. Length: {len(job["cvs"])}')
            except FileNotFoundError:
                logging.warning(f"{json_file_path} does not exist.")
            except json.JSONDecodeError:
                logging.error(f"Error decoding JSON from {json_file_path}.")

    @staticmethod
    def process_chunks(path_prefix, start_chunk=1, stop_chunk=20):
        for chunk_number in range(start_chunk, stop_chunk + 1):
            json_file_path = f'{path_prefix}_{chunk_number}.json'
            failed_folder = os.path.join(
                os.path.dirname(json_file_path), 'failed')
            os.makedirs(failed_folder, exist_ok=True)
            failed_file_path = os.path.join(
                failed_folder, f'cv_failed_chunk_{chunk_number}.json')

            try:
                with open(json_file_path, 'r') as file:
                    data = json.load(file)

                cv_failed = []
                filtered_jobs = []

                for job in data.get('data', []):
                    if len(job.get('cvs', [])) != 5:
                        job.pop('cvs', None)
                        cv_failed.append(job)
                    else:
                        filtered_jobs.append(job)

                data['data'] = filtered_jobs

                with open(json_file_path, 'w') as file:
                    json.dump(data, file, indent=4)

                if cv_failed:
                    with open(failed_file_path, 'w') as failed_file:
                        json.dump({"data": cv_failed}, failed_file, indent=4)

                logging.info(
                    f"Processed {json_file_path} and saved failed jobs to {failed_file_path}")

            except FileNotFoundError:
                logging.warning(f"{json_file_path} does not exist.")
            except json.JSONDecodeError:
                logging.error(f"Error decoding JSON from {json_file_path}.")
            except Exception as e:
                logging.error(
                    f"Unexpected error processing {json_file_path}: {e}")

    @staticmethod
    def pull_indices_from_chunks(chunks, indices_for_chunks, path_prefix):
        output_folder = os.path.join(
            os.path.dirname(path_prefix), 'pulled_indices')
        os.makedirs(output_folder, exist_ok=True)

        for chunk_number, indices in zip(chunks, indices_for_chunks):
            json_file_path = f'{path_prefix}_{chunk_number}.json'
            pulled_file_path = os.path.join(
                output_folder, f'pulled_chunk_{chunk_number}.json')

            try:
                with open(json_file_path, 'r') as file:
                    data = json.load(file)

                pulled_data = [job for job in data.get(
                    'data', []) if job['index'] in indices]

                if pulled_data:
                    with open(pulled_file_path, 'w') as pulled_file:
                        json.dump({"data": pulled_data}, pulled_file, indent=4)
                    logging.info(
                        f"Pulled indices from {json_file_path} and saved to {pulled_file_path}")
                else:
                    logging.info(
                        f"No matching indices found in {json_file_path}")

            except FileNotFoundError:
                logging.warning(f"{json_file_path} does not exist.")
            except json.JSONDecodeError:
                logging.error(f"Error decoding JSON from {json_file_path}.")
            except Exception as e:
                logging.error(
                    f"Unexpected error processing {json_file_path}: {e}")

    @staticmethod
    def delete_indices_from_chunks(chunks, indices_for_chunks, path_prefix):
        for chunk_number, indices in zip(chunks, indices_for_chunks):
            json_file_path = f'{path_prefix}_{chunk_number}.json'

            try:
                with open(json_file_path, 'r') as file:
                    data = json.load(file)

                data['data'] = [job for job in data.get(
                    'data', []) if job['index'] not in indices]

                with open(json_file_path, 'w') as file:
                    json.dump(data, file, indent=4)

                logging.info(f"Deleted indices from {json_file_path}")

            except FileNotFoundError:
                logging.warning(f"{json_file_path} does not exist.")
            except json.JSONDecodeError:
                logging.error(f"Error decoding JSON from {json_file_path}.")
            except Exception as e:
                logging.error(
                    f"Unexpected error processing {json_file_path}: {e}")

    @staticmethod
    def evaluate_cvs_in_chunks(path_prefix, start_chunk=1, stop_chunk=20):
        for chunk_number in range(start_chunk, stop_chunk + 1):
            json_file_path = f'{path_prefix}_{chunk_number}.json'

            try:
                with open(json_file_path, 'r') as file:
                    data = json.load(file)

                for job in data.get('data', []):
                    results = []

                    for i, cv_data in enumerate(job.get('cvs', []), start=1):
                        cv_num = i
                        cv = cv_data.get(f'cv_{cv_num}', [])
                        evaluation = cv_data.get('evaluation', '')

                        if len(cv) <= 150 or len(evaluation) <= 150:
                            results.append(f'    cv_{cv_num} FAILED')
                        else:
                            results.append(f'    cv_{cv_num} PASS')

                    if any('FAILED' in result for result in results):
                        logging.info(
                            f'Chunk {chunk_number}, Index number {job["index"]} FAILED')
                        for result in results:
                            if 'FAILED' in result:
                                logging.info(result)
                        logging.info('-' * 20)

            except FileNotFoundError:
                logging.warning(f"{json_file_path} does not exist.")
            except json.JSONDecodeError:
                logging.error(f"Error decoding JSON from {json_file_path}.")

    @staticmethod
    def combine_pulled_indices(normal_prefix_path, pulled_prefix_path, start_chunk=1, stop_chunk=20):
        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(message)s')

        for chunk_num in range(start_chunk, stop_chunk + 1):
            normal_file = f"{normal_prefix_path}_{chunk_num}.json"
            pulled_file = f"{pulled_prefix_path}_{chunk_num}.json"

            if not os.path.exists(normal_file):
                logging.warning(f"{normal_file} does not exist.")
                continue

            if not os.path.exists(pulled_file):
                logging.warning(f"{pulled_file} does not exist.")
                continue

            try:
                with open(normal_file, "r") as n_file:
                    normal_data = json.load(n_file)

                with open(pulled_file, "r") as p_file:
                    pulled_data = json.load(p_file)

                normal_data_dict = {job['index']                                    : job for job in normal_data['data']}

                for pulled_job in pulled_data['data']:
                    normal_data_dict[pulled_job['index']] = pulled_job

                combined_data = {"data": list(normal_data_dict.values())}

                with open(normal_file, "w") as n_file:
                    json.dump(combined_data, n_file, indent=4)

                logging.info(
                    f"Combined data for chunk {chunk_num} saved to {normal_file}.")

                # Optionally, you can delete the pulled file after merging
                os.remove(pulled_file)
                logging.info(
                    f"Deleted pulled file {pulled_file} after merging.")

            except FileNotFoundError as e:
                logging.warning(f"File not found: {e}")
            except json.JSONDecodeError as e:
                logging.error(f"Error decoding JSON: {e}")
            except Exception as e:
                logging.error(f"Unexpected error: {e}")

# Example usage:
# chunks = [0, 3, 5, 6]
# indices_for_chunks = [[2, 4], [3, 6], [6, 8, 6, 4], [2, 5, 8]]

# DataChecker.pull_indices_from_chunks(
#     chunks, indices_for_chunks, 'path/to/chunks/prefix')
# DataChecker.delete_indices_from_chunks(
#     chunks, indices_for_chunks, 'path/to/chunks/prefix')
# DataChecker.combine_pulled_indices(
#     'data/cvs/cvs_chunk', 'data/cvs/pulled_indices/cvs_chunk')
