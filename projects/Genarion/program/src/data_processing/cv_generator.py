import os
import json
import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dotenv import load_dotenv
from openai import OpenAI


class CVGenerator:
    def __init__(self, start_chunk, stop_chunk, input_prefix_path, output_prefix_path, concurrent_jobs=4):
        self.start_chunk = start_chunk
        self.stop_chunk = stop_chunk
        self.concurrent_jobs = concurrent_jobs
        self.input_prefix_path = input_prefix_path
        self.output_prefix_path = output_prefix_path
        self.model = "gpt-4o"
        load_dotenv()
        self.api_key = os.getenv("OPENAI_KEY")
        if not self.api_key:
            raise ValueError(
                "API key is missing. Set it in your .env file as 'OPENAI_KEY'.")
        self.client = OpenAI(api_key=self.api_key)
        self.setup_logging()

    def setup_logging(self):
        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(message)s')

    def make_api_call(self, prompt):
        try:
            response = self.client.chat.completions.create(
                messages=[{"role": "user", "content": prompt}],
                model=self.model,
            )
            return response.choices[0].message.content
        except Exception as e:
            logging.error(f"API call failed: {e}")
            return None

    def generate_5_cvs(self, job_posting):
        prompt = f"""
        Create 5 unique CVs for different candidates based on the following job posting: {job_posting}. Ensure that the candidates' work experiences, sectors, and education show slight differences (around 30%-50%) from the desired qualifications in the job postings. Make some CVs fail the evaluation for this job and some pass. Autofill information with random data if needed to make it look real. Separate CVs by using ***** on a new line between each CV.
        """
        return self.make_api_call(prompt)

    def split_cvs(self, cv_response):
        return cv_response.split("*****") if cv_response else []

    def create_cvs_for_job_posting(self, index, job_posting_data):
        job_posting = job_posting_data["job_poster"]
        cv_response = self.generate_5_cvs(job_posting)
        cvs = self.split_cvs(cv_response)
        if not cvs:
            logging.error(
                f"Failed to generate CVs for job posting: {job_posting}")
            return index, job_posting_data, None

        logging.info(f"Generated CVs for job posting {index + 1}.")
        return index, job_posting_data, cvs

    def process_cv_generation(self):
        for chunk_num in range(self.start_chunk, self.stop_chunk + 1):
            cvs_data = []

            input_file = f"{self.input_prefix_path}_{chunk_num}.json"
            if not os.path.exists(input_file):
                logging.warning(
                    f"{input_file} does not exist. Skipping chunk {chunk_num}.")
                continue

            try:
                with open(input_file, "r") as file:
                    job_postings_data = json.load(file)
            except json.JSONDecodeError:
                logging.error(
                    f"Error decoding JSON from {input_file}. Skipping chunk {chunk_num}.")
                continue

            with ThreadPoolExecutor(max_workers=self.concurrent_jobs) as executor:
                futures = [executor.submit(self.create_cvs_for_job_posting, index, job_posting)
                           for index, job_posting in enumerate(job_postings_data["data"])]

                for future in as_completed(futures):
                    index, job_posting_data, cvs = future.result()
                    if cvs:
                        job_posting_data["cvs"] = [cv.strip()
                                                   for cv in cvs if cv.strip()]
                        cvs_data.append(job_posting_data)

            output_data = {"data": cvs_data}
            output_file = f"{self.output_prefix_path}_{chunk_num}.json"

            with open(output_file, "w") as file:
                json.dump(output_data, file, indent=4)

            logging.info(
                f"All CVs data for chunk {chunk_num} saved to {output_file}.")
