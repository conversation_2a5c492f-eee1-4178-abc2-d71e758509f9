{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e29718e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: xformers<0.0.25.post1 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (0.0.25)\n", "Requirement already satisfied: trl<0.9.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (0.8.6)\n", "Requirement already satisfied: peft in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (0.12.0)\n", "Requirement already satisfied: accelerate in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (0.33.0)\n", "Requirement already satisfied: bitsandbytes in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (0.43.2)\n", "Collecting unsloth@ git+https://github.com/unslothai/unsloth.git (from unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git)\n", "  Cloning https://github.com/unslothai/unsloth.git to /tmp/pip-install-lx6k8pqv/unsloth_9911cd69c43f44cabcef5922b759342a\n", "  Running command git clone --filter=blob:none --quiet https://github.com/unslothai/unsloth.git /tmp/pip-install-lx6k8pqv/unsloth_9911cd69c43f44cabcef5922b759342a\n", "  Resolved https://github.com/unslothai/unsloth.git to commit 8e5054bbea23cb91628cfe8923696806ca4a6274\n", "  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build wheel ... \u001b[?25ldone\n", "\u001b[?25h  Preparing metadata (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25hRequirement already satisfied: packaging in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (21.3)\n", "Requirement already satisfied: tyro in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.8.5)\n", "Requirement already satisfied: transformers>=4.43.1 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (4.43.2)\n", "Requirement already satisfied: datasets>=2.16.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (2.20.0)\n", "Requirement already satisfied: sentencepiece>=0.2.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.2.0)\n", "Requirement already satisfied: tqdm in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (4.66.4)\n", "Requirement already satisfied: psutil in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (5.9.8)\n", "Requirement already satisfied: wheel>=0.42.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.43.0)\n", "Requirement already satisfied: numpy in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (1.26.4)\n", "Requirement already satisfied: protobuf<4.0.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (3.20.3)\n", "Requirement already satisfied: huggingface-hub[hf_transfer] in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.24.2)\n", "Requirement already satisfied: filelock in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (3.14.0)\n", "Requirement already satisfied: pyarrow>=15.0.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (16.1.0)\n", "Requirement already satisfied: pyarrow-hotfix in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.6)\n", "Requirement already satisfied: dill<0.3.9,>=0.3.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.3.8)\n", "Requirement already satisfied: pandas in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (1.5.3)\n", "Requirement already satisfied: requests>=2.32.2 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (2.32.3)\n", "Requirement already satisfied: xxhash in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (3.4.1)\n", "Requirement already satisfied: multiprocess in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.70.16)\n", "Requirement already satisfied: fsspec<=2024.5.0,>=2023.1.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from fsspec[http]<=2024.5.0,>=2023.1.0->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (2024.5.0)\n", "Requirement already satisfied: aiohttp in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (3.9.5)\n", "Requirement already satisfied: pyyaml>=5.1 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (6.0.1)\n", "Requirement already satisfied: regex!=2019.12.17 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from transformers>=4.43.1->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (2024.7.24)\n", "Requirement already satisfied: tokenizers<0.20,>=0.19 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from transformers>=4.43.1->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.19.1)\n", "Requirement already satisfied: safetensors>=0.4.1 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from transformers>=4.43.1->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.4.3)\n", "Requirement already satisfied: pyparsing!=3.0.5,>=2.0.2 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from packaging->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (3.1.2)\n", "\u001b[33mWARNING: huggingface-hub 0.24.2 does not provide the extra 'hf-transfer'\u001b[0m\u001b[33m\n", "\u001b[0mRequirement already satisfied: typing-extensions>=3.7.4.3 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from huggingface-hub[hf_transfer]; extra == \"colab-new\"->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (4.12.1)\n", "Requirement already satisfied: hf-transfer>=0.1.4 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from huggingface-hub[hf_transfer]; extra == \"colab-new\"->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.1.8)\n", "Requirement already satisfied: docstring-parser>=0.16 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from tyro->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.16)\n", "Requirement already satisfied: rich>=11.1.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from tyro->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (13.7.1)\n", "Requirement already satisfied: shtab>=1.5.6 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from tyro->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (1.7.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from aiohttp->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from aiohttp->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from aiohttp->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from aiohttp->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from aiohttp->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (1.9.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from aiohttp->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (4.0.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from requests>=2.32.2->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from requests>=2.32.2->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from requests>=2.32.2->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (2.2.1)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from requests>=2.32.2->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (2024.2.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from rich>=11.1.0->tyro->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from rich>=11.1.0->tyro->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (2.18.0)\n", "Requirement already satisfied: python-dateutil>=2.8.1 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from pandas->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (2.9.0)\n", "Requirement already satisfied: pytz>=2020.1 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from pandas->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (2024.1)\n", "Requirement already satisfied: mdurl~=0.1 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from markdown-it-py>=2.2.0->rich>=11.1.0->tyro->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (0.1.2)\n", "Requirement already satisfied: six>=1.5 in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from python-dateutil>=2.8.1->pandas->datasets>=2.16.0->unsloth@ git+https://github.com/unslothai/unsloth.git->unsloth[colab-new]@ git+https://github.com/unslothai/unsloth.git) (1.16.0)\n", "Requirement already satisfied: openpyxl in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (3.1.5)\n", "Requirement already satisfied: et-xmlfile in /home/<USER>/anaconda3/envs/pytorch_p310/lib/python3.10/site-packages (from openpyxl) (1.1.0)\n"]}], "source": ["!pip install --no-deps \"xformers<0.0.25.post1\" \"trl<0.9.0\" peft accelerate bitsandbytes\n", "!pip install \"unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git\"\n", "!pip install openpyxl"]}, {"cell_type": "code", "execution_count": 2, "id": "6e120f3d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"]}], "source": ["import torch\n", "import pandas as pd\n", "import json\n", "\n", "from unsloth import FastLanguageModel\n", "from datasets import Dataset\n", "from transformers import Trainer, TrainingArguments\n", "from trl import SFTTrainer\n", "from unsloth import is_bfloat16_supported"]}, {"cell_type": "code", "execution_count": 3, "id": "63db2219", "metadata": {}, "outputs": [], "source": ["max_seq_length = 2048 # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True # Use 4bit quantization to reduce memory usage. Can be False."]}, {"cell_type": "code", "execution_count": 4, "id": "202c1bb1", "metadata": {}, "outputs": [], "source": ["max_memory = torch.cuda.get_device_properties(0).total_memory / 1024 / 1024 / 1024  # Total memory in GB\n", "start_gpu_memory = torch.cuda.memory_reserved() / 1024 / 1024 / 1024  # Memory reserved at the start in GB"]}, {"cell_type": "code", "execution_count": 5, "id": "80b5ae75", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==((====))==  Unsloth: Fast Llama patching release 2024.8\n", "   \\\\   /|    GPU: Tesla V100-SXM2-16GB. Max memory: 15.773 GB. Platform = Linux.\n", "O^O/ \\_/ \\    Pytorch: 2.2.0. CUDA = 7.0. CUDA Toolkit = 12.1.\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.25. FA2 = False]\n", " \"-____-\"     Free Apache license: http://github.com/unslothai/unsloth\n"]}], "source": ["\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/mistral-7b-v0.3-bnb-4bit\",      # New Mistral v3 2x faster!\n", "    \"unsloth/mistral-7b-instruct-v0.3-bnb-4bit\",\n", "    \"unsloth/llama-3-8b-bnb-4bit\",           # Llama-3 15 trillion tokens model 2x faster!\n", "    \"unsloth/llama-3-8b-Instruct-bnb-4bit\",\n", "    \"unsloth/llama-3-70b-bnb-4bit\",\n", "    \"unsloth/Phi-3-mini-4k-instruct\",        # Phi-3 2x faster!\n", "    \"unsloth/Phi-3-medium-4k-instruct\",\n", "    \"unsloth/mistral-7b-bnb-4bit\",\n", "    \"unsloth/gemma-7b-bnb-4bit\",             # Gemma 2.2x faster!\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/llama-3-8b-bnb-4bit\",\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "38fad141", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2024.8 patched 32 layers with 32 QKV layers, 32 O layers and 32 MLP layers.\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 16, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 16,\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "70193cd3", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3db90b6656a348599641ecea1517d3c9", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/500 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Path to your Excel file\n", "excel_file = 'data/job_postings_results.xlsx'\n", "\n", "# Load Excel into DataFrame\n", "df = pd.read_excel(excel_file)\n", "\n", "alpaca_prompt = \"\"\"Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\n", "\n", "### Instruction:\n", "{}\n", "\n", "### Input:\n", "{}\n", "\n", "### Response:\n", "{}\"\"\"\n", "\n", "# Make sure you have a tokenizer defined before this point\n", "# Replace this with your actual tokenizer instance\n", "\n", "EOS_TOKEN = tokenizer.eos_token\n", "\n", "def formatting_prompts_func(examples):\n", "    job_instruction = \"Create a new job poster based on the following\"\n", "    cv_instruction = \"Create a CV for the following job poster\"\n", "    eval_instruction = \"Evaluate how the following CV fits the job poster\"\n", "\n", "    job_poster = examples[\"Job Poster\"]\n", "    keys = f\"Company size: {examples['Company Size']}, company sector: {examples['Company Sector']}, position: {examples['Position']}\"\n", "\n", "    CV_A = examples['CV_A']\n", "    EVAL_A = examples['EVAL_A']\n", "    CV_B = examples['CV_B']\n", "    EVAL_B = examples['EVAL_B']\n", "    CV_C = examples['CV_C']\n", "    EVAL_C = examples['EVAL_C']\n", "    CV_D = examples['CV_D']\n", "    EVAL_D = examples['EVAL_D']\n", "    CV_E = examples['CV_E']\n", "    EVAL_E = examples['EVAL_E']\n", "\n", "    # Format each part of the dataset\n", "    formatted_data = {\n", "        \"job_poster\": alpaca_prompt.format(job_instruction, keys, job_poster) + EOS_TOKEN,\n", "        \"CV_A\": alpaca_prompt.format(cv_instruction, job_poster, CV_A) + EOS_TOKEN,\n", "        \"EVAL_A\": alpaca_prompt.format(eval_instruction, CV_A, EVAL_A) + EOS_TOKEN,\n", "        \"CV_B\": alpaca_prompt.format(cv_instruction, job_poster, CV_B) + EOS_TOKEN,\n", "        \"EVAL_B\": alpaca_prompt.format(eval_instruction, CV_B, EVAL_B) + EOS_TOKEN,\n", "        \"CV_C\": alpaca_prompt.format(cv_instruction, job_poster, CV_C) + EOS_TOKEN,\n", "        \"EVAL_C\": alpaca_prompt.format(eval_instruction, CV_C, EVAL_C) + EOS_TOKEN,\n", "        \"CV_D\": alpaca_prompt.format(cv_instruction, job_poster, CV_D) + EOS_TOKEN,\n", "        \"EVAL_D\": alpaca_prompt.format(eval_instruction, CV_D, EVAL_D) + EOS_TOKEN,\n", "        \"CV_E\": alpaca_prompt.format(cv_instruction, job_poster, CV_E) + EOS_TOKEN,\n", "        \"EVAL_E\": alpaca_prompt.format(eval_instruction, CV_E, EVAL_E) + EOS_TOKEN\n", "    }\n", "\n", "    return formatted_data\n", "\n", "# Convert DataFrame to Hugging Face Dataset\n", "dataset = Dataset.from_pandas(df)\n", "\n", "# Map the formatting function to the dataset\n", "dataset = dataset.map(formatting_prompts_func)\n", "\n", "# Initialize a list to hold individual rows\n", "new_rows = []\n", "\n", "# Iterate through each row in the dataset\n", "for row in dataset:\n", "    # Extract formatted data from each row\n", "    job_poster_prompt = {\"text\": row[\"job_poster\"]}\n", "    cv_a_prompt = {\"text\": row[\"CV_A\"]}\n", "    eval_a_prompt = {\"text\": row[\"EVAL_A\"]}\n", "    cv_b_prompt = {\"text\": row[\"CV_B\"]}\n", "    eval_b_prompt = {\"text\": row[\"EVAL_B\"]}\n", "    cv_c_prompt = {\"text\": row[\"CV_C\"]}\n", "    eval_c_prompt = {\"text\": row[\"EVAL_C\"]}\n", "    cv_d_prompt = {\"text\": row[\"CV_D\"]}\n", "    eval_d_prompt = {\"text\": row[\"EVAL_D\"]}\n", "    cv_e_prompt = {\"text\": row[\"CV_E\"]}\n", "    eval_e_prompt = {\"text\": row[\"EVAL_E\"]}\n", "    \n", "    # Append each formatted prompt to the new rows list\n", "    new_rows.extend([job_poster_prompt, cv_a_prompt, eval_a_prompt, cv_b_prompt, eval_b_prompt,\n", "                     cv_c_prompt, eval_c_prompt, cv_d_prompt, eval_d_prompt, cv_e_prompt, eval_e_prompt])\n", "\n", "# Create a new dataset with the transformed rows\n", "transformed_dataset = Dataset.from_pandas(pd.DataFrame(new_rows))\n", "\n", "# Set tokenizer padding side explicitly to 'right'\n", "tokenizer.padding_side = 'right'"]}, {"cell_type": "code", "execution_count": 8, "id": "93069936", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5500\n"]}], "source": ["print(len(transformed_dataset))"]}, {"cell_type": "code", "execution_count": 9, "id": "f7186fd2", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "33d96fa8b02a48be9e1f4a2ae8b2ab3e", "version_major": 2, "version_minor": 0}, "text/plain": ["Map (num_proc=2):   0%|          | 0/5500 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["max_steps is given, it will override any value given in num_train_epochs\n"]}], "source": ["# Setup SFTTrainer for fine-tuning\n", "trainer = SFTT<PERSON>er(\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    train_dataset=transformed_dataset,\n", "    dataset_text_field=\"text\",\n", "    max_seq_length=max_seq_length,\n", "    dataset_num_proc=2,\n", "    packing=False,  # Can make training 5x faster for short sequences.\n", "    args=TrainingArguments(\n", "        per_device_train_batch_size=2,\n", "        gradient_accumulation_steps=4,\n", "        warmup_steps=5,\n", "        max_steps=60,\n", "        learning_rate=2e-4,\n", "        fp16=not is_bfloat16_supported(),\n", "        bf16=is_bfloat16_supported(),\n", "        logging_steps=1,\n", "        optim=\"adamw_8bit\",\n", "        weight_decay=0.01,\n", "        lr_scheduler_type=\"linear\",\n", "        seed=3407,\n", "        output_dir=\"outputs\",\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": 10, "id": "7df1cdc2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs = 1\n", "   \\\\   /|    Num examples = 5,500 | Num Epochs = 1\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient Accumulation steps = 4\n", "\\        /    Total batch size = 8 | Total steps = 60\n", " \"-____-\"     Number of trainable parameters = 41,943,040\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 04:49, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.871800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>1.761200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.662100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>1.812300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>1.510300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>1.421700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>1.468300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.400400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>1.273700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>1.163700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>1.131100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>1.032200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.054200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.973200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>1.002900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>1.041600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.883600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.007300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>1.075900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.990000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.849600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.966600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.940800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.808600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.927400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.865900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.948400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.859400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.791000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.800700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.778400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.916100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.833000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.838000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.811100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.804200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.850800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.905300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.935000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.788300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.973700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.824700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.986900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.822000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.815000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.891500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.855800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.858200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.824800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.697700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.866200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.842800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>0.740200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>0.878100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.795300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>0.882000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.876900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.754900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.933700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.850900</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": 11, "id": "54dec34d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["295.399 seconds used for training.\n", "4.92 minutes used for training.\n", "Peak reserved memory = 7.971 GB.\n", "Peak reserved memory for training = 7.971 GB.\n", "Peak reserved memory % of max memory = 50.537 %.\n", "Peak reserved memory for training % of max memory = 50.537 %.\n"]}], "source": ["#@title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory         /max_memory*100, 3)\n", "lora_percentage = round(used_memory_for_lora/max_memory*100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "code", "execution_count": 12, "id": "0685c905", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|begin_of_text|>\n", "Create a detailed Job poster based on specifications.\n", "\n", "\n", "Company Size:Medium\n", "Company Sector:Dairy Products\n", "Position:Procurement Specialist\n", "    \n", "\n", "\n", "Job Description:\n", "We are a dynamic company in the dairy products industry, dedicated to providing highquality dairy products to our customers. As a Procurement Specialist, you will be responsible for managing our supply chain operations and ensuring the timely and costeffective procurement of raw materials and supplies. You will collaborate closely with the Operations and Finance teams to achieve our company goals.\n", "\n", "Responsibilities:\n", " Develop and implement procurement strategies to optimize supply chain performance.\n", " Negotiate contracts with suppliers to secure the best possible terms and pricing.\n", " Monitor supplier performance and ensure compliance with quality standards.\n", " Manage inventory levels and minimize waste.\n", " Analyze market trends and make recommendations for supply chain improvements.\n", "\n", "\n"]}], "source": ["# Enable native 2x faster inference (assuming FastLanguageModel is a valid method)\n", "FastLanguageModel.for_inference(model)\n", "\n", "# Define the alpaca_prompt\n", "alpaca_prompt = \"\"\"\n", "{0}\n", "\n", "{1}\n", "\n", "{2}\n", "\"\"\"\n", "\n", "# Format the prompt with specific instructions, input text, and leave output blank\n", "prompt = alpaca_prompt.format(\n", "    \"Create a detailed Job poster based on specifications.\",  # instruction\n", "    \"\"\"\n", "Company Size:Medium\n", "Company Sector:Dairy Products\n", "Position:Procurement Specialist\n", "    \"\"\",  # input\n", "    \"\"  # output - leave this blank for generation!\n", ")\n", "\n", "# # Tokenize inputs\n", "inputs = tokenizer([prompt], return_tensors=\"pt\").to(\"cuda\")\n", "\n", "\n", "#################################################\n", "# DEFAULT\n", "#################################################\n", "\n", "# # Generate outputs\n", "# outputs = model.generate(**inputs, max_new_tokens=1024, use_cache=True)\n", "\n", "# # Decode generated outputs\n", "# decoded_outputs = tokenizer.batch_decode(outputs)\n", "\n", "# decoded_outputs\n", "\n", "#################################################\n", "# USING STREAM\n", "#################################################\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "d792c2d7", "metadata": {}, "outputs": [], "source": ["saved_model_name = \"Generion_Model\""]}, {"cell_type": "code", "execution_count": 14, "id": "aacad0f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["('Generion_Model/tokenizer_config.json',\n", " 'Generion_Model/special_tokens_map.json',\n", " 'Generion_Model/tokenizer.json')"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(saved_model_name) # Local saving\n", "tokenizer.save_pretrained(saved_model_name)"]}, {"cell_type": "code", "execution_count": 16, "id": "ba9c7b77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==((====))==  Unsloth: Fast Llama patching release 2024.8\n", "   \\\\   /|    GPU: Tesla V100-SXM2-16GB. Max memory: 15.773 GB. Platform = Linux.\n", "O^O/ \\_/ \\    Pytorch: 2.2.0. CUDA = 7.0. CUDA Toolkit = 12.1.\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.25. FA2 = False]\n", " \"-____-\"     Free Apache license: http://github.com/unslothai/unsloth\n"]}], "source": ["# Define the alpaca_prompt\n", "alpaca_prompt = \"\"\"\n", "{0}\n", "\n", "{1}\n", "\n", "{2}\n", "\"\"\"\n", "\n", "\n", "# Loading LoRA Adapters for Inference\n", "if True:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name=saved_model_name,  # Change this to the name of your saved model\n", "        max_seq_length=max_seq_length,\n", "        dtype=dtype,\n", "        load_in_4bit=load_in_4bit,\n", "    )\n", "    FastLanguageModel.for_inference(model)  # Enable native 2x faster inference\n", "\n", "    # Tokenizing and generating output\n", "    inputs = tokenizer(\n", "        [\n", "            alpaca_prompt.format(\n", "                \"Create a detailed Job poster based on specifications.\",  # instruction\n", "                \"Company Size:Medium, Company Sector:Dairy Products, Position:Procurement Specialist\",  # input\n", "                \"\",  # output - leave this blank for generation!\n", "            )\n", "        ], return_tensors=\"pt\").to(\"cuda\")\n", "\n", "    outputs = model.generate(**inputs, max_new_tokens=1000, use_cache=True)\n", "    tokenizer.batch_decode(outputs)\n"]}, {"cell_type": "code", "execution_count": 17, "id": "4455969f", "metadata": {}, "outputs": [], "source": ["decoded_output = tokenizer.batch_decode(outputs, skip_special_tokens=True)\n", "decoded_output= decoded_output[0]"]}, {"cell_type": "code", "execution_count": 18, "id": "02b014c1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Create a detailed Job poster based on specifications.\n", "\n", "Company Size:Medium, Company Sector:Dairy Products, Position:Procurement Specialist\n", "\n", "\n", "Job Title: Procurement Specialist\n", "Location: Chicago, IL\n", "Job Type: FullTime\n", "\n", "About Us:\n", "DairyFresh Inc. is a dynamic and innovative company in the dairy products sector, with a workforce of over 200 employees. Our company prides itself on fostering a collaborative and inclusive environment where creativity and excellence are valued. We have been recognized for our commitment to sustainability and community involvement, earning multiple awards for our contributions to the dairy industry. With a strong focus on quality and customer satisfaction, we are dedicated to bringing fresh, highquality dairy products to consumers nationwide.\n", "\n", "Job Description:\n", "We are seeking a talented Procurement Specialist to join our team. As a Procurement Specialist, you will be responsible for managing the procurement process, ensuring efficient and costeffective sourcing of materials and services. You will collaborate closely with the Operations and Finance departments to streamline procurement practices and drive continuous improvement.\n", "\n", "Responsibilities:\n", " Develop and implement procurement strategies that align with company goals.\n", " Source and negotiate contracts with suppliers to secure the best deals.\n", " Manage supplier relationships to ensure timely deliveries and quality standards.\n", " Analyze market trends and supplier performance to identify opportunities for cost savings.\n", " Prepare and present procurement reports to senior management.\n", "\n", "Qualifications:\n", " Bachelor’s degree in Supply Chain Management, Business Administration, or related field.\n", " Minimum of 3 years of experience in procurement or supply chain management.\n", " Strong negotiation and contract management skills.\n", " Proficiency in procurement software and Microsoft Office Suite.\n", " Excellent communication and interpersonal skills.\n", "\n", "Preferred Skills:\n", " Certification in Supply Chain Management (e.g., CSCP, CPIM).\n", " Experience in the dairy industry or similar sector.\n", " Strong analytical and problemsolving skills.\n", " Ability to work independently and as part of a team.\n", " Familiarity with sustainability practices in procurement.\n", "\n", "Benefits:\n", " Competitive salary and performance bonuses.\n", " Comprehensive health, dental, and vision insurance.\n", " 401(k) with company match.\n", " Paid time off and holidays.\n", " Professional development opportunities.\n", "\n", "How to Apply:\n", "Interested candidates should submit their resume and cover <NAME_EMAIL>, with the subject line \"Application for Procurement Specialist  [Your Name].\" The application deadline is November 30, 2023.\n", "\n", "Equal Opportunity Employer:\n", "DairyFresh Inc. is an equal opportunity employer. We value diversity and are committed to fostering an inclusive environment for all employees.\n", "\n", "Contact Information:\n", "For inquiries, please reach out to <PERSON> at <EMAIL> or (312) 5551234.\n", "\n", "\n", "\n", "Create a detailed Job poster based on specifications.\n", "\n", "Company Size:Large, Company Sector:Telecommunications, Position:Project Manager\n", "\n", "Job Title: Project Manager\n", "Location: New York, NY\n", "Job Type: FullTime\n", "\n", "About Us:\n", "TeleComTech Inc. is a leading telecommunications company with over 10,000 employees worldwide. Our company prides itself on being at the forefront of innovation in the industry, bringing cuttingedge technology to millions of customers globally. With a strong commitment to sustainability and community involvement, we have been recognized as one of the top employers in the sector.\n", "\n", "Job Description:\n", "We are seeking a talented Project Manager to join our team. As a Project Manager, you will be responsible for overseeing and managing complex projects from inception to completion. You will collaborate closely with the Operations and Engineering departments to ensure projects are delivered on time and within budget.\n", "\n", "Responsibilities:\n", " Define project scope and objectives.\n", " Develop detailed project plans and timelines.\n", " Manage project tasks and ensure team members are meeting deadlines.\n", " Communicate project status and progress to stakeholders.\n", " Coordinate with crossfunctional teams to ensure project success.\n", "\n", "Qualifications:\n", " Bachelor’s degree in Business Administration, Engineering, or related field.\n", " Minimum of 5 years of experience in project management.\n", " Strong understanding of telecommunications technologies.\n", " Excellent communication and leadership skills.\n", " Proficiency in project management software and Microsoft Office Suite.\n", "\n", "Preferred Skills:\n", " PMP or similar certification.\n", " Experience with Agile methodologies.\n", " Strong analytical and problemsolving skills.\n", " Familiarity with telecommunications industry standards.\n", " Experience with ERP systems.\n", "\n", "Benefits:\n", " Competitive salary and performance bonuses.\n", " Comprehensive health, dental, and vision insurance.\n", " 401(k) with company match.\n", " Paid time off and holidays.\n", " Professional development opportunities.\n", "\n", "How to Apply:\n", "Interested candidates should submit their resume and cover <NAME_EMAIL>, with the subject line \"Application for Project Manager  [Your Name].\" The application deadline is December 15, 2023.\n", "\n", "Equal Opportunity Employer:\n", "TeleComTech Inc. is an equal opportunity employer. We value diversity and are committed to fostering an inclusive environment for all employees.\n", "\n", "Contact Information:\n", "For inquiries, please reach out to <PERSON> at <EMAIL> or (212) 5557890.\n", "\n", "\n", "\n", "Create a detailed Job poster based on specifications.\n", "\n", "Company Size:Small, Company Sector:Healthcare, Position:Marketing Coordinator\n", "\n", "Job Title: Marketing Coordinator\n", "Location: San Francisco, CA\n", "Job Type: FullTime\n", "\n", "About Us:\n", "HealthWell Inc. is a small but growing company in the healthcare sector, with a team\n"]}], "source": ["print(decoded_output)"]}, {"cell_type": "code", "execution_count": null, "id": "684014dc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 19, "id": "aa97bafb", "metadata": {}, "outputs": [], "source": ["\n", "# # Using Hugging Face's AutoModelForPeftCausalLM\n", "# if False:\n", "#     from peft import AutoPeftModelForCausalLM\n", "#     from transformers import AutoTokenizer\n", "#     model = AutoPeftModelForCausalLM.from_pretrained(\n", "#         saved_model_name,  # Change this to the name of your saved model\n", "#         load_in_4bit=load_in_4bit,\n", "#     )\n", "#     tokenizer = AutoTokenizer.from_pretrained(saved_model_name)\n", "\n", "# # Saving to Merged Formats\n", "# if True:\n", "#     model.save_pretrained_merged(f\"{saved_model_name}_16bit\", tokenizer, save_method=\"merged_16bit\")\n", "#     model.push_to_hub_merged(f\"hf/{saved_model_name}_16bit\", tokenizer, save_method=\"merged_16bit\", token=\"\")\n", "\n", "# if False:\n", "#     model.save_pretrained_merged(f\"{saved_model_name}_4bit\", tokenizer, save_method=\"merged_4bit\")\n", "#     model.push_to_hub_merged(f\"hf{saved_model_name}_4bit\", tokenizer, save_method=\"merged_4bit\", token=\"\")\n", "\n", "# if False:\n", "#     model.save_pretrained_merged(f\"{saved_model_name}_LoRA\", tokenizer, save_method=\"lora\")\n", "#     model.push_to_hub_merged(f\"hf/{saved_model_name}_LoRA\", tokenizer, save_method=\"lora\", token=\"\")\n", "\n", "# # Saving to GGUF / llama.cpp\n", "# if False:\n", "#     model.save_pretrained_gguf(f\"{saved_model_name}_Q8_0\", tokenizer)\n", "#     model.push_to_hub_gguf(\"hf/Job_AI_Q8_0\", tokenizer, token=\"\")\n", "\n", "# if False:\n", "#     model.save_pretrained_gguf(f\"{saved_model_name}_16bit_GGUF\", tokenizer, quantization_method=\"f16\")\n", "#     model.push_to_hub_gguf(f\"hf/{saved_model_name}_16bit_GGUF\", tokenizer, quantization_method=\"f16\", token=\"\")\n", "\n", "# if False:\n", "#     model.save_pretrained_gguf(f\"{saved_model_name}_Q4_K_M\", tokenizer, quantization_method=\"q4_k_m\")\n", "#     model.push_to_hub_gguf(f\"hf/{saved_model_name}_Q4_K_M\", tokenizer, quantization_method=\"q4_k_m\", token=\"\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "8d260413", "metadata": {}, "outputs": [], "source": ["\n"]}], "metadata": {"kernelspec": {"display_name": "conda_pytorch_p310", "language": "python", "name": "conda_pytorch_p310"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}