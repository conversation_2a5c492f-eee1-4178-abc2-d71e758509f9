# Report on Data Generation, Processing, and Evaluation

## 1. Data Generation

### 1.1 Creating and Splitting Data

- **Purpose:** Develop a robust dataset for training and evaluation.
- **Process:**
  - **Generating Data:** Create pairs of job postings and CVs, then split them into training and evaluation sets.
  - **Chunking:** Divide the training data into 20 parts for easier management, while keeping the evaluation data intact.

### 1.2 Making Job Postings and CVs

- **Purpose:** Simulate real-world job postings and CVs.
- **Process:**
  - **Job Postings:** Use AI to generate 500 realistic job postings.
  - **CVs:** Create 5 unique CVs for each job posting to ensure variety.

### 1.3 Evaluating CVs

- **Purpose:** Assess how well each CV matches the job postings.
- **Process:**
  - **Evaluation:** Evaluate each CV for its relevance to the job postings and record the results for later analysis.

## 2. Data Processing

### 2.1 Organizing the Data

- **Purpose:** Prepare data for easy analysis.
- **Process:**
  - **JSON Structuring:** Organize data into 20 JSON files, each containing job details, CVs, and evaluation results.
  - **Indexing:** Include information such as job category, company size, sector, position, and related CVs and evaluations in each file.

### 2.2 Cleaning and Finalizing Data

- **Purpose:** Ensure data accuracy and consistency.
- **Process:**
  - **Cleaning Scripts:** Use scripts to validate and clean the data.
  - **Merging:** Combine all data chunks into a final dataset and perform a final round of cleaning.

## 3. Evaluation and Final Report

### 3.1 Assessing Data Quality

- **Purpose:** Evaluate the effectiveness of the data.
- **Process:**
  - **Metrics:** Measure how well CVs match job postings and analyze the results.

### 3.2 Summarizing and Documenting

- **Purpose:** Provide a clear summary of the process and results.
- **Process:**
  - **Report:** Compile results into an easy-to-understand report with clear data presentation and analysis.

---

---

# Fine-Tuning and Deploying Models with `unsloth`

#### Overview of `unsloth`

`unsloth` is a powerful library for fine-tuning language models, offering:

1. **Faster Fine-Tuning:** Accelerates training for quicker results.
2. **Optimized Performance:** Enhances model accuracy and efficiency.
3. **Broad Model Support:** Compatible with a wide range of model architectures.

#### Key Components and How to Use Them

**1. Hyperparameters**

Fine-tuning involves adjusting parameters like learning rates and batch sizes. `unsloth` allows customization for better performance.

**2. `FastLanguageModel`**

This class helps train language models quickly and efficiently, handling large models and datasets.

```python
model = FastLanguageModel(model_name="unsloth/llama-3-8b-bnb-4bit")
```

**3. Data Setup**

Prepare and load your dataset with `unsloth`:

```python
transformed_dataset = Dataset.from_pandas(pd.DataFrame(new_rows))
```

**4. `SFTTrainer`**

Manage the training process with `SFTTrainer`:

```python
trainer = SFTTrainer(model=model, args=training_args, train_dataset=train_data)
trainer.train()
```

**5. Saving the Model**

    **[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!

```python
model.save_pretrained("lora_model") # Local saving
tokenizer.save_pretrained("lora_model")
```

    After training, save the model as Qlora and related files in the`Generion_Model/` directory:

- **`adapter_config.json`**: Adapter configuration.
- **`adapter_model.safetensors`**: Adapter weights.
- **`README.md`**: Documentation.
- **`special_tokens_map.json`**: Special tokens mapping.
- **`tokenizer_config.json`**: Tokenizer settings.
- **`tokenizer.json`**: Tokenizer vocabulary.

# Loading Model

Needs to be customized for .py

```python
# Install necessary packages
!pip install --no-deps "xformers<0.0.25.post1" "trl<0.9.0" peft accelerate bitsandbytes
!pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
!pip install openpyxl

import torch
from unsloth import FastLanguageModel
from transformers import Trainer, TrainingArguments

# Configuration
max_seq_length = 2048  # Adjust as needed
dtype = None  # None for auto detection; specify for different hardware
load_in_4bit = True  # Use 4-bit quantization to reduce memory usage

# Unsloth initialization
print("==((====))== Unsloth: Fast Llama patching release 2024.8")
print("\\ /| GPU: Tesla V100-SXM2-16GB. Max memory: 15.773 GB. Platform = Linux.")
print("O^O/ \\_/ \\ Pytorch: 2.2.0. CUDA = 7.0. CUDA Toolkit = 12.1.")
print("\\ / Bfloat16 = FALSE. FA [Xformers = 0.0.25. FA2 = False]")
print("\"-____-\" Free Apache license: http://github.com/unslothai/unsloth")

# Define the alpaca_prompt
alpaca_prompt = """
{0}
{1}
{2}
"""

# Load model and tokenizer
model, tokenizer = FastLanguageModel.from_pretrained(
    model_name="Generion_Model",  # Change this to your model name
    max_seq_length=max_seq_length,
    dtype=dtype,
    load_in_4bit=load_in_4bit
)

FastLanguageModel.for_inference(model)  # Enable native 2x faster inference

# Tokenize and generate output
inputs = tokenizer(
    [alpaca_prompt.format(
        "Create a detailed Job poster based on specifications.",  # instruction
        "Company Size: Medium, Company Sector: Dairy Products, Position: Procurement Specialist",  # input
        ""  # output - leave this blank for generation
    )],
    return_tensors="pt"
).to("cuda")

outputs = model.generate(**inputs, max_new_tokens=1000, use_cache=True)
decoded_output = tokenizer.batch_decode(outputs, skip_special_tokens=True)[0]

# Display the generated output
print(decoded_output)

```
