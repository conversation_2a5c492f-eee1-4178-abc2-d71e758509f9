## Extra

multi agents, multi role, train etmisler, seperate models,


Çıktıları okudum. CV yorumlaması fazla pozitif gibi görünüyor. Adayın güçlü yanları kadar zayıf yanlarını ve olası eksikliklerini de yorumlayacak şekilde revize edilirse promt, daha iyi çıktılar alınabilir.

### LINKS

- https://www.datacamp.com/tutorial/fine-tuning-llama-2

## STEPS

### Step 1: Creating Job Postings

**Objective:** Prepare 500 different job postings using predefined variables.

**Actions:**

1. **Determine Variables:**

   - **Company Size:** Create categories for small, medium, and large companies.
   - **Company Sector:** Define the sectors as Software, Home Appliances, Furniture, Consumer Electronics, Dairy Products, Agriculture, Telecommunications, Energy, Logistics, Healthcare.
   - **Position:** Define the list of positions:
     - Accounting Manager
     - Financial Analyst
     - Marketing Manager
     - Human Resources Specialist
     - Sales Representative
     - Project Manager
     - Business Development Specialist
     - Information Technology (IT) Manager
     - Customer Service Manager
     - Product Manager
     - Legal Advisor
     - Procurement Specialist
     - Digital Marketing Specialist
     - Logistics and Supply Chain Manager
     - Business Intelligence Analyst
     - Social Media Manager
     - Corporate Communications Specialist
     - R&D Engineer
     - Training and Development Specialist
     - Strategic Planning Specialist
2. **Create Job Postings:**

   - Use combinations of the variables to generate 500 unique job postings. Ensure that each combination of company size, sector, and position is utilized.
   - Number each job posting from 1 to 500.

**Example:**

- Job Posting 1: Small Software Company looking for an Accounting Manager.
- Job Posting 2: Medium Home Appliances Company looking for a Financial Analyst.
- ...

### Step 2: Creating CVs for Each Job Posting

**Objective:** Generate CVs for 5 different fictional candidates for each of the 500 job postings.

**Actions:**

1. **Design CV Templates:**

   - Create a template for CVs that includes sections like Personal Information, Education, Work Experience, Skills, and Certifications.
2. **Generate CVs:**

   - For each job posting, create 5 different CVs labeled A, B, C, D, and E.
   - Ensure that the candidates' work experiences, sectors, and education show slight differences (around 20%) from the desired qualifications in the job postings.
3. **Label CVs:**

   - Label each CV with the job posting number and the candidate label, e.g., 350A, 350B, etc.

**Example:**

- For Job Posting 1 (Small Software Company looking for an Accounting Manager):
  - 1A: Candidate with relevant accounting experience in a small software company.
  - 1B: Candidate with accounting experience in a medium-sized home appliances company.
  - ...

### Step 3: Evaluating the CVs

**Objective:** Provide evaluation summaries for each of the 2,500 CVs based on predefined criteria.

**Actions:**

1. **Define Evaluation Criteria:**

   - Relevance of the candidate's previous positions to the advertised position.
   - Relevance of the candidate's degree and education to the advertised position.
   - Relevance of the candidate's previous companies to the sector of the company advertising the position.
   - Relevance of the candidate's previous companies to the size of the company advertising the position.
   - Overall fit of the candidate's experience and skills with the job requirements and company profile.
2. **Evaluate CVs:**

   - For each CV, write an evaluation summary addressing the criteria listed above.
   - Comment on whether the candidate is suitable or not, providing reasons.
3. **Label Evaluations:**

   - Label each evaluation with the job posting number, candidate label, and an "R" for review, e.g., 350AR, 350BR, etc.

**Example:**

- Evaluation for CV 1A:
  - Position Relevance: High
  - Education Relevance: Moderate
  - Sector Relevance: High
  - Company Size Relevance: High
  - Overall Fit: Suitable
  - Comments: Candidate 1A has extensive experience in accounting within the software sector and has worked in small companies similar to ours. Education aligns moderately with the requirements.

### Final Output

**Objective:** Create training datasets consisting of job postings, CVs, and evaluations.

**Actions:**

1. **Compile Data:**

   - Each training dataset will consist of:
     - 1 Job Posting
     - 5 CVs for the job posting
     - 5 Evaluations for the CVs
2. **Number and Organize:**

   - Organize the datasets in sets of 11 outputs per job posting (1 job posting + 5 CVs + 5 evaluations).
   - Ensure each set is clearly labeled and numbered accordingly.

**Example:**

- Training Dataset for Job Posting 1:
  - Job Posting 1
  - CVs: 1A, 1B, 1C, 1D, 1E
  - Evaluations: 1AR, 1BR, 1CR, 1DR, 1ER

By following these steps, you will systematically create a comprehensive dataset with 500 different job postings, each with 5 corresponding candidate CVs and evaluations, resulting in a total of 5,500 outputs.
