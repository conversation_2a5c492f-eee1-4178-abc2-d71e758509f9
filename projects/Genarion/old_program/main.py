from job_poster import JobPostingGenerator
from cv_gen import CVGenerator
from eval import CVEvaluator
from org import DataOrganizer
from data_gen import JobPostingsDataProcessor
from data_checker import DataChecker


##################################################
##################################################
# VARIABLES
##################################################

start_chunk = 11
stop_chunk = 20
##################################################
##################################################
# DATAFRAME GENERATION
##################################################

num_chunks = 20  # Set your desired number of chunks

# processor = JobPostingsDataProcessor(num_chunks)
# processor.process()

##################################################
##################################################
# JOB POSTER GENERATION
##################################################

# input_raw_prefix_path = "data/raw/chunks/data_chunk"
# output_prefix_path = "data/job/job_postings_chunk"

# generator = JobPostingGenerator(start_chunk=start_chunk,
#                                 stop_chunk=stop_chunk, input_prefix_path=input_raw_prefix_path, output_prefix_path=output_prefix_path)
# # generator.process_job_postings()

##################################################
##################################################
# CV GENERATION
##################################################
# input_prefix_path = "data/job/pulled_indices/pulled_chunk"
# output_prefix_path = "data/cvs/pulled_indices/cvs_chunk"
# input_prefix_path = "data/job/job_postings_chunk"
# output_prefix_path = "data/cvs/cvs_chunk"
# generator = CVGenerator(start_chunk=start_chunk, stop_chunk=stop_chunk,
#                         input_prefix_path=input_prefix_path, output_prefix_path=output_prefix_path)
# generator.process_cv_generation()

##################################################
##################################################
##################################################
# EVAL GENERATION
##################################################


input_prefix_path = "data/cvs/cvs_chunk"
output_prefix_path = "data/evaluations/evaluations_chunk"
evaluator = CVEvaluator(start_chunk=start_chunk,stop_chunk=stop_chunk, input_prefix_path=input_prefix_path, output_prefix_path=output_prefix_path)
evaluator.process_evaluations()
