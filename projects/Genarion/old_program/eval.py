import os
import json
import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dotenv import load_dotenv
from openai import OpenAI


class CVEvaluator:
    def __init__(self, start_chunk, stop_chunk, input_prefix_path, output_prefix_path, concurrent_jobs=4):
        self.start_chunk = start_chunk
        self.stop_chunk = stop_chunk
        self.concurrent_jobs = concurrent_jobs
        self.input_prefix_path = input_prefix_path
        self.output_prefix_path = output_prefix_path
        self.evaluations_data = {}
        self.MODEL = "gpt-4o"
        load_dotenv()
        self.api_key = os.getenv("OPENAI_KEY")
        if not self.api_key:
            raise ValueError(
                "API key is missing. Set it in your .env file as 'OPENAI_KEY'.")
        self.client = OpenAI(api_key=self.api_key)
        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(message)s')

    def make_api_call(self, prompt):
        try:
            response = self.client.chat.completions.create(
                messages=[{"role": "user", "content": prompt}],
                model=self.MODEL,
            )
            return response.choices[0].message.content
        except Exception as e:
            logging.error(f"API call failed: {e}")
            return None

    def evaluate_cv(self, combined_cvs, job_posting):
        prompt_with_cvs = f"""
        Evaluate the following CVs against the job posting: {job_posting}. Provide detailed evaluations including how well the candidates match the job requirements and qualifications, as well as any areas where they fall short. Summarize the evaluations with recommendations on whether these candidates should be shortlisted for interviews. Separate evals by using ***** on a new line between each evaluation.
        Example:

        *****
        Eval_1:
        - **Position Relevance:** High
        - **Education Relevance:** Moderate
        - **Sector Relevance:** High
        - **Company Size Relevance:** High
        - **Overall Fit:** Suitable
        - **Comments:** Candidate 1A has extensive experience in accounting within the software sector and has worked in small companies similar to ours. Education aligns moderately with the requirements.
        *****
        Eval_2:
        content ...
        *****
        Eval_3:
        content ...
        *****
        Eval_4:
        content ...
        *****
        Eval_5:
        content ...
        *****

        CVs to evaluate
        {combined_cvs}
        """
        return self.make_api_call(prompt_with_cvs)

    def split_evaluations(self, response):
        return response.split("*****") if response else []

    def evaluate_cvs_for_job_posting(self, index, job_posting_data):
        job_posting = job_posting_data["job_poster"]
        combined_cvs = "\n".join(job_posting_data["cvs"])
        evaluations = []

        # Make API call to evaluate CVs
        evaluation_response = self.evaluate_cv(combined_cvs, job_posting)

        if evaluation_response:
            # Split evaluation response into separate evaluations
            evaluations = self.split_evaluations(evaluation_response)
            evaluations = [eval.strip()
                           for eval in evaluations if eval.strip()]

            # Prepare data for storage
            cvs_with_evaluations = []
            for i, cv in enumerate(job_posting_data["cvs"]):
                cv_data = {"cv_" + str(i + 1): cv}
                if i < len(evaluations):
                    cv_data["evaluation"] = evaluations[i]
                else:
                    cv_data["evaluation"] = ""
                cvs_with_evaluations.append(cv_data)

            # Store the evaluation response and other relevant data in the evaluations_data dictionary
            self.evaluations_data[index + 1] = {
                "company_size": job_posting_data["company_size"],
                "company_sector": job_posting_data["company_sector"],
                "position": job_posting_data["position"],
                "job_poster": job_posting,
                "cvs": cvs_with_evaluations,
            }
        else:
            logging.error(
                f"Failed to evaluate CVs for job posting: {job_posting}")

        logging.info(f"Evaluated CVs for job posting {index + 1}.")
        return index, evaluations if evaluations else evaluations

    def process_evaluations(self):
        for chunk_num in range(self.start_chunk, self.stop_chunk + 1):
            # Reset evaluations_data for this chunk
            self.evaluations_data = {}

            # Read CVs from JSON file
            input_file = f"{self.input_prefix_path}_{chunk_num}.json"
            with open(input_file, "r") as file:
                cvs_data = json.load(file)

            # Using ThreadPoolExecutor for concurrent processing of evaluations
            with ThreadPoolExecutor(max_workers=self.concurrent_jobs) as executor:
                futures = [executor.submit(self.evaluate_cvs_for_job_posting, index, job_posting)
                           for index, job_posting in enumerate(cvs_data["data"])]

                for future in as_completed(futures):
                    index, evaluations = future.result()
                    if evaluations:
                        self.evaluations_data[index + 1] = evaluations

            # Organize evaluation data by index and convert to the desired format
            sorted_evaluations_data = []
            for index in range(len(cvs_data["data"])):
                if index + 1 in self.evaluations_data:
                    job_posting_data = cvs_data["data"][index]
                    cvs_with_evaluations = []
                    for i, cv in enumerate(job_posting_data["cvs"]):
                        cv_data = {"cv_" + str(i + 1): cv}
                        if i < len(self.evaluations_data[index + 1]):
                            cv_data["evaluation"] = self.evaluations_data[index + 1][i]
                        else:
                            cv_data["evaluation"] = ""
                        cvs_with_evaluations.append(cv_data)

                    sorted_evaluations_data.append({
                        "index": index + 1,
                        "company_size": job_posting_data["company_size"],
                        "company_sector": job_posting_data["company_sector"],
                        "position": job_posting_data["position"],
                        "job_poster": job_posting_data["job_poster"],
                        "cvs": cvs_with_evaluations,
                    })
                else:
                    logging.error(
                        f"No evaluation data found for index {index + 1}")

            # Save all evaluation data for this chunk to a single JSON file
            output_evaluations_data = {"data": sorted_evaluations_data}
            output_evaluations_file = f"{self.output_prefix_path}_{chunk_num}.json"
            with open(output_evaluations_file, "w") as file:
                json.dump(output_evaluations_data, file, indent=4)

            logging.info(
                f"All evaluations data for chunk {chunk_num} saved to {output_evaluations_file}.")


# if __name__ == "__main__":
#     evaluator = CVEvaluator(start_chunk=0, stop_chunk=4,
#                             input_prefix_path="data/cvs/cvs_chunk",
#                             output_prefix_path="data/evaluations/evaluations_chunk")
#     evaluator.process_evaluations()
