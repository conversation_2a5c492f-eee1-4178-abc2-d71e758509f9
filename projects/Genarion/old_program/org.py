import os
import json
import logging


class DataOrganizer:
    def __init__(self, start_chunk=1, stop_chunk=5, data_type="eval"):
        self.start_chunk = start_chunk
        self.stop_chunk = stop_chunk
        self.data_type = data_type.lower()  # Ensure data_type is lowercase

        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(message)s')

    def organize_data(self):
        if self.data_type == "eval":
            base_path = "data/evaluations/evaluations_chunk_"
        elif self.data_type == "cv":
            base_path = "data/cvs/cvs_chunk_"
        elif self.data_type == "job":
            base_path = "data/job/job_postings_chunk_"
        else:
            logging.error(f"Invalid data type: {self.data_type}")
            return

        for chunk_num in range(self.start_chunk, self.stop_chunk + 1):
            input_file = f"{base_path}{chunk_num}.json"

            if not os.path.exists(input_file):
                logging.warning(f"{input_file} does not exist.")
                continue

            with open(input_file, "r") as file:
                data = json.load(file)

            # Sort the data based on the 'index'
            sorted_data = sorted(data["data"], key=lambda x: x["index"])

            # Save the sorted data back to the same input file path
            with open(input_file, "w") as file:
                json.dump({"data": sorted_data}, file, indent=4)

            logging.info(
                f"Organized {self.data_type} data for chunk {chunk_num} saved to {input_file}.")
