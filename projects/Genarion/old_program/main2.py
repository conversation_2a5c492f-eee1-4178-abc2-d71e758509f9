from job_poster import JobPostingGenerator
from cv_gen import CVGenerator
from eval import CVEvaluator
from org import DataOrganizer
from data_gen import JobPostingsDataProcessor
from data_checker import DataChecker
from combine import EvaluationsCombiner


##################################################
##################################################
# VARIABLES
##################################################

# maino = 7
# start_chunk = maino
# stop_chunk = maino


start_chunk = 16
stop_chunk = 20
##################################################
##################################################
# DATA organizer
##################################################

type_P = "eval"  # job , cv, eval

# organizer = DataOrganizer(start_chunk=start_chunk,
#                           stop_chunk=stop_chunk, data_type=type_P)
# organizer.organize_data()

##################################################
##################################################
##################################################
# DATA CHECKER
##################################################


# PATH
##################################################
# custom_path = 'data/job/job_postings_chunk'
# custom_path = 'data/cvs/cvs_chunk'
custom_path = 'data/evaluations/evaluations_chunk'
# custom_path = "data/cvs/pulled_indices/cvs_chunk"
checker = DataChecker()


# check number of job = 25
#################################################
# checker.check_num_jobs_in_chunks(custom_path, start_chunk, stop_chunk)

# check cvs = 5
##################################################
# checker.check_cvs_lists_length(custom_path, start_chunk, stop_chunk)


pull_custom_path = 'data/job/job_postings_chunk'
delete_custom_path = 'data/cvs/cvs_chunk'


chunks = [3, 12, 13, 14, 15, 16, 17, 18, 19, 20]
indices_for_chunks = [
    [5, 7, 9, 17, 19],
    [5, 7, 15],
    [3, 4, 11],
    [3, 8, 20],
    [17, 21],
    [6, 22],
    [15, 17],
    [22],
    [4, 12, 14],
    [7, 13, 16, 20]
]


# DataChecker.pull_indices_from_chunks(chunks, indices_for_chunks, pull_custom_path)
# DataChecker.delete_indices_from_chunks(
#     chunks, indices_for_chunks, delete_custom_path)


# check cv and eval in cvs > 256 char
##################################################
# checker.evaluate_cvs_in_chunks(custom_path, start_chunk, stop_chunk)


# DataChecker.combine_pulled_indices(
#     'data/cvs/cvs_chunk', 'data/cvs/pulled_indices/cvs_chunk')
combiner = EvaluationsCombiner(start_chunk=1, stop_chunk=20,
                               input_prefix_path="data/evaluations/evaluations_chunk",
                               output_file_path="data/final/final.json")
combiner.combine_evaluations()

