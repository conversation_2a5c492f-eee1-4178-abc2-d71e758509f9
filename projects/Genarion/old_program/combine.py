import os
import json
import logging


class EvaluationsCombiner:
    def __init__(self, start_chunk, stop_chunk, input_prefix_path, output_file_path):
        self.start_chunk = start_chunk
        self.stop_chunk = stop_chunk
        self.input_prefix_path = input_prefix_path
        self.output_file_path = output_file_path
        self.combined_evaluations = []

    def combine_evaluations(self):
        current_index = 0

        for chunk_num in range(self.start_chunk, self.stop_chunk + 1):
            input_file = f"{self.input_prefix_path}_{chunk_num}.json"
            if not os.path.exists(input_file):
                logging.error(f"File not found: {input_file}")
                continue

            with open(input_file, "r") as file:
                chunk_data = json.load(file)
                for item in chunk_data["data"]:
                    item["index"] = current_index
                    self.combined_evaluations.append(item)
                    current_index += 1

            logging.info(
                f"Chunk {chunk_num} processed. Current index: {current_index}")

        self.save_combined_evaluations()

    def save_combined_evaluations(self):
        output_data = {"data": self.combined_evaluations}
        with open(self.output_file_path, "w") as file:
            json.dump(output_data, file, indent=4)
        logging.info(f"Combined evaluations saved to {self.output_file_path}")


# if __name__ == "__main__":
#     combiner = EvaluationsCombiner(start_chunk=0, stop_chunk=4,
#                                    input_prefix_path="data/evaluations/evaluations_chunk",
#                                    output_file_path="data/evaluations/combined_evaluations.json")
#     combiner.combine_evaluations()
