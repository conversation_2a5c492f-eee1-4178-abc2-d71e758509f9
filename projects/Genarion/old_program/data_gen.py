import os
import random
import pandas as pd
from itertools import product
from dotenv import load_dotenv
import logging

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


class JobPostingsDataProcessor:
    COMPANY_SIZES = ["Small", "Medium", "Large"]
    COMPANY_SECTORS = [
        "Software", "Home Appliances", "Furniture", "Consumer Electronics",
        "Dairy Products", "Agriculture", "Telecommunications", "Energy",
        "Logistics", "Healthcare"
    ]
    POSITIONS = [
        "Accounting Manager", "Financial Analyst", "Marketing Manager",
        "Human Resources Specialist", "Sales Representative", "Project Manager",
        "Business Development Specialist", "Information Technology (IT) Manager",
        "Customer Service Manager", "Product Manager", "Legal Advisor",
        "Procurement Specialist", "Digital Marketing Specialist",
        "Logistics and Supply Chain Manager", "Business Intelligence Analyst",
        "Social Media Manager", "Corporate Communications Specialist",
        "R&D Engineer", "Training and Development Specialist",
        "Strategic Planning Specialist"
    ]

    def __init__(self, num_chunks):
        self.num_chunks = num_chunks
        self.client = None
        self.api_key = None
        self.all_df = None
        self.training_df = None
        self.eval_df = None
        self.load_api_key()
        self.generate_data()

    def load_api_key(self):
        load_dotenv()
        self.api_key = os.getenv("OPENAI_KEY")
        if not self.api_key:
            raise ValueError(
                "API key is missing. Set it in your .env file as 'OPENAI_KEY'.")

    def generate_data(self):
        combinations = list(
            product(self.COMPANY_SIZES, self.COMPANY_SECTORS, self.POSITIONS))
        random.shuffle(combinations)

        all_data = {
            "Company Size": [combo[0] for combo in combinations],
            "Company Sector": [combo[1] for combo in combinations],
            "Position": [combo[2] for combo in combinations]
        }

        self.all_df = pd.DataFrame(all_data)
        self.training_df = self.all_df.head(500)
        self.eval_df = self.all_df.tail(len(self.all_df) - 500)

    def split_into_chunks(self, df):
        chunk_size = len(df) // self.num_chunks
        return [df.iloc[i * chunk_size:(i + 1) * chunk_size] for i in range(self.num_chunks)]

    def save_chunks(self):
        chunks = self.split_into_chunks(self.training_df)
        training_output_folder = "data/raw/chunks"
        eval_output_folder = "data/raw/eval"
        os.makedirs(training_output_folder, exist_ok=True)
        os.makedirs(eval_output_folder, exist_ok=True)

        for i, chunk in enumerate(chunks):
            output_file = os.path.join(
                training_output_folder, f"data_chunk_{i + 1}.xlsx")
            chunk.to_excel(output_file, index=False)
            logging.info(f"Chunk {i + 1} saved to {output_file}")

        eval_file = os.path.join(eval_output_folder, "eval_set.xlsx")
        self.eval_df.to_excel(eval_file, index=False)
        logging.info(f"Evaluation set saved to {eval_file}")

    def process(self):
        self.save_chunks()
