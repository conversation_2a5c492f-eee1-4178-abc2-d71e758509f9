{"cells": [{"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"ename": "JSONDecodeError", "evalue": "Expecting value: line 281 column 7 (char 215861)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mJSONDecodeError\u001b[0m                           Traceback (most recent call last)", "Cell \u001b[0;32mIn[60], line 35\u001b[0m\n\u001b[1;32m     32\u001b[0m stop_chunk \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m4\u001b[39m\n\u001b[1;32m     34\u001b[0m \u001b[38;5;66;03m# Call the function with custom path and chunk range\u001b[39;00m\n\u001b[0;32m---> 35\u001b[0m \u001b[43mcheck_num_jobs_in_chunks\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcustom_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstart_chunk\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop_chunk\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[60], line 15\u001b[0m, in \u001b[0;36mcheck_num_jobs_in_chunks\u001b[0;34m(path_prefix, start_chunk, stop_chunk)\u001b[0m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;66;03m# Load the JSON data\u001b[39;00m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(json_file_path, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m file:\n\u001b[0;32m---> 15\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[43mjson\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;66;03m# Count the number of jobs\u001b[39;00m\n\u001b[1;32m     18\u001b[0m num_jobs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py:293\u001b[0m, in \u001b[0;36mload\u001b[0;34m(fp, cls, object_hook, parse_float, parse_int, parse_constant, object_pairs_hook, **kw)\u001b[0m\n\u001b[1;32m    274\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mload\u001b[39m(fp, \u001b[38;5;241m*\u001b[39m, \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, object_hook\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, parse_float\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    275\u001b[0m         parse_int\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, parse_constant\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m, object_pairs_hook\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkw):\n\u001b[1;32m    276\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Deserialize ``fp`` (a ``.read()``-supporting file-like object containing\u001b[39;00m\n\u001b[1;32m    277\u001b[0m \u001b[38;5;124;03m    a JSON document) to a Python object.\u001b[39;00m\n\u001b[1;32m    278\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    291\u001b[0m \u001b[38;5;124;03m    kwarg; otherwise ``JSONDecoder`` is used.\u001b[39;00m\n\u001b[1;32m    292\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 293\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mloads\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    294\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mcls\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mobject_hook\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mobject_hook\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    295\u001b[0m \u001b[43m        \u001b[49m\u001b[43mparse_float\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparse_float\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparse_int\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparse_int\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    296\u001b[0m \u001b[43m        \u001b[49m\u001b[43mparse_constant\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparse_constant\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mobject_pairs_hook\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mobject_pairs_hook\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkw\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py:346\u001b[0m, in \u001b[0;36mloads\u001b[0;34m(s, cls, object_hook, parse_float, parse_int, parse_constant, object_pairs_hook, **kw)\u001b[0m\n\u001b[1;32m    341\u001b[0m     s \u001b[38;5;241m=\u001b[39m s\u001b[38;5;241m.\u001b[39mdecode(detect_encoding(s), \u001b[38;5;124m'\u001b[39m\u001b[38;5;124msurrogatepass\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m    344\u001b[0m         parse_int \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m parse_float \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m    345\u001b[0m         parse_constant \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_pairs_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m kw):\n\u001b[0;32m--> 346\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_default_decoder\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    347\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    348\u001b[0m     \u001b[38;5;28mcls\u001b[39m \u001b[38;5;241m=\u001b[39m JSONDecoder\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/decoder.py:337\u001b[0m, in \u001b[0;36mJSONDecoder.decode\u001b[0;34m(self, s, _w)\u001b[0m\n\u001b[1;32m    332\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdecode\u001b[39m(\u001b[38;5;28mself\u001b[39m, s, _w\u001b[38;5;241m=\u001b[39mWHITESPACE\u001b[38;5;241m.\u001b[39mmatch):\n\u001b[1;32m    333\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Return the Python representation of ``s`` (a ``str`` instance\u001b[39;00m\n\u001b[1;32m    334\u001b[0m \u001b[38;5;124;03m    containing a JSON document).\u001b[39;00m\n\u001b[1;32m    335\u001b[0m \n\u001b[1;32m    336\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 337\u001b[0m     obj, end \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraw_decode\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43midx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m_w\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mend\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    338\u001b[0m     end \u001b[38;5;241m=\u001b[39m _w(s, end)\u001b[38;5;241m.\u001b[39mend()\n\u001b[1;32m    339\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m end \u001b[38;5;241m!=\u001b[39m \u001b[38;5;28mlen\u001b[39m(s):\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/decoder.py:355\u001b[0m, in \u001b[0;36mJSONDecoder.raw_decode\u001b[0;34m(self, s, idx)\u001b[0m\n\u001b[1;32m    353\u001b[0m     obj, end \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mscan_once(s, idx)\n\u001b[1;32m    354\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[0;32m--> 355\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m JSONDecodeError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mExpecting value\u001b[39m\u001b[38;5;124m\"\u001b[39m, s, err\u001b[38;5;241m.\u001b[39mvalue) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    356\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m obj, end\n", "\u001b[0;31mJSONDecodeError\u001b[0m: Expecting value: line 281 column 7 (char 215861)"]}], "source": ["############################################################\n", "# CHECK NUMBER OF JOB POSTINGS\n", "############################################################\n", "\n", "import json\n", "\n", "\n", "def check_num_jobs_in_chunks(path_prefix, start_chunk=1, stop_chunk=20):\n", "    for chunk_number in range(start_chunk, stop_chunk + 1):\n", "        # Adjusted path format\n", "        json_file_path = f'{path_prefix}_{chunk_number}.json'\n", "\n", "        # Load the JSON data\n", "        with open(json_file_path, 'r') as file:\n", "            data = json.load(file)\n", "\n", "        # Count the number of jobs\n", "        num_jobs = len(data['data'])\n", "\n", "        # Check if there are exactly 25 jobs\n", "        if num_jobs == 25:\n", "            print(\n", "                f\"Chunk {chunk_number}: Perfect! There are exactly 25 jobs in the JSON file.\")\n", "        else:\n", "            print(\n", "                f\"Chunk {chunk_number}: There are {num_jobs} jobs in the JSON file, not 25.\")\n", "\n", "\n", "# Example usage:\n", "custom_path = 'data/cvs/cvs_chunk'\n", "start_chunk = 4\n", "stop_chunk = 4\n", "\n", "# Call the function with custom path and chunk range\n", "check_num_jobs_in_chunks(custom_path, start_chunk, stop_chunk)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["############################################################\n", "# CHECK LENGTH OF 'CVS' LISTS IN JOB POSTINGS\n", "############################################################\n", "\n", "import json\n", "\n", "\n", "def check_cvs_lists_length(path_prefix, start_chunk=1, stop_chunk=20):\n", "    for chunk_number in range(start_chunk, stop_chunk + 1):\n", "        json_file_path = f'{path_prefix}_{chunk_number}.json'\n", "\n", "        # Load the JSON data\n", "        with open(json_file_path, 'r') as file:\n", "            data = json.load(file)\n", "\n", "        # Check the length of each 'cvs' list\n", "        for job in data['data']:\n", "            if len(job['cvs']) != 5:\n", "                print(\n", "                    f'Chunk {chunk_number}, Job posting with index {job[\"index\"]} does not have a cvs list with exactly 5 elements. Length: {len(job[\"cvs\"])}')\n", "\n", "\n", "\n", "# Example usage:\n", "custom_path = 'data/cvs/cvs_chunk'\n", "start_chunk = 1\n", "stop_chunk = 1\n", "\n", "# Call the function with custom path and chunk range\n", "check_cvs_lists_length(custom_path, start_chunk, stop_chunk)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chunk 5, Index number 16 FAILED\n", "    cv_1 FAILED (CV Length: 1553, Evaluation Length: 25)\n", "--------------------\n"]}], "source": ["import json\n", "\n", "\n", "def evaluate_cvs_in_chunks(path_prefix, start_chunk=1, stop_chunk=20):\n", "    for chunk_number in range(start_chunk, stop_chunk + 1):\n", "        json_file_path = f'{path_prefix}_{chunk_number}.json'\n", "\n", "        # Load the JSON data\n", "        with open(json_file_path, 'r') as file:\n", "            data = json.load(file)\n", "\n", "        # Initialize a set to track processed indexes within this chunk\n", "        processed_indexes = set()\n", "\n", "        # Iterate through each job posting\n", "        for job in data['data']:\n", "            index_number = job[\"index\"]\n", "\n", "            # Check if this index has already been processed in this chunk\n", "            if index_number in processed_indexes:\n", "                continue  # Skip processing this index again\n", "\n", "            # Initialize a list to collect results for each cv entry\n", "            results = []\n", "\n", "            # Iterate through each cv entry in the 'cvs' list\n", "            for i, cv_data in enumerate(job['cvs'], start=1):\n", "                cv_num = i\n", "                cv = cv_data[f'cv_{cv_num}']\n", "                evaluation = cv_data['evaluation']\n", "\n", "                # Check if both cv and evaluation are greater than 256 characters\n", "                if len(cv) > 256 and len(evaluation) > 256:\n", "                    pass\n", "                else:\n", "                    results.append(\n", "                        f'    cv_{cv_num} FAILED (CV Length: {len(cv)}, Evaluation Length: {len(evaluation)})')\n", "\n", "            # Determine overall result for the job posting\n", "            if all('PASS' in result for result in results):\n", "                continue\n", "            else:\n", "                print(\n", "                    f'Chunk {chunk_number}, Index number {index_number} FAILED')\n", "\n", "            # Print detailed results for each cv entry\n", "            for result in results:\n", "                print(result)\n", "\n", "            # Add this index to processed_indexes set\n", "            processed_indexes.add(index_number)\n", "\n", "            # Add spacing between job postings for clarity\n", "            print('-' * 20)\n", "\n", "\n", "# Example usage:\n", "custom_path = 'data/evaluations/evaluations_chunk'\n", "start_chunk = 1\n", "stop_chunk = 7\n", "\n", "# Call the function with custom path and chunk range\n", "evaluate_cvs_in_chunks(custom_path, start_chunk, stop_chunk)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Combined chunks saved to combined_chunks.xlsx\n"]}], "source": ["import pandas as pd\n", "import os\n", "\n", "\n", "def combine_chunks_and_save(chunk_folder, output_filename):\n", "    combined_df = pd.DataFrame()\n", "    for filename in os.listdir(chunk_folder):\n", "        if filename.startswith(\"data_chunk_\") and filename.endswith(\".xlsx\"):\n", "            file_path = os.path.join(chunk_folder, filename)\n", "            chunk_df = pd.read_excel(file_path)\n", "            combined_df = pd.concat([combined_df, chunk_df], ignore_index=True)\n", "\n", "    # Save combined DataFrame to Excel\n", "    combined_df.to_excel(output_filename, index=False)\n", "    print(f\"Combined chunks saved to {output_filename}\")\n", "\n", "\n", "# Example usage:\n", "chunk_folder = \"data/raw/chunks\"\n", "output_filename = \"combined_chunks.xlsx\"\n", "combine_chunks_and_save(chunk_folder, output_filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Combination Large, Dairy Products, Business Intelligence Analyst exists in data/raw/combined_chunks.xlsx.\n"]}], "source": ["import pandas as pd\n", "\n", "\n", "def find_combination_in_file(file_path, company_size, company_sector, position):\n", "    # Load the Excel file into a DataFrame\n", "    try:\n", "        df = pd.read_excel(file_path)\n", "    except FileNotFoundError:\n", "        print(f\"Error: File '{file_path}' not found.\")\n", "        return False\n", "\n", "    # Check if the combination exists in the DataFrame\n", "    combination_exists = ((df['Company Size'] == company_size) &\n", "                          (df['Company Sector'] == company_sector) &\n", "                          (df['Position'] == position)).any()\n", "\n", "    return combination_exists\n", "\n", "\t\t\n", "# Example usage:\n", "file_path = \"data/raw/combined_chunks.xlsx\"\n", "# company_size = \"Medium\"\n", "# company_sector = \"Dairy Products\"\n", "# position = \"Procurement Specialist\"\n", "\n", "\n", "\n", "company_size = \"Large\"\n", "company_sector = \"Dairy Products\"\n", "position = \"Business Intelligence Analyst\"\n", "\n", "\n", "exists = find_combination_in_file(\n", "    file_path, company_size, company_sector, position)\n", "if exists:\n", "    print(\n", "        f\"Combination {company_size}, {company_sector}, {position} exists in {file_path}.\")\n", "else:\n", "    print(\n", "        f\"Combination {company_size}, {company_sector}, {position} does not exist in {file_path}.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}