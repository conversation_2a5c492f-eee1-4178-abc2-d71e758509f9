# import os
# import json
# import pandas as pd
# import logging
# from concurrent.futures import ThreadPoolExecutor, as_completed
# from dotenv import load_dotenv
# from openai import OpenAI


# class JobPostingGenerator:
#     def __init__(self, start_chunk, stop_chunk, concurrent_rows=4):
#         self.start_chunk = start_chunk
#         self.stop_chunk = stop_chunk
#         self.concurrent_rows = concurrent_rows
#         self.model = "gpt-4o"
#         load_dotenv()
#         self.api_key = os.getenv("OPENAI_KEY")
#         if not self.api_key:
#             raise ValueError(
#                 "API key is missing. Set it in your .env file as 'OPENAI_KEY'.")
#         self.client = OpenAI(api_key=self.api_key)
#         self.setup_logging()

#     def setup_logging(self):
#         logging.basicConfig(level=logging.INFO,
#                             format='%(asctime)s - %(message)s')

#     def load_training_data(self, chunk_num):
#         file_path = f'data/raw/chunks/data_chunk_{chunk_num}.xlsx'
#         self.training_df = pd.read_excel(file_path).head(4)

#     def make_api_call(self, prompt):
#         try:
#             response = self.client.chat.completions.create(
#                 messages=[{"role": "user", "content": prompt}],
#                 model=self.model,
#             )
#             return response.choices[0].message.content
#         except Exception as e:
#             logging.error(f"API call failed: {e}")
#             return None

#     def generate_job_posting(self, company_size, company_sector, position):
#         prompt = f"""
#         Create a job posting for a {company_size} company in the {company_sector} sector looking for a {position}. Autofill information with random data if needed to make it look real.

#         Use this format:
#         ---
#         **[Company Name]**
#         **Job Title: [Job Title]**
#         **Location: [Location]**
#         **Job Type: [Full-Time/Part-Time/Contract]**
#         **About Us:**
#         [Insert brief description of the company. Mention the industry, company size, company culture, and any significant achievements or values.]
#         **Job Description:**
#         We are seeking a talented [Job Title] to join our team. As a [Job Title], you will be responsible for [brief overview of key responsibilities]. You will collaborate closely with [mention team or department] to [mention key goals or projects].
#         **Responsibilities:**
#         - [Responsibility 1]
#         - [Responsibility 2]
#         - [Responsibility 3]
#         - [Responsibility 4]
#         - [Responsibility 5]
#         **Qualifications:**
#         - [Qualification 1]
#         - [Qualification 2]
#         - [Qualification 3]
#         - [Qualification 4]
#         - [Qualification 5]
#         **Preferred Skills:**
#         - [Preferred Skill 1]
#         - [Preferred Skill 2]
#         - [Preferred Skill 3]
#         - [Preferred Skill 4]
#         - [Preferred Skill 5]
#         **Benefits:**
#         - [Benefit 1]
#         - [Benefit 2]
#         - [Benefit 3]
#         - [Benefit 4]
#         - [Benefit 5]
#         **How to Apply:**
#         Interested candidates should submit their resume and cover letter to [email address], with the subject line "Application for [Job Title] - [Your Name]." The application deadline is [Application Deadline].
#         **Equal Opportunity Employer:**
#         [Company Name] is an equal opportunity employer. We value diversity and are committed to fostering an inclusive environment for all employees.
#         **Contact Information:**
#         For inquiries, please reach out to [Contact Person’s Name] at [Contact Email] or [Contact Phone Number].
#         """
#         return self.make_api_call(prompt)

#     def create_data_for_job_posting(self, index, row):
#         company_size = row['Company Size']
#         company_sector = row['Company Sector']
#         position = row['Position']
#         job_posting = self.generate_job_posting(
#             company_size, company_sector, position)
#         if not job_posting:
#             logging.error(f"Failed to generate job posting for row {index}")
#             return index, None
#         logging.info(
#             f"Processed job posting {index + 1}.\nGenerated job posting for {company_size} company in {company_sector} sector looking for {position}.")
#         return {
#             "index": index,
#             "company_size": company_size,
#             "company_sector": company_sector,
#             "position": position,
#             "job_poster": job_posting
#         }

#     def process_job_postings(self):
#         for chunk_num in range(self.start_chunk, self.stop_chunk + 1):
#             logging.info(f"Processing chunk {chunk_num}")
#             self.load_training_data(chunk_num)
#             rows_to_process = list(self.training_df.iterrows())
#             data = []

#             with ThreadPoolExecutor(max_workers=self.concurrent_rows) as executor:
#                 futures = [executor.submit(self.create_data_for_job_posting, index, row)
#                            for index, row in rows_to_process]

#                 for future in as_completed(futures):
#                     job_posting_data = future.result()
#                     if job_posting_data:
#                         data.append(job_posting_data)

#             output_data = {"data": data}
#             output_file = f"data/job/job_postings_chunk_{chunk_num}.json"

#             with open(output_file, "w") as file:
#                 json.dump(output_data, file, indent=4)

#             logging.info(f"All job postings data saved to {output_file}.")
import os
import json
import pandas as pd
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from dotenv import load_dotenv
from openai import OpenAI


class JobPostingGenerator:
    def __init__(self, start_chunk, stop_chunk, input_prefix_path, output_prefix_path, concurrent_rows=4):
        self.start_chunk = start_chunk
        self.stop_chunk = stop_chunk
        self.concurrent_rows = concurrent_rows
        self.input_prefix_path = input_prefix_path
        self.output_prefix_path = output_prefix_path
        self.model = "gpt-4o"
        load_dotenv()
        self.api_key = os.getenv("OPENAI_KEY")
        if not self.api_key:
            raise ValueError(
                "API key is missing. Set it in your .env file as 'OPENAI_KEY'.")
        self.client = OpenAI(api_key=self.api_key)
        self.setup_logging()

    def setup_logging(self):
        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(message)s')

    def load_training_data(self, chunk_num):
        file_path = f'{self.input_prefix_path}_{chunk_num}.xlsx'
        self.training_df = pd.read_excel(file_path)

    def make_api_call(self, prompt):
        try:
            response = self.client.chat.completions.create(
                messages=[{"role": "user", "content": prompt}],
                model=self.model,
            )
            return response.choices[0].message.content
        except Exception as e:
            logging.error(f"API call failed: {e}")
            return None

    def generate_job_posting(self, company_size, company_sector, position):
        prompt = f"""
        Create a job posting for a {company_size} company in the {company_sector} sector looking for a {position}. Autofill information with random data if needed to make it look real.
        
        Use this format:
        ---
        **[Company Name]**
        **Job Title: [Job Title]**
        **Location: [Location]**
        **Job Type: [Full-Time/Part-Time/Contract]**
        **About Us:**
        [Insert brief description of the company. Mention the industry, company size, company culture, and any significant achievements or values.]
        **Job Description:**
        We are seeking a talented [Job Title] to join our team. As a [Job Title], you will be responsible for [brief overview of key responsibilities]. You will collaborate closely with [mention team or department] to [mention key goals or projects].
        **Responsibilities:**
        - [Responsibility 1]
        - [Responsibility 2]
        - [Responsibility 3]
        - [Responsibility 4]
        - [Responsibility 5]
        **Qualifications:**
        - [Qualification 1]
        - [Qualification 2]
        - [Qualification 3]
        - [Qualification 4]
        - [Qualification 5]
        **Preferred Skills:**
        - [Preferred Skill 1]
        - [Preferred Skill 2]
        - [Preferred Skill 3]
        - [Preferred Skill 4]
        - [Preferred Skill 5]
        **Benefits:**
        - [Benefit 1]
        - [Benefit 2]
        - [Benefit 3]
        - [Benefit 4]
        - [Benefit 5]
        **How to Apply:**
        Interested candidates should submit their resume and cover letter to [email address], with the subject line "Application for [Job Title] - [Your Name]." The application deadline is [Application Deadline].
        **Equal Opportunity Employer:**
        [Company Name] is an equal opportunity employer. We value diversity and are committed to fostering an inclusive environment for all employees.
        **Contact Information:**
        For inquiries, please reach out to [Contact Person’s Name] at [Contact Email] or [Contact Phone Number].
        """
        return self.make_api_call(prompt)

    def create_data_for_job_posting(self, index, row):
        company_size = row['Company Size']
        company_sector = row['Company Sector']
        position = row['Position']
        job_posting = self.generate_job_posting(
            company_size, company_sector, position)
        if not job_posting:
            logging.error(f"Failed to generate job posting for row {index}")
            return index, None
        logging.info(
            f"Processed job posting {index + 1}.\nGenerated job posting for {company_size} company in {company_sector} sector looking for {position}.")
        return {
            "index": index,
            "company_size": company_size,
            "company_sector": company_sector,
            "position": position,
            "job_poster": job_posting
        }

    def process_job_postings(self):
        for chunk_num in range(self.start_chunk, self.stop_chunk + 1):
            logging.info(f"Processing chunk {chunk_num}")
            self.load_training_data(chunk_num)
            rows_to_process = list(self.training_df.iterrows())
            data = []

            with ThreadPoolExecutor(max_workers=self.concurrent_rows) as executor:
                futures = [executor.submit(self.create_data_for_job_posting, index, row)
                           for index, row in rows_to_process]

                for future in as_completed(futures):
                    job_posting_data = future.result()
                    if job_posting_data:
                        data.append(job_posting_data)

            output_data = {"data": data}
            output_file = f"{self.output_prefix_path}_{chunk_num}.json"

            with open(output_file, "w") as file:
                json.dump(output_data, file, indent=4)

            logging.info(f"All job postings data saved to {output_file}.")
