apiVersion: apps/v1
kind: Deployment
metadata:
  name: arivgames
spec:
  replicas: 1
  selector:
    matchLabels:
      app: arivgames
  template:
    metadata:
      labels:
        app: arivgames
    spec:
      containers:
      - name: arivgames-container
        image: 050286875379.dkr.ecr.us-east-1.amazonaws.com/nginx-test:latest
        ports:
        - containerPort: 6969
        readinessProbe:
          httpGet:
            path: /health
            port: 6969
          initialDelaySeconds: 3
          periodSeconds: 5
