output "vpc_id" {
  value = aws_vpc.vpc.id
}

output "vpc_cidr_block" {
  value = aws_vpc.vpc.cidr_block
}

output "public_subnets" {
  value = aws_subnet.vpc_public_subnets
}

output "private_subnets" {
  value = aws_subnet.vpc_private_subnets
}
##############################
#SECURITY GROUPS
##############################
output "backend_ec2_sg" {
  value = aws_security_group.backend_ec2_sg.id
}
output "rds_sg" {
  value = aws_security_group.rds_sg.id
}

output "bastion_sg" {
  value = aws_security_group.bastion_sg.id
}

output "alb_sg" {
  value = aws_security_group.alb_sg.id
}
