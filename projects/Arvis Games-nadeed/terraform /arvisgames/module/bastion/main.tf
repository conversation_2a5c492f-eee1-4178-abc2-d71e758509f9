resource "aws_instance" "bastion-host" {
  ami                         = "ami-0866a3c8686eaeeba"
  instance_type               = "t3.medium"
  key_name                    = "bastionKey"
  subnet_id                   = var.public_subnets[0].id
  vpc_security_group_ids      = [var.bastion_sg]
  associate_public_ip_address = true
  root_block_device {
    volume_size = 10
    volume_type = "gp3"
  }

  tags = {
    Name = "bastion-host"
  }
}
