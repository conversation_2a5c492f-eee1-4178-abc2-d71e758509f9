variable "project_name" {}
variable "rds_sg" {}
variable "public_subnets" {}

variable "dbname" {
  type        = string
  default     = "Arvisgames"
  description = "DB name"
}

variable "username" {
  type        = string
  default     = "Arvisgames"
  description = "arvisgames"
}

variable "engine" {
  type    = string
  default = "postgres" # Changed to PostgreSQL as per your requirement
}

variable "instance_class" {
  type    = string
  default = "db.t3.medium" # Adjusted to fit 2 vCPUs and 4 GiB memory
}

variable "engine_version" {
  type    = string
  default = "16.3" # Updated to a compatible PostgreSQL version; adjust as needed
}
