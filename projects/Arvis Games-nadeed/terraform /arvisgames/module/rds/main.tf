##############################
#CREDENTIAL GENERATOR
##############################

resource "aws_kms_key" "rds_kms_key" {
  description = "${var.project_name} RDS encryption key"
  # deletion_window_in_days = 30
}

resource "aws_secretsmanager_secret" "rds_credentials_secret" {
  name = "${var.project_name}-rds-credentials"
}

resource "aws_secretsmanager_secret_version" "rds_credentials_version" {
  secret_id     = aws_secretsmanager_secret.rds_credentials_secret.id
  secret_string = random_password.rds_password.result
}

resource "random_password" "rds_password" {
  length  = 12
  special = false
}



##############################
#RDS
##############################

resource "aws_db_subnet_group" "db_subnet_group" {
  name       = "${var.project_name}-subnet-group"
  subnet_ids = [var.public_subnets[0].id, var.public_subnets[1].id]
  tags = merge(
    { Name = "RDS-Subnet-Group" }
  )
}

resource "aws_db_instance" "db_instance" {
  allocated_storage      = 200
  identifier             = "${var.project_name}-instance"
  db_name                = var.dbname
  engine                 = var.engine
  instance_class         = var.instance_class
  engine_version         = var.engine_version
  username               = var.username
  password               = aws_secretsmanager_secret_version.rds_credentials_version.secret_string
  db_subnet_group_name   = aws_db_subnet_group.db_subnet_group.name
  vpc_security_group_ids = [var.rds_sg]
  skip_final_snapshot    = true

  tags = {
    Name = "rds-instance"
  }
}
