# Main EKS Cluster Configuration
module "eks_dev" {
  source = "terraform-aws-modules/eks/aws"
  version = "~> 19.13"

  cluster_name                   = var.cluster_name
  cluster_version                = var.cluster_version
  cluster_endpoint_public_access = true

  iam_role_name            = var.iam_role_name
  iam_role_use_name_prefix = false

  vpc_id     = var.vpc_id
  subnet_ids = [var.private_subnets[0].id, var.private_subnets[1].id]

  eks_managed_node_groups = {
    managed = {
      iam_role_name              = var.managed_node_group_iam_role_name
      iam_role_use_name_prefix   = false
      use_custom_launch_template = false

      instance_types = var.instance_types

      min_size     = var.min_size
      max_size     = var.max_size
      desired_size = var.desired_size

      labels = {
        Which = "managed"
      }
    }
  }

  tags = var.tags
}

# Add-ons for EKS Cluster
module "eks_blueprints_addons" {
  source  = "aws-ia/eks-blueprints-addons/aws"
  version = "~> 1.0"

  cluster_name      = module.eks_dev.cluster_name
  cluster_endpoint  = module.eks_dev.cluster_endpoint
  cluster_version   = module.eks_dev.cluster_version
  oidc_provider_arn = module.eks_dev.oidc_provider_arn

  eks_addons = {
    aws-ebs-csi-driver = {
      most_recent              = true
      service_account_role_arn = module.ebs_csi_driver_irsa.iam_role_arn
    }
  }

  enable_aws_load_balancer_controller = true
  enable_cluster_autoscaler           = true
}

# IAM Role for EBS CSI Driver
module "ebs_csi_driver_irsa" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "~> 5.20"

  role_name_prefix = var.ebs_csi_driver_role_prefix

  attach_ebs_csi_policy = true

  oidc_providers = {
    main = {
      provider_arn               = module.eks_dev.oidc_provider_arn
      namespace_service_accounts = ["kube-system:ebs-csi-controller-sa"]
    }
  }
}

# Helm provider configuration to manage Kubernetes resources
provider "helm" {
  kubernetes {
    host                   = module.eks_dev.cluster_endpoint
    cluster_ca_certificate = base64decode(module.eks_dev.cluster_certificate_authority_data)

    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      command     = "aws"
      args        = ["eks", "get-token", "--cluster-name", module.eks_dev.cluster_name]
    }
  }
}
