variable "vpc_id" {
  description = "VPC ID where the EKS cluster will be deployed"
  type        = string
}

variable "private_subnets" {
}

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
  default     = "eks-cluster-Arvis"
}

variable "cluster_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
  default     = "1.31"
}

variable "iam_role_name" {
  description = "IAM role name for the EKS cluster"
  type        = string
  default     = "eks-cluster-Arvis-cluster-role"
}

variable "managed_node_group_iam_role_name" {
  description = "IAM role name for the EKS managed node group"
  type        = string
  default     = "eks-cluster-Arvis-role"
}

variable "instance_types" {
  description = "List of EC2 instance types for the managed node group"
  type        = list(string)
  default     = ["t3.medium"]
}

variable "min_size" {
  description = "Minimum size of the managed node group"
  type        = number
  default     = 1
}

variable "max_size" {
  description = "Maximum size of the managed node group"
  type        = number
  default     = 2
}

variable "desired_size" {
  description = "Desired size of the managed node group"
  type        = number
  default     = 1
}

variable "tags" {
  description = "Tags to apply to EKS resources"
  type        = map(string)
  default     = {
    Blueprint  = "eks-cluster-Arvis"
    GithubRepo = "github.com/aws-ia/terraform-aws-eks-blueprints"
  }
}

variable "ebs_csi_driver_role_prefix" {
  description = "Role name prefix for the EBS CSI driver IAM role"
  type        = string
  default     = "eks-cluster-Arvis-ebs-csi-driver"
}
