
module "vpc" {
  source       = "./module/vpc"
  region       = var.region
  cidr_block   = var.cidr_block
  project_name = var.project_name
}
module "rds" {
  source          = "./module/rds"
  project_name    = var.project_name
  rds_sg          = module.vpc.rds_sg
  public_subnets = module.vpc.public_subnets
}
module "bastion" {
  source         = "./module/bastion"
  vpc_id         = module.vpc.vpc_id
  public_subnets = module.vpc.public_subnets
  bastion_sg     = module.vpc.bastion_sg
}

module "eks_dev" {
  source = "./module/eks_dev"

  vpc_id = module.vpc.vpc_id
  private_subnets = module.vpc.private_subnets
}

