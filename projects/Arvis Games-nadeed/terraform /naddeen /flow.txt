ecr repo oluşturup github actions'a bak oidc oluşturma falan

müşteriden uygulamayı istiyebilirsin kaç portunda çalıştığını falan öğren

sonra eks'in içine uygulamayı deploy etmeye çalışacaksın ama loadbalancer target group'u müşterinin verdiği port numarasına göre değiştirmeyi unutma


Önce ECR repo oluştur sonra Github Actions için OIDC oluştur müşteriyle iletişime geçip Github ECR pipeline oluştur

müşteri pipeline çalıştırsın image ECR gelsin sonra port bilgilerini öğrenip EKS'e deploy etmeye çalışırsın

bunların hepsi olduğunda en son Route53 kısmına geçersin

ecr repo bitsin ondan sonra yazarim parameterleri

:16
github action yaptığınıda adamın uygulamasını github action otomatik olarak image build alacak ve ECR'a atacak sen sonrasında o ECR'da ki image i yaml file da belirtip deploy edeceksin








cpu 








Hata: Uygulama, bir çevre düzeni sorunu nedeniyle başlatılamadı.

Detaylar:
- Hata Türü: System.TypeInitializationException
- İç Hata: System.Exception
- Mesaj: Ortam adı boş olamaz

Hatanın Yeri:
- Uygulama: Fray.Api.Web
- Giriş Noktası: Program.Main
- Konum: /App/src/Fray.Api.Web/Program.cs:line 70

