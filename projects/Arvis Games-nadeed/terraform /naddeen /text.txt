kubectl apply -f https://raw.githubusercontent.com/kubernetes/dashboard/v2.7.0/aio/deploy/recommended.yaml

aws configure 
access key
secret key

aws eks update-kubeconfig --name eks-cluster-Arvis --region us-east-1 //connect to EKS step 1



kubectl apply -f yaml/fray-app-ingress.yaml
kubectl apply -f fray-app-ingress.yaml

kubectl logs arivgames-54f88fb654-xfw4s

kubectl get deployment -n kube-system alb-ingress-controller

kubectl get ingress your-app-ingress

kubectl logs -n kube-system -l app.kubernetes.io/name=alb-ingress-controller

kubectl get ingress your-app-ingress -o jsonpath="{.status.loadBalancer.ingress[0].hostname}"


brew services stop postgresql@14
aws configure list        
export AWS_PROFILE=fray 





1. Create AWS CodeBuild Project:
In the AWS Management Console, navigate to CodeBuild.

Click on "Create build project."

Configure the project settings:

Project name: Enter a meaningful name for your CodeBuild project.
Source provider: Choose the source provider where your code is stored (e.g., GitHub).
Repository: Select the repository and branch.
In the "Environment" section:

Environment image: Choose an image that includes Docker and kubectl. You can use aws/codebuild/amazonlinux2-x86_64-standard:4.0 as a starting point.
Service role: Choose an existing service role or create a new one with sufficient permissions.
In the "Buildspec" section, you can use the buildspec provided earlier in this conversation.

Click on "Create build project."

2. IAM Role Permissions for CodeBuild:
Ensure that the IAM role associated with your CodeBuild project has the necessary permissions. At a minimum, it should have permissions to:

Download artifacts from S3 or any other storage where Kubernetes manifests are stored.
Push Docker images to ECR.
Interact with EKS.
3. Update RBAC ConfigMap in EKS Cluster:
Update the aws-auth ConfigMap in the kube-system namespace to grant the CodeBuild Service Role the necessary permissions, as mentioned earlier.

4. Configure CloudWatch Events Rule:
In the AWS Management Console, navigate to CloudWatch > Events > Rules.

Click on "Create rule."

In the "Event Source" section:

Choose "Event Source Type" as "EventBridge Schema."
Choose "Service provider" as "Container Registry (ECR)."
Choose "Event type" as "Image Pushed."
In the "Targets" section:

Choose "CodeBuild project" as the target.
Select the CodeBuild project you created earlier.
Click on "Configure details."

Provide a name and description for your rule, and click on "Create rule."

5. Configure ECR Repository for Image Push Events:
Navigate to the AWS Management Console > ECR.

Select your repository.

Click on the "Edit" button.

Under the "Image settings" section, ensure that the "Scan on push" option is enabled.

Click on the "Save changes" button.

