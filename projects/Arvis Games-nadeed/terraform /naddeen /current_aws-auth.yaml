# Please edit the object below. Lines beginning with a '#' will be ignored,
# and an empty file will abort the edit. If an error occurs while saving this file will be
# reopened with the relevant failures.
#
apiVersion: v1
data:
  mapRoles: |
    - groups:
      - system:bootstrappers
      - system:nodes
      rolearn: arn:aws:iam::121040156615:role/eks-cluster-Arvis-role
      username: system:node:{{EC2PrivateDNSName}}
    - groups:
      - system:masters
      rolearn: arn:aws:iam::121040156615:role/service-role/codebuild-codebuild-yaml-service-role
      username: codebuild

kind: ConfigMap
metadata:
  creationTimestamp: "2023-12-01T10:11:52Z"
  name: aws-auth
  namespace: kube-system
  resourceVersion: "1459"
