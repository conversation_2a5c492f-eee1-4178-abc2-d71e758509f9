name: Building to Amazon ECR

on: 
  push:
    branches:
      - aws-docker-1

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: arivgames

permissions:
  id-token: write
  contents: read # This is required for actions/checkout@v2
  
jobs:
    build-and-push:
      runs-on: ubuntu-latest

      steps:
        - name: Checkout repository
          uses: actions/checkout@v3

        - name: configure aws credentials
          uses: aws-actions/configure-aws-credentials@v1.7.0
          with:
            role-to-assume: arn:aws:iam::121040156615:role/github-actions-arivgames-role   #change to reflect your IAM role's ARN
            role-session-name: GitHub_to_AWS_via_FederatedOIDC
            aws-region: us-east-1


        - name: Login to AWS ECR
          id: login-ecr
          uses: aws-actions/amazon-ecr-login@v1

        - name: Build and push Docker images (app1)
          id: build-image
          env:
            ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          run: |
            docker build -t arivgames .
            docker tag arivgames:latest 121040156615.dkr.ecr.us-east-1.amazonaws.com/arivgames:latest
            docker push 121040156615.dkr.ecr.us-east-1.amazonaws.com/arivgames:latest
            echo "::set-output name=image::121040156615.dkr.ecr.us-east-1.amazonaws.com/arivgames:latest
