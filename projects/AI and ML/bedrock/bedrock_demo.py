import boto3
import json
import base64
from PIL import Image
from io import BytesIO

bedrock = boto3.client(
    service_name='bedrock-runtime',
    region_name='us-east-1'
)

input = {
    "modelId": "stability.stable-diffusion-xl-v0",
    "contentType": "application/json",
    "accept": "application/json",
    "body": "{\"text_prompts\":[{\"text\":\"draw me work center\"}],\"cfg_scale\":10,\"seed\":0,\"steps\":50}"
}

response = bedrock.invoke_model(
    body=input["body"],
    modelId=input["modelId"],
    accept=input["accept"],
    contentType=input["contentType"]
)

response_body = json.loads(response['body'].read())


# Load the JSON response from the saved file
# with open("bedrock_response.json", "r") as json_file:
#     response_json = json.load(json_file)

# Extract the image data from the response
image_base64 = response_body["artifacts"][0]["base64"]
image_bytes = base64.b64decode(image_base64)

# Create a PIL image from the bytes
image = Image.open(BytesIO(image_bytes))

# Display the image
image.show()

# Optionally, save the image as a file
image.save("output_image.png")

