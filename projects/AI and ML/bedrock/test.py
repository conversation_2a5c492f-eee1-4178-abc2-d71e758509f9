

import json
import base64
from PIL import Image
from io import BytesIO

# Load the JSON response from the saved file
with open("bedrock_response.json", "r") as json_file:
    response_json = json.load(json_file)

# Extract the image data from the response
image_base64 = response_json["artifacts"][0]["base64"]
image_bytes = base64.b64decode(image_base64)

# Create a PIL image from the bytes
image = Image.open(BytesIO(image_bytes))

# Display the image
image.show()

# Optionally, save the image as a file
image.save("output_image.png")
