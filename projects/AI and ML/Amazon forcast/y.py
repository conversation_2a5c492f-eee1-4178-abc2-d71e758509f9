data = [
    ['25814.9578079891', '25858.3743125201', '25589.9885855439', '25779.9817949848'],
    ['25968.1701334655', '26081.5246051517', '25657.0255672937', '25812.4152686728'],
    ['25869.4727927244', '26087.1491647509', '25817.0317984223', '25969.5673526316'],
    ['25800.9096091103', '25970.284489312', '25753.0938469384', '25868.7981401932'],
    ['25934.0208061586', '26125.8697737206', '25362.6091281569', '25800.7243726077'],
    ['27301.9293168046', '27456.0790013622', '25752.9299469294', '25931.4728933574'],
    ['27726.0840340273', '27760.1593966037', '27069.2074245836', '27297.2653483206'],
    ['26102.4858320026', '28089.3380271543', '25912.6289086749', '27727.3930090424'],
    ['26089.6149228858', '26198.5790644484', '25880.6002488905', '26106.1500439649']
]

for row in data:
    open_value = float(row[0])
    close_value = float(row[3])

    open_close_avg = (open_value + close_value) / 2
    high_value = float(row[1])
    low_value = float(row[2])
    high_low_avg = (high_value + low_value) / 2
    final_avg = (open_close_avg + high_low_avg) / 2

    print("Row:", row)
    print("Average (Open + Close) / 2:", open_close_avg)
    print("Average (High + Low) / 2:", high_low_avg)
    print("Final Average for this row:", final_avg)

    # Calculate absolute differences
    open_diff = abs(final_avg - open_value)
    close_diff = abs(final_avg - close_value)

    if open_diff < close_diff:
        print("Final Average is closer to Open.")
    elif close_diff < open_diff:
        print("Final Average is closer to Close.")
    else:
        print("Final Average is equidistant from Open and Close.")
    print()  # Add a blank line for better readability
