resource "aws_security_group" "rds_sg" {
  name        = "rds_security_group"
  description = "Security group for RDS SQL Server"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 1433
    to_port     = 1433
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Replace with a restricted IP range in production
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

##############################
# RDS Subnet Group
##############################
resource "aws_db_subnet_group" "db_subnet_group" {
  name       = "${var.project_name}-subnet-group"
  subnet_ids = [var.public_subnets[0].id, var.public_subnets[1].id]
  tags = {
    Name = "RDS-Subnet-Group"
  }
}

##############################
# RDS SQL Server Instance
##############################
resource "aws_db_instance" "rds_instance" {
  identifier             = "${var.project_name}-db"
  allocated_storage      = 100
  instance_class         = "db.m5.large"
  engine                 = "sqlserver-se"    # SQL Server Standard Edition
  engine_version         = "16.00.4140.3.v1" # SQL Server 2022
  username               = var.username
  password               = var.db_password # Replace with a variable or secret
  db_subnet_group_name   = aws_db_subnet_group.db_subnet_group.name
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  publicly_accessible    = false
  multi_az               = false
  storage_type           = "gp3" # Use gp3 instead of gp2 for cost optimization
  iops                   = 3000
  storage_throughput     = 125
  skip_final_snapshot    = true
  deletion_protection    = false

  tags = {
    Name = "bendevar-rds-sqlserver"
  }
}
