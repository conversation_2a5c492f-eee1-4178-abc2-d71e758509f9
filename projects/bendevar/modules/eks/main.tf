module "eks" {
  source  = "terraform-aws-modules/eks/aws"
  version = "~> v20.26.0"

  cluster_name                   = "bendevar-prod-cluster"
  cluster_version                = "1.31"
  cluster_endpoint_public_access = true
  cluster_enabled_log_types      = ["api", "audit", "authenticator", "controllerManager", "scheduler"]

  iam_role_name                          = "prod-cluster-role"
  iam_role_use_name_prefix               = false
  cluster_security_group_name            = "prod-cluster-sg"
  cluster_security_group_use_name_prefix = false

  vpc_id                                   = var.vpc_id
  subnet_ids                               = [var.private_subnets[0].id, var.private_subnets[1].id]
  enable_cluster_creator_admin_permissions = true

  eks_managed_node_groups = {
    prod-node-group = {
      use_name_prefix                     = false
      cluster_name_prefix                 = false
      iam_role_name                       = "prod-node-group-role"
      iam_role_use_name_prefix            = false
      node_security_group_name            = "prod-node-group-sg"
      node_security_group_use_name_prefix = false
      use_custom_launch_template          = false
      disk_size                           = 20
      instance_types                      = ["t3.medium"]
      min_size                            = 1
      max_size                            = 2
      desired_size                        = 2
      capacity_type                       = "ON_DEMAND"
      remote_access = {
        ec2_ssh_key = var.key_name
      }
      labels = {
        Which = "prod-node-group"
      }
    }
  }

  tags = {
    Blueprint  = "eks-cluster"
    GithubRepo = "github.com/aws-ia/terraform-aws-eks-blueprints"
  }
}


module "eks_blueprints_addons" {
  source  = "aws-ia/eks-blueprints-addons/aws"
  version = "~> v1.16.2"

  cluster_name      = module.eks.cluster_name
  cluster_endpoint  = module.eks.cluster_endpoint
  cluster_version   = module.eks.cluster_version
  oidc_provider_arn = module.eks.oidc_provider_arn


  eks_addons = {

    vpc-cni = {
      most_recent = true
    }
    kube-proxy = {
      most_recent = true
    }
    coredns = {
      most_recent = true
    }
    aws-ebs-csi-driver = {
      most_recent              = true
      service_account_role_arn = module.ebs_csi_driver_irsa.iam_role_arn
    }
  }
  enable_aws_load_balancer_controller = true
  enable_cluster_autoscaler           = true
}

provider "helm" {
  kubernetes {
    host                   = module.eks.cluster_endpoint
    cluster_ca_certificate = base64decode(module.eks.cluster_certificate_authority_data)

    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      command     = "aws"
      args        = ["eks", "get-token", "--cluster-name", module.eks.cluster_name, "--profile", "bendevar"]
    }
  }
}

module "ebs_csi_driver_irsa" {
  source                = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version               = "~> 5.20"
  role_name_prefix      = "eks-cluster-ebs-csi-driver"
  attach_ebs_csi_policy = true
  oidc_providers = {
    main = {
      provider_arn               = module.eks.oidc_provider_arn
      namespace_service_accounts = ["kube-system:ebs-csi-controller-sa"]
    }
  }
}
