variable "public_subnet_cidrs" {
  type        = list(string)
  description = "Public Subnet CIDR values"
  default     = ["********/24", "********/24", "********/24"]
}

variable "private_subnet_cidrs" {
  type        = list(string)
  description = "Private Subnet CIDR values"
  default     = ["********/24", "********/24", "********/24"]
}

variable "cidr_block" {
  type        = string
  description = "CIDR range of VPC"
  default     = "10.0.0.0/16"
}

variable "azs" {
  type        = list(string)
  description = "Availability Zones"
  default     = ["a", "b", "c"]
}

variable "region" {
  type = string
}

variable "subnet_count" {
  type    = number
  default = 2
}

variable "project_name" {
  type    = string
  default = "bendevar"
}

variable "env" {}
