resource "aws_vpc" "vpc" {
  cidr_block = var.cidr_block

  enable_dns_support = true

  enable_dns_hostnames = true

  tags = merge(
    { Name = "${var.project_name}-vpc" }
  )
}


resource "aws_internet_gateway" "igw" {
  vpc_id = aws_vpc.vpc.id
  tags = merge(
    { Name = "${var.project_name}-vpc-igw" }
  )
}

resource "aws_subnet" "vpc_public_subnets" {
  count                   = var.subnet_count
  vpc_id                  = aws_vpc.vpc.id
  cidr_block              = element(var.public_subnet_cidrs, count.index)
  availability_zone       = "${var.region}${element(var.azs, count.index)}"
  map_public_ip_on_launch = true

  tags = {
    Name                                          = "${var.project_name}-public-subnet-${count.index + 1}"
    "kubernetes.io/cluster/bendevar-prod-cluster" = "shared"
    "kubernetes.io/cluster/bendevar-prod-cluster" = "owned"
    "kubernetes.io/role/elb"                      = "1"
  }
}

resource "aws_subnet" "vpc_private_subnets" {
  count             = var.subnet_count
  vpc_id            = aws_vpc.vpc.id
  cidr_block        = element(var.private_subnet_cidrs, count.index)
  availability_zone = "${var.region}${element(var.azs, count.index)}"

  tags = {
    Name = "${var.project_name}-private-subnet-${count.index + 1}"
  }
}

resource "aws_eip" "nat_eips" {
  count = var.subnet_count
  depends_on = [
    aws_internet_gateway.igw
  ]
  tags = merge(
    { Name = "${var.project_name} nat gateway ${count.index + 1}" }
  )
}

resource "aws_nat_gateway" "nat_gateways" {
  count         = var.subnet_count
  allocation_id = aws_eip.nat_eips[count.index].id
  subnet_id     = aws_subnet.vpc_public_subnets[count.index].id

  tags = merge(
    { Name = "${var.project_name} gateway - ${count.index + 1}" }
  )
}

resource "aws_route_table" "public_route_table" {
  vpc_id = aws_vpc.vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }

  tags = merge(
    { Name = "${var.project_name} public route table" }
  )
}

resource "aws_route_table" "private_route_tables" {
  count  = var.subnet_count
  vpc_id = aws_vpc.vpc.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.nat_gateways[count.index].id
  }

  tags = merge(
    { Name = "${var.project_name} private route table ${count.index + 1}" }
  )
}

resource "aws_route_table_association" "public_association" {
  count     = var.subnet_count
  subnet_id = aws_subnet.vpc_public_subnets[count.index].id

  route_table_id = aws_route_table.public_route_table.id
}

resource "aws_route_table_association" "private_association" {
  count     = var.subnet_count
  subnet_id = aws_subnet.vpc_private_subnets[count.index].id

  route_table_id = aws_route_table.private_route_tables[count.index].id
}




