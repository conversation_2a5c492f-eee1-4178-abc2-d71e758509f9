
variable "subnet_group_name" {
  description = "Subnet group name"
  default     = "redis-sg"
}

variable "vpc_id" {

}

variable "private_subnets" {
}


variable "cluster_name" {
  description = "Name of the Redis ElastiCache cluster"
  default     = "bendevar-cache"
}

variable "node_type" {
  description = "Instance type for Redis nodes"
  default     = "cache.m4.large"
}

variable "number_of_replicas" {
  description = "Number of replica nodes"
  default     = 1
}

variable "engine_version" {
  description = "Redis engine version"
  default     = "7.1"
}

variable "parameter_group_name" {
  default = "default.redis7.cluster.on"
}
