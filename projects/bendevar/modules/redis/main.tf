resource "aws_security_group" "redis_sg" {
  name        = "redis_ecurity_group"
  description = "Security group for redis"
  vpc_id      = var.vpc_id
  ingress {
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}


# VPC Subnet Group
resource "aws_elasticache_subnet_group" "redis_subnetgroup" {
  name        = var.subnet_group_name
  description = "Subnet group for Redis ElastiCache cluster"
  subnet_ids  = var.private_subnets
}

# ElastiCache Redis Cluster
resource "aws_elasticache_replication_group" "redis_cluster" {
  replication_group_id       = var.cluster_name
  description                = "ElastiCache Redis Cluster - ${var.cluster_name}"
  node_type                  = var.node_type
  num_node_groups            = 1
  replicas_per_node_group    = 1
  parameter_group_name       = var.parameter_group_name
  subnet_group_name          = aws_elasticache_subnet_group.redis_subnetgroup.name
  security_group_ids         = [aws_security_group.redis_sg.id]
  automatic_failover_enabled = false
  auto_minor_version_upgrade = true
  multi_az_enabled           = false
  transit_encryption_enabled = false
  at_rest_encryption_enabled = false

}
