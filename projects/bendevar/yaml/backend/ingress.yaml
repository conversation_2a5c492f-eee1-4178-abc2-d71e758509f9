# @format

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backend-ingress
  namespace: default
  annotations:
    alb.ingress.kubernetes.io/scheme: 'internet-facing' # Publicly accessible ALB
    alb.ingress.kubernetes.io/target-type: ip # Target pod IPs directly
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}]' # Listen on HTTP and HTTPS
    alb.ingress.kubernetes.io/healthcheck-path: '/' # Path for ALB health checks
    alb.ingress.kubernetes.io/subnet-tag-key: 'kubernetes.io/role/elb'

spec:
  ingressClassName: 'alb' # Specifies ALB as the ingress class
  rules:
    - http:
        paths:
          - path: '/'
            pathType: Prefix
            backend:
              service:
                name: backend # The service name to route traffic to
                port:
                  number: 8080
