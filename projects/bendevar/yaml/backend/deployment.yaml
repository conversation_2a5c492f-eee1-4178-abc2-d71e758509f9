# @format

apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: default
  labels:
    app: backend
  annotations:
    k8s.amazonaws.com/sg-ids: 'sg-0fb7a187a5c59e9bc' # Replace with the Security Group ID for Backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
        - name: backend
          image: 818718464176.dkr.ecr.eu-central-1.amazonaws.com/backend:latest # Replace with your backend container image
          ports:
            - containerPort: 8080
