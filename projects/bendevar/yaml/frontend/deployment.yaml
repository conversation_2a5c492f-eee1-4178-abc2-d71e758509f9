# @format

apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: default
  labels:
    app: frontend
  annotations:
    k8s.amazonaws.com/sg-ids: 'sg-0c7d906c3ee6a094d' # Replace with the Security Group ID for Frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
        - name: frontend
          image: 818718464176.dkr.ecr.eu-central-1.amazonaws.com/frontend:latest
          ports:
            - containerPort: 8080
