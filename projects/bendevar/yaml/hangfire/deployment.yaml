# @format

apiVersion: apps/v1
kind: Deployment
metadata:
  name: hangfire
  namespace: default
  labels:
    app: hangfire
  annotations:
    k8s.amazonaws.com/sg-ids: 'sg-0dd4abbef889a0723' # Replace with the Security Group ID for Hangfire
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hangfire
  template:
    metadata:
      labels:
        app: hangfire
    spec:
      containers:
        - name: hangfire
          image: 818718464176.dkr.ecr.eu-central-1.amazonaws.com/hangfire:latest # Replace with your hangfire container image
          ports:
            - containerPort: 8080

