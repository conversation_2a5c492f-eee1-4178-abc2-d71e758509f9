module "eks" {
  source          = "../modules/eks"
  vpc_id          = module.vpc.vpc_id
  private_subnets = module.vpc.private_subnets
  env             = var.env
  instance_type   = var.instance_type
}

module "vpc" {
  source               = "../modules/vpc"
  region               = var.region
  cidr_block           = var.cidr_block
  project_name         = var.project_name
  env                  = var.env
  public_subnet_cidrs  = var.public_subnet_cidrs
  private_subnet_cidrs = var.private_subnet_cidrs
}



module "redis" {
  source          = "../modules/redis"
  vpc_id          = module.vpc.vpc_id
  private_subnets = module.vpc.private_subnets

}

module "rds" {
  source          = "../modules/rds"
  vpc_id          = module.vpc.vpc_id
  private_subnets = module.vpc.private_subnets
}




