variable "env" {
  type    = string
  default = "prod"
}

variable "project_name" {
  type    = string
  default = "bendevar"
}

variable "public_subnet_cidrs" {
  type        = list(string)
  description = "Public Subnet CIDR values"
  default     = ["*********/24", "*********/24", "*********/24"]
}

variable "private_subnet_cidrs" {
  type        = list(string)
  description = "Private Subnet CIDR values"
  default     = ["*********/24", "*********/24", "*********/24"]
}

variable "cidr_block" {
  type        = string
  description = "CIDR range of VPC"
  default     = "*********/16"
}

variable "region" {
  type    = string
  default = "eu-central-1"
}

variable "instance_type" {
  type    = string
  default = "t3.medium"
}

