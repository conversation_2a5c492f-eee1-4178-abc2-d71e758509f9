def lambda_handler(event, context):
    print(f"EVENT: {event}")

    # Load variables from variables.json
    base_path = os.path.dirname(__file__)
    variables_file_path = os.path.join(base_path, 'variables.json')

    with open(variables_file_path, 'r') as var_file:
        variables = json.load(var_file)

    # Extract key information from event and environment variables
    company_name = event.get('company_name')
    s3_bucket_name = os.getenv('s3_bucket_name')
    listener_https_arn = variables['listener_https_arn']
    region = os.getenv('region')
    vpc_id = variables['vpc_id']

    # Step 1: Create CloudMap and Route53 entries
    cloudmap_result = create_cloudmap_and_route53(company_name, vpc_id)
    if cloudmap_result['statusCode'] != 200:
        return cloudmap_result

    # Extract namespace ID from the CloudMap response
    namespace_id = json.loads(cloudmap_result['body'])['namespace_id']

    # Step 2: Create CloudMap services for all required services
    service_descriptions = [
        ("grpc-gateway-service", f"{company_name} gRPC-gateway service"),
        ("service-csvtxt", f"{company_name} CSV and TXT service"),
        ("backend-service", f"{company_name} Backend Service"),
        ("service-ssh", f"{company_name} SSH Service"),
        ("service-md", f"{company_name} MateDrive Service"),
        ("service-msteams", f"{company_name} Microsoft Teams Service"),
        ("service-pdf", f"{company_name} PDF Service"),
        ("service-db", f"{company_name} Database Service"),
        ("service-email", f"{company_name} Email Service"),
        ("service-excel", f"{company_name} Excel Service"),
        ("service-ftp", f"{company_name} FTP Service"),
        ("service-rws", f"{company_name} RestfulWeb Service")
    ]

    service_discovery_ids = {}
    for service_name, description in service_descriptions:
        service_id = create_cloudmap_service(
            company_name, namespace_id, service_name, description)
        if isinstance(service_id, dict) and 'statusCode' in service_id:
            return service_id
        service_discovery_ids[service_name] = service_id

    # Prepare data for the second Lambda
    service_keys = {
        "frontend": f"{company_name}/frontend_td.json",
        "backend": f"{company_name}/backend_td.json",
        "grpc_gateway": f"{company_name}/grpc_gateway_td.json",
        "csvtxt": f"{company_name}/service_csvtxt_td.json",
        "db": f"{company_name}/service_db_td.json",
        "email": f"{company_name}/service_email_td.json",
        "excel": f"{company_name}/service_excel_td.json",
        "ftp": f"{company_name}/service_ftp_td.json",
        "md": f"{company_name}/service_md_td.json",
        "msteams": f"{company_name}/service_msteams_td.json",
        "pdf": f"{company_name}/service_pdf_td.json",
        "rws": f"{company_name}/service_rws_td.json",
        "ssh": f"{company_name}/service_ssh_td.json"
    }

    ecs_cluster_name = variables['ecs_cluster_name']
    private_subnet_ids = variables['private_subnet_ids']
    security_groups = {
        "frontend": variables['frontend_security_group_id'],
        "backend": variables['backend_security_group_id'],
        "grpc_gateway": variables['grpc_gateway_security_group_id'],
        "csvtxt": variables['csvtxt_security_group_id'],
        "db": variables['db_service_security_group_id'],
        "email": variables['email_service_security_group_id'],
        "excel": variables['excel_service_security_group_id'],
        "ftp": variables['ftp_service_security_group_id'],
        "md": variables['md_service_security_group_id'],
        "msteams": variables['msteams_service_security_group_id'],
        "pdf": variables['pdf_service_security_group_id'],
        "rws": variables['rws_service_security_group_id'],
        "ssh": variables['ssh_service_security_group_id']
    }

    tags = [{'Key': 'companyName', 'Value': company_name}]

   # Create a list of services with service discovery logic
    service_list = []
    for service, s3_key in service_keys.items():
        local_path = f"/tmp/{service}_td.json"

        # Apply if-else for service discovery name assignment
        if service == "frontend":
            service_discovery_name = "frontend-service"
        elif service == "backend":
            service_discovery_name = "backend-service"
        elif service == "grpc_gateway":
            service_discovery_name = "grpc-gateway-service"
        else:
            service_discovery_name = f"service-{service}"

        service_discovery_id = service_discovery_ids.get(
            service_discovery_name)

        # Log to ensure service discovery IDs are correct
        print(f"Service: {service}, Service Discovery Name: {
              service_discovery_name}, ID: {service_discovery_id}")

        # Append service info to the list
        service_list.append({
            "service": service,
            "s3_bucket_name": s3_bucket_name,
            "s3_key": s3_key,
            "local_path": local_path,
            "ecs_cluster_name": ecs_cluster_name,
            "private_subnet_ids": private_subnet_ids,
            "security_group": security_groups[service],
            "tags": tags,
            "company_name": company_name,
            "vpc_id": vpc_id,
            "listener_https_arn": listener_https_arn,
            "service_discovery_id": service_discovery_id  # Pass the discovery ID
        })

    # Calculate chunk size dynamically
    total_services = len(service_list)
    num_lambdas = 5  # Total number of chunks (for example)
    base_chunk_size = total_services // num_lambdas  # Size of each chunk
    remainder = total_services % num_lambdas  # Extra services to distribute

    services_to_create = []
    start = 0

    # Create chunks with almost equal number of services
    for i in range(num_lambdas):
        end = start + base_chunk_size + (1 if i < remainder else 0)
        services_chunk = service_list[start:end]
        services_to_create.append(services_chunk)
        start = end

    # Structure the data for the Step Function or Part 2 Lambda
    return {
        'statusCode': 200,
        'body': {
            'message': f'Services are ready to be created for {company_name}',
            'services': services_to_create
        }
    }
