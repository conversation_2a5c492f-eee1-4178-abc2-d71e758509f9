import json
import boto3


def lambda_handler(event, context=None):
    if 'body' in event and isinstance(event['body'], str):
        # Deserialize the JSON string in 'body'
        event['body'] = json.loads(event['body'])

    # Now that the 'body' is a dictionary, you can access 'services'
    services = event['body'].get('services', [])[0]

    print("Extracted Services:")
    print(json.dumps(services, indent=2))


if __name__ == "__main__":
    # Example test event for local testing
    test_event = {
        "statusCode": 200,
        "body": "{\"message\": \"Services are ready to be created for ermntest\", \"services\": [[{\"service\": \"frontend\", \"s3_key\": \"ermntest/frontend_td.json\", \"local_path\": \"/tmp/frontend_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-0954224fd3b2fe33a\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": null}, {\"service\": \"backend\", \"s3_key\": \"ermntest/backend_td.json\", \"local_path\": \"/tmp/backend_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-0c459e6644f5ab910\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": null}, {\"service\": \"grpc_gateway\", \"s3_key\": \"ermntest/grpc_gateway_td.json\", \"local_path\": \"/tmp/grpc_gateway_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-09ef38c7e74f37fa8\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-jgt7xzvzr7w44qei\"}], [{\"service\": \"csvtxt\", \"s3_key\": \"ermntest/service_csvtxt_td.json\", \"local_path\": \"/tmp/csvtxt_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-02632af394e231e15\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-hyz6j2mrgzshkzvf\"}, {\"service\": \"db\", \"s3_key\": \"ermntest/service_db_td.json\", \"local_path\": \"/tmp/db_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-01e7ca53acba3db11\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-2ysszgg3im7wia24\"}, {\"service\": \"email\", \"s3_key\": \"ermntest/service_email_td.json\", \"local_path\": \"/tmp/email_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-07378090807b34e8c\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-eqgc6nqrraknds2q\"}], [{\"service\": \"excel\", \"s3_key\": \"ermntest/service_excel_td.json\", \"local_path\": \"/tmp/excel_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-0699ae0a411caf44c\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-wrfplhrhbl24mxd4\"}, {\"service\": \"ftp\", \"s3_key\": \"ermntest/service_ftp_td.json\", \"local_path\": \"/tmp/ftp_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-07b029119073d3d26\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-ccxveyj466azzrz2\"}, {\"service\": \"md\", \"s3_key\": \"ermntest/service_md_td.json\", \"local_path\": \"/tmp/md_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-0a3014d9e9da73274\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-ag5lltn4kw2ivq5q\"}], [{\"service\": \"msteams\", \"s3_key\": \"ermntest/service_msteams_td.json\", \"local_path\": \"/tmp/msteams_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-0ab56be32e010c038\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-ygx6rdthwa55n4vg\"}, {\"service\": \"pdf\", \"s3_key\": \"ermntest/service_pdf_td.json\", \"local_path\": \"/tmp/pdf_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-04f462f2036caba4f\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-sf5fgo5zqws2iuyu\"}], [{\"service\": \"rws\", \"s3_key\": \"ermntest/service_rws_td.json\", \"local_path\": \"/tmp/rws_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-04546d56176b27ee3\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-zi6bxx52lwdziwl7\"}, {\"service\": \"ssh\", \"s3_key\": \"ermntest/service_ssh_td.json\", \"local_path\": \"/tmp/ssh_td.json\", \"ecs_cluster_name\": \"autommate-test-cluster\", \"private_subnet_ids\": [\"subnet-00ff2733f83785ba6\", \"subnet-00596712a8828d954\"], \"security_group\": \"sg-07028500f2515a8e8\", \"tags\": [{\"Key\": \"companyName\", \"Value\": \"ermntest\"}], \"company_name\": \"ermntest\", \"vpc_id\": \"vpc-0d2976c09e13fac2a\", \"listener_https_arn\": \"arn:aws:elasticloadbalancing:eu-central-1:248189940469:listener/app/alb-autommate-test/87a9a5a469e185ac/72f1167612b5a0fa\", \"service_discovery_id\": \"srv-j7vec2chjjvfn5vq\"}]]}"
    }

    # Simulating invocation of lambda handler with test event
    response = lambda_handler(test_event)
    print(f"Lambda response: {json.dumps(response, indent=2)}")
