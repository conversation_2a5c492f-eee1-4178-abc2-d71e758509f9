import json
import boto3
import os
import time
from botocore.exceptions import ClientError

ecs_client = boto3.client('ecs')
elbv2_client = boto3.client('elbv2')
s3_client = boto3.client('s3')
servicediscovery = boto3.client('servicediscovery')
route53 = boto3.client('route53')
autoscaling_client = boto3.client('application-autoscaling')

initial_priority = 1


def create_cloudmap_and_route53(company_name, vpc_id):
    try:
        namespace_name = f'{company_name}-microservice-connect'
        namespace_description = f'{
            company_name} Microservice-namespaces for ECS services'

        create_namespace_response = servicediscovery.create_private_dns_namespace(
            Name=namespace_name,
            Vpc=vpc_id,
            Description=namespace_description
        )
        operation_id = create_namespace_response['OperationId']
        print(f'Namespace created with operation ID: {operation_id}')
    except Exception as e:
        print(f'Error creating namespace: {e}')
        return {
            'statusCode': 500,
            'body': json.dumps(f'Error creating namespace: {e}')
        }

    try:
        namespace_id = None
        for _ in range(20):
            time.sleep(6)
            operation_status = servicediscovery.get_operation(
                OperationId=operation_id)
            if operation_status['Operation']['Status'] == 'SUCCESS':
                namespace_id = operation_status['Operation']['Targets']['NAMESPACE']
                print(f'Namespace created successfully with ID: {
                      namespace_id}')
                break
            elif operation_status['Operation']['Status'] == 'FAIL':
                raise Exception(f"Namespace creation failed: {
                                operation_status['Operation']['ErrorMessage']}")
        if not namespace_id:
            raise Exception("Namespace creation timed out")
    except Exception as e:
        print(f'Error waiting for namespace creation: {e}')
        return {
            'statusCode': 500,
            'body': json.dumps(f'Error waiting for namespace creation: {e}')
        }

    return {
        'statusCode': 200,
        'body': json.dumps({
            'namespace_id': namespace_id,
            'message': 'CloudMap namespace and its own Route 53 private hosted zone created successfully'
        })
    }


def create_cloudmap_service(company_name, namespace_id, service_name, description):
    try:
        response = servicediscovery.create_service(
            Name=service_name,
            NamespaceId=namespace_id,
            Description=description,
            DnsConfig={
                'NamespaceId': namespace_id,
                'RoutingPolicy': 'MULTIVALUE',
                'DnsRecords': [
                    {
                        'Type': 'A',
                        'TTL': 30
                    }
                ]
            },
            HealthCheckCustomConfig={
                'FailureThreshold': 1
            },
            Tags=[
                {
                    'Key': 'companyName',
                    'Value': company_name
                }
            ]
        )
        service_id = response['Service']['Id']
        print(f'CloudMap service "{service_name}" successfully created under namespace "{
              namespace_id}" and registered with ECS Service Discovery. Service ID: {service_id}')
        return service_id
    except ClientError as e:
        print(f'Error creating CloudMap service {service_name}: {e}')
        return {
            'statusCode': 500,
            'body': json.dumps(f'Error creating CloudMap service {service_name}: {e}')
        }


def download_task_definition(s3_bucket_name, s3_key, local_path):
    try:
        s3_client.download_file(s3_bucket_name, s3_key, local_path)
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == '404':
            return {
                'statusCode': 404,
                'body': json.dumps({
                    'message': f"File not found in S3 bucket: {s3_bucket_name}/{s3_key}"
                })
            }
        else:
            return {
                'statusCode': 500,
                'body': json.dumps({
                    'message': f"Error downloading file from S3: {str(e)}"
                })
            }
    return None


def register_task_definition(task_definition):
    response = ecs_client.register_task_definition(
        family=task_definition['family'],
        networkMode=task_definition['networkMode'],
        containerDefinitions=task_definition['containerDefinitions'],
        requiresCompatibilities=task_definition.get(
            'requiresCompatibilities', []),
        cpu=task_definition.get('cpu', ''),
        volumes=task_definition.get('volumes', ''),
        memory=task_definition.get('memory', ''),
        executionRoleArn=task_definition.get('executionRoleArn', ''),
        taskRoleArn=task_definition.get('taskRoleArn', ''),
        runtimePlatform=task_definition.get('runtimePlatform', None)
    )
    return response['taskDefinition']['taskDefinitionArn']


def create_target_group_frontend(company_name, vpc_id):
    response = elbv2_client.create_target_group(
        Name=f"{company_name}-f-tg",
        Protocol='HTTP',
        Port=80,
        VpcId=vpc_id,
        HealthCheckProtocol='HTTP',
        HealthCheckPort='traffic-port',
        HealthCheckPath='/health',
        HealthCheckIntervalSeconds=30,
        HealthCheckTimeoutSeconds=5,
        HealthyThresholdCount=5,
        UnhealthyThresholdCount=2,
        TargetType='ip',
        Tags=[
            {
                'Key': 'companyName',
                'Value': company_name
            }
        ]
    )
    target_group_arn = response['TargetGroups'][0]['TargetGroupArn']

    # Add target stickiness
    elbv2_client.modify_target_group_attributes(
        TargetGroupArn=target_group_arn,
        Attributes=[
            {
                'Key': 'stickiness.enabled',
                'Value': 'true'
            },
            {
                'Key': 'stickiness.type',
                'Value': 'lb_cookie'
            },
            {
                'Key': 'stickiness.lb_cookie.duration_seconds',
                'Value': str(86400)  # 1 day in seconds
            }
        ]
    )

    return target_group_arn


def create_target_group_backend(company_name, vpc_id):
    response = elbv2_client.create_target_group(
        Name=f"{company_name}-b-tg",
        Protocol='HTTPS',
        Port=443,
        VpcId=vpc_id,
        HealthCheckProtocol='HTTPS',
        HealthCheckPort='traffic-port',
        HealthCheckPath='/api/v1/health',
        HealthCheckIntervalSeconds=30,
        HealthCheckTimeoutSeconds=5,
        HealthyThresholdCount=5,
        UnhealthyThresholdCount=2,
        TargetType='ip',
        Tags=[
            {
                'Key': 'companyName',
                'Value': company_name
            }
        ]
    )
    target_group_arn = response['TargetGroups'][0]['TargetGroupArn']

    # Add target stickiness
    elbv2_client.modify_target_group_attributes(
        TargetGroupArn=target_group_arn,
        Attributes=[
            {
                'Key': 'stickiness.enabled',
                'Value': 'true'
            },
            {
                'Key': 'stickiness.type',
                'Value': 'lb_cookie'
            },
            {
                'Key': 'stickiness.lb_cookie.duration_seconds',
                'Value': str(86400)  # 1 day in seconds
            }
        ]
    )

    return target_group_arn


def create_or_update_service(cluster_name, service_name, task_definition_arn, subnets, security_groups, tags, container_port, target_group_arn, service_discovery_id):

    prefix_arn_of_servicediscovery = os.getenv(
        'prefix_arn_of_servicediscovery')
    print(f"create service function, service name: {
          service_name}, and service_discovery_id: {service_discovery_id}")
    # Updated placement strategy and deployment configuration
    placement_strategy = [
        {
            'type': 'binpack',
            'field': 'cpu'
        }
    ]

    deployment_configuration = {
        'deploymentCircuitBreaker': {
            'enable': True,
            'rollback': False
        }
    }

    # Capacity provider strategy
    capacity_provider_strategy = [
        {
            'capacityProvider': 'Cluster-EC2CapacityProvider',
            'base': 0,
            'weight': 1
        }
    ]

    try:
        ecs_client.create_service(
            cluster=cluster_name,
            serviceName=service_name,
            taskDefinition=task_definition_arn,
            desiredCount=1,
            networkConfiguration={
                'awsvpcConfiguration': {
                    'subnets': subnets,
                    'securityGroups': security_groups,
                    'assignPublicIp': 'DISABLED'
                }
            },
            loadBalancers=[
                {
                    'targetGroupArn': target_group_arn,
                    'containerName': service_name,
                    'containerPort': container_port
                }
            ] if target_group_arn else [],
            serviceRegistries=[
                {
                    'registryArn': f"{prefix_arn_of_servicediscovery}/{service_discovery_id}"
                }
            ] if service_discovery_id else [],
            tags=[{'key': tag['Key'], 'value': tag['Value']} for tag in tags],
            placementStrategy=placement_strategy,
            deploymentConfiguration=deployment_configuration,
            capacityProviderStrategy=capacity_provider_strategy
        )
    except ClientError as e:
        if "already exists" in str(e) or "Creation of service was not idempotent" in str(e):
            try:
                ecs_client.update_service(
                    cluster=cluster_name,
                    service=service_name,
                    taskDefinition=task_definition_arn,
                    placementStrategy=placement_strategy,
                    deploymentConfiguration=deployment_configuration,
                    serviceRegistries=[
                        {
                            'registryArn': service_discovery_id
                        }
                    ] if service_discovery_id else [],
                    capacityProviderStrategy=capacity_provider_strategy
                )
            except ClientError as update_e:
                return {
                    'statusCode': 500,
                    'body': json.dumps({
                        'message': f"Error updating ECS service: {str(update_e)}"
                    })
                }
        else:
            return {
                'statusCode': 500,
                'body': json.dumps({
                    'message': f"Error creating/updating ECS service: {str(e)}"
                })
            }

    # Add scale response
    scalable_target_response = None
    try:
        scalable_target_response = autoscaling_client.register_scalable_target(
            ServiceNamespace='ecs',
            ResourceId=f'service/{cluster_name}/{service_name}',
            ScalableDimension='ecs:service:DesiredCount',
            MinCapacity=1,
            MaxCapacity=3
        )
        print(f"Scalable target registered: {scalable_target_response}")
    except ClientError as e:
        print(f"Error registering scalable target: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': f"Error registering scalable target: {str(e)}"
            })
        }

    # Autoscaling Policy for CPU
    try:
        response = autoscaling_client.put_scaling_policy(
            PolicyName='cpu_scaling_policy',
            ServiceNamespace='ecs',
            ResourceId=f'service/{cluster_name}/{service_name}',
            ScalableDimension='ecs:service:DesiredCount',
            PolicyType='TargetTrackingScaling',
            TargetTrackingScalingPolicyConfiguration={
                'TargetValue': 80.0,
                'PredefinedMetricSpecification': {
                    'PredefinedMetricType': 'ECSServiceAverageCPUUtilization'
                },
                'ScaleOutCooldown': 60,
                'ScaleInCooldown': 60
            }
        )
        print(f"CPU scaling policy created: {response}")

        # CloudWatch Alarm for CPU Utilization
        cloudwatch_client = boto3.client('cloudwatch')
        cloudwatch_client.put_metric_alarm(
            AlarmName=f"{service_name}-cpu-utilization-alarm",
            ComparisonOperator='GreaterThanThreshold',
            EvaluationPeriods=1,
            MetricName='CPUUtilization',
            Namespace='AWS/ECS',
            Period=10,  # 10 second
            Statistic='Average',
            Threshold=85.0,
            ActionsEnabled=False,
            AlarmDescription='Alarm when CPU exceeds 85%',
            Dimensions=[
                {
                    'Name': 'ClusterName',
                    'Value': cluster_name
                },
                {
                    'Name': 'ServiceName',
                    'Value': service_name
                }
            ],
            Unit='Percent'
        )
        print(f"CPU Utilization alarm for {
              service_name} created successfully.")
    except ClientError as e:
        print(f"Error creating CPU scaling policy or alarm: {e}")

    # Autoscaling Policy for Memory
    try:
        response = autoscaling_client.put_scaling_policy(
            PolicyName='memory_scaling_policy',
            ServiceNamespace='ecs',
            ResourceId=f'service/{cluster_name}/{service_name}',
            ScalableDimension='ecs:service:DesiredCount',
            PolicyType='TargetTrackingScaling',
            TargetTrackingScalingPolicyConfiguration={
                'TargetValue': 60.0,
                'PredefinedMetricSpecification': {
                    'PredefinedMetricType': 'ECSServiceAverageMemoryUtilization'
                },
                'ScaleOutCooldown': 60,
                'ScaleInCooldown': 60
            }
        )
        print(f"Memory scaling policy created: {response}")

        # CloudWatch Alarm for Memory Utilization
        cloudwatch_client.put_metric_alarm(
            AlarmName=f"{service_name}-memory-utilization-alarm",
            ComparisonOperator='GreaterThanThreshold',
            EvaluationPeriods=1,
            MetricName='MemoryUtilization',
            Namespace='AWS/ECS',
            Period=10,  # 10 second
            Statistic='Average',
            Threshold=60.0,
            ActionsEnabled=False,
            AlarmDescription='Alarm when Memory exceeds 60%',
            Dimensions=[
                {
                    'Name': 'ClusterName',
                    'Value': cluster_name
                },
                {
                    'Name': 'ServiceName',
                    'Value': service_name
                }
            ],
            Unit='Percent'
        )
        print(f"Memory Utilization alarm for {
              service_name} created successfully.")
    except ClientError as e:
        print(f"Error creating memory scaling policy or alarm: {e}")

    return None


def get_available_priority(listener_arn, initial_priority):
    priority = initial_priority
    while True:
        try:
            response = elbv2_client.describe_rules(
                ListenerArn=listener_arn
            )
            existing_priorities = [
                int(rule['Priority']) for rule in response['Rules'] if rule['Priority'].isdigit()]
            if priority not in existing_priorities:
                return priority
            priority += 1
        except ClientError as e:
            return {
                'statusCode': 500,
                'body': json.dumps({
                    'message': f"Error checking priorities: {str(e)}"
                })
            }


def add_listener_rule(company_name, target_group_arn, listener_http_arn, priority):

    try:
        response = elbv2_client.create_rule(
            ListenerArn=listener_http_arn,
            Conditions=[
                {
                    'Field': 'host-header',
                    'Values': [f"{company_name}.autommate.app"]
                },
            ],
            Actions=[
                {
                    'Type': 'forward',
                    'ForwardConfig': {
                        'TargetGroups': [
                            {
                                'TargetGroupArn': target_group_arn,
                                'Weight': 1
                            }
                        ],
                        'TargetGroupStickinessConfig': {
                            'Enabled': True,
                            'DurationSeconds': 86400  # 1 day in seconds
                        }
                    }
                },
            ],
            Priority=priority
        )
        rule_arn = response['Rules'][0]['RuleArn']

        elbv2_client.add_tags(
            ResourceArns=[rule_arn],
            Tags=[
                {
                    'Key': 'Name',
                    'Value': f"{company_name}-frontend-rule"
                }
            ]
        )
    except ClientError as e:
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': f"Error creating listener rule: {str(e)}"
            })
        }
    return None


def add_listener_rule_backend(company_name, target_group_arn, listener_https_arn, path_pattern, priority):

    try:
        response = elbv2_client.create_rule(
            ListenerArn=listener_https_arn,
            Conditions=[
                {
                    'Field': 'path-pattern',
                    'Values': [path_pattern]
                },
                {
                    'Field': 'host-header',
                    'Values': [f"{company_name}.autommate.app"]
                }
            ],
            Actions=[
                {
                    'Type': 'forward',
                    'ForwardConfig': {
                        'TargetGroups': [
                            {
                                'TargetGroupArn': target_group_arn,
                                'Weight': 1
                            }
                        ],
                        'TargetGroupStickinessConfig': {
                            'Enabled': True,
                            'DurationSeconds': 86400  # 1 day in seconds
                        }
                    }
                },
            ],
            Priority=priority
        )
        rule_arn = response['Rules'][0]['RuleArn']

        elbv2_client.add_tags(
            ResourceArns=[rule_arn],
            Tags=[
                {
                    'Key': 'Name',
                    'Value': f"{company_name}-backend-rule-{priority}"
                }
            ]
        )
    except ClientError as e:
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': f"Error creating listener rule: {str(e)}"
            })
        }
    return None


def handle_service(s3_bucket_name, s3_key, local_path, ecs_cluster_name, subnets, security_group_id, tags, company_name, vpc_id, listener_https_arn, service_discovery_id):

    print(f"handle service function {
          s3_key}, and service_discovery_id: {service_discovery_id}")

    result = download_task_definition(s3_bucket_name, s3_key, local_path)
    if result:
        return result

    with open(local_path, 'r') as json_file:
        task_definition = json.load(json_file)

    task_definition_arn = register_task_definition(task_definition)

    priority = get_available_priority(listener_https_arn, initial_priority)
    if isinstance(priority, dict):
        return priority

    if 'backend' in s3_key:
        target_group_arn = create_target_group_backend(company_name, vpc_id)

        result = add_listener_rule_backend(
            company_name, target_group_arn, listener_https_arn, "/api/*", priority)
        if result:
            return result

        result = add_listener_rule_backend(
            company_name, target_group_arn, listener_https_arn, "/libraries/*", priority + 1)
        if result:
            return result

        result = add_listener_rule_backend(
            company_name, target_group_arn, listener_https_arn, "/socket/*", priority + 2)
        if result:
            return result

        container_port = task_definition['containerDefinitions'][0]['portMappings'][0]['containerPort']

        result = create_or_update_service(
            ecs_cluster_name,
            task_definition['containerDefinitions'][0]['name'],
            task_definition_arn,
            subnets,
            [security_group_id],
            tags,
            container_port,
            target_group_arn,
            service_discovery_id
        )
    else:
        if 'frontend' in s3_key:
            target_group_arn = create_target_group_frontend(
                company_name, vpc_id)

            result = add_listener_rule(
                company_name, target_group_arn, listener_https_arn, priority + 3)
            if result:
                return result

            container_port = task_definition['containerDefinitions'][0]['portMappings'][0]['containerPort']

            result = create_or_update_service(
                ecs_cluster_name,
                task_definition['containerDefinitions'][0]['name'],
                task_definition_arn,
                subnets,
                [security_group_id],
                tags,
                container_port,
                target_group_arn,
                None
            )
        else:
            result = create_or_update_service(
                ecs_cluster_name,
                task_definition['containerDefinitions'][0]['name'],
                task_definition_arn,
                subnets,
                [security_group_id],
                tags,
                None,
                None,
                service_discovery_id
            )

    if result:
        return result

    return task_definition_arn


def lambda_handler(event, context):
    print(f"EVENT: {event}")

    base_path = os.path.dirname(__file__)
    variables_file_path = os.path.join(base_path, 'variables.json')

    with open(variables_file_path, 'r') as var_file:
        variables = json.load(var_file)

    company_name = event.get('company_name')
    s3_bucket_name = os.getenv('s3_bucket_name')
    listener_https_arn = variables['listener_https_arn']
    region = os.getenv('region')
    vpc_id = variables['vpc_id']

    cloudmap_result = create_cloudmap_and_route53(company_name, vpc_id)
    if cloudmap_result['statusCode'] != 200:
        return cloudmap_result

    namespace_id = json.loads(cloudmap_result['body'])['namespace_id']

    service_descriptions = [
        ("grpc-gateway-service", f"{company_name} gRPC-gateway service"),
        ("service-csvtxt", f"{company_name} CSV and TXT service"),
        ("backend-service", f"{company_name} Backend Service"),
        ("service-ssh", f"{company_name} SSH Service"),
        ("service-md", f"{company_name} MateDrive Service"),
        ("service-msteams", f"{company_name} Microsoft Teams Service"),
        ("service-pdf", f"{company_name} PDF Service"),
        ("service-db", f"{company_name} Database Service"),
        ("service-email", f"{company_name} Email Service"),
        ("service-excel", f"{company_name} Excel Service"),
        ("service-ftp", f"{company_name} FTP Service"),
        ("service-rws", f"{company_name} RestfulWeb Service")
    ]

    service_discovery_ids = {}
    for service_name, description in service_descriptions:
        service_id = create_cloudmap_service(
            company_name, namespace_id, service_name, description)
        if isinstance(service_id, dict) and 'statusCode' in service_id:
            return service_id
        service_discovery_ids[service_name] = service_id

    # # Print every service discovery id for control
    # for service_name, service_id in service_discovery_ids.items():
    #     print(f'service_name: {service_name}, service_id: {service_id}')




    service_keys = {
        "frontend": f"{company_name}/frontend_td.json",
        "backend": f"{company_name}/backend_td.json",
        "grpc_gateway": f"{company_name}/grpc_gateway_td.json",
        "csvtxt": f"{company_name}/service_csvtxt_td.json",
        "db": f"{company_name}/service_db_td.json",
        "email": f"{company_name}/service_email_td.json",
        "excel": f"{company_name}/service_excel_td.json",
        "ftp": f"{company_name}/service_ftp_td.json",
        "md": f"{company_name}/service_md_td.json",
        "msteams": f"{company_name}/service_msteams_td.json",
        "pdf": f"{company_name}/service_pdf_td.json",
        "rws": f"{company_name}/service_rws_td.json",
        "ssh": f"{company_name}/service_ssh_td.json"
    }

    ecs_cluster_name = variables['ecs_cluster_name']
    private_subnet_ids = variables['private_subnet_ids']
    security_groups = {
        "frontend": variables['frontend_security_group_id'],
        "backend": variables['backend_security_group_id'],
        "grpc_gateway": variables['grpc_gateway_security_group_id'],
        "csvtxt": variables['csvtxt_security_group_id'],
        "db": variables['db_service_security_group_id'],
        "email": variables['email_service_security_group_id'],
        "excel": variables['excel_service_security_group_id'],
        "ftp": variables['ftp_service_security_group_id'],
        "md": variables['md_service_security_group_id'],
        "msteams": variables['msteams_service_security_group_id'],
        "pdf": variables['pdf_service_security_group_id'],
        "rws": variables['rws_service_security_group_id'],
        "ssh": variables['ssh_service_security_group_id']
    }
    vpc_id = variables['vpc_id']

    tags = [
        {
            'Key': 'companyName',
            'Value': company_name
        }
    ]

    service_arns = {}

    for service, s3_key in service_keys.items():
        local_path = f"/tmp/{service}_td.json"
        print(f"service into attach handle_service function: {service}")
        service_discovery_name = None
        if (service == "frontend") or (service == "backend"):
            service_discovery_name = f"{service}-service"
        elif (service == "grpc_gateway"):
            service_discovery_name = "grpc-gateway-service"
        elif service in ["csvtxt", "ssh", "md", "msteams", "pdf", "db", "email", "excel", "ftp", "rws"]:
            service_discovery_name = f"service-{service}"
        result = handle_service(
            s3_bucket_name, s3_key, local_path, ecs_cluster_name, private_subnet_ids, security_groups[
                service], tags, company_name, vpc_id, listener_https_arn, service_discovery_ids.get(service_discovery_name, None)
        )
        if isinstance(result, dict) and 'statusCode' in result:
            return result
        service_arns[f"{service}TaskDefinitionArn"] = result

    return {
        'statusCode': 200,
        'body': json.dumps({
            'message': f'All services are created successfully for {company_name}',
            **service_arns
        })
    }


if __name__ == "__main__":
    test_event = {
        "company_name": None,
        "s3_bucket_name": None
    }
    result = lambda_handler(test_event, None)
    print(result)
