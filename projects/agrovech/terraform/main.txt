################################################################################################
# ECS Cluster
################################################################################################
resource "aws_ecs_cluster" "ecs_cluster" {
  name = "${var.name}-ecs-cluster"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

################################################################################################
# ECS capacity provider
################################################################################################

resource "aws_ecs_capacity_provider" "ecs_capacity_provider" {
  name = "EC2_CapacityProvider"

  auto_scaling_group_provider {
    auto_scaling_group_arn = aws_autoscaling_group.ecs_asg.arn

    managed_scaling {
      maximum_scaling_step_size = 1000
      minimum_scaling_step_size = 1
      status                    = "ENABLED"
      target_capacity           = 1
    }
  }
}

resource "aws_ecs_cluster_capacity_providers" "capacity_provider" {
  cluster_name = aws_ecs_cluster.ecs_cluster.name

  capacity_providers = [aws_ecs_capacity_provider.ecs_capacity_provider.name]

  default_capacity_provider_strategy {
    base              = 0
    weight            = 1
    capacity_provider = aws_ecs_capacity_provider.ecs_capacity_provider.name
  }
}

################################################################################################
# IAM Roles and Policies for ECS
################################################################################################
resource "aws_iam_role" "ecs_task_role" {
  name = "${var.name}-ECS-TaskRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect    = "Allow",
      Principal = { Service = "ecs-tasks.amazonaws.com" },
      Action    = "sts:AssumeRole",
    }],
  })
}

resource "aws_iam_role" "ecs_iam_role" {
  name               = "${var.name}-ECS-ExecutionRole"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role_policy_attachment" "ecs_execution_role_policy" {
  role       = aws_iam_role.ecs_iam_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

################################################################################################
# EC2 Instance Profile and Role for ECS
################################################################################################
resource "aws_iam_instance_profile" "ecs_profile" {
  name = aws_iam_role.instance_role.name
  role = aws_iam_role.instance_role.name
}

resource "aws_iam_role" "instance_role" {
  name = "${var.name}-EC2-InstanceRole"
  path = "/"
  assume_role_policy = jsonencode({
    "Version" : "2008-10-17",
    "Statement" : [
      {
        "Action" : "sts:AssumeRole",
        "Principal" : { "Service" : "ec2.amazonaws.com" },
        "Effect" : "Allow",
        "Sid" : ""
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ec2_registry_fullaccess" {
  role       = aws_iam_role.instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryFullAccess"
}

resource "aws_iam_role_policy_attachment" "ec2_service_role_policy" {
  role       = aws_iam_role.instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceRole"
}

resource "aws_iam_role_policy_attachment" "ec2_service_for_ec2_role_policy" {
  role       = aws_iam_role.instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

################################################################################################
# Autoscaling Group and Launch Template
################################################################################################
data "aws_ssm_parameter" "ecs_ami_parameter" {
  name = "/aws/service/ecs/optimized-ami/amazon-linux-2/recommended/image_id"
}



resource "aws_launch_template" "ecs_template" {
  name                   = "${var.name}-ECS-LaunchTemplate"
  image_id               = data.aws_ssm_parameter.ecs_ami_parameter.value
  instance_type          = var.instance_type
  vpc_security_group_ids = [var.ecs_sg]
  key_name               = "ec2-ecs-key"
  user_data = base64encode(templatefile("${path.module}/ecs.sh", {
    PROJECT_NAME_CLUSTER = aws_ecs_cluster.ecs_cluster.name
  }))

  iam_instance_profile {
    arn = aws_iam_instance_profile.ecs_profile.arn
  }

  block_device_mappings {
    device_name = "/dev/xvda"
    ebs {
      volume_size = 30
    }
  }
}

resource "aws_autoscaling_group" "ecs_asg" {
  name                      = "${var.name}-AutoScalingGroup"
  max_size                  = 1
  min_size                  = 1
  desired_capacity          = 1
  health_check_type         = "EC2"
  health_check_grace_period = 300
  vpc_zone_identifier       = var.private_subnets[*].id # Ensure it's a list of subnet IDs

  launch_template {
    id      = aws_launch_template.ecs_template.id
    version = "$Latest"
  }

  tag {
    key                 = "AmazonECSManaged"
    value               = true
    propagate_at_launch = true
  }

  tag {
    key                 = "Name"
    value               = "${var.name}-EC2-Instance"
    propagate_at_launch = true
  }
  depends_on = [
    aws_ecs_cluster.ecs_cluster
  ]
}

################################################################################################
################################################################################################
################################################################################################
################################################################################################
################################################################################################
# Frontend Resources
################################################################################################

# Frontend Log Group
resource "aws_cloudwatch_log_group" "frontend_log_group" {
  name              = "/ecs/${var.name}-frontend-log-group"
  retention_in_days = 30
}

# Frontend Task Definition
resource "aws_ecs_task_definition" "frontend_task_definition" {
  family             = "${var.name}-frontend-task"
  network_mode       = "awsvpc"
  cpu                = 3072
  memory             = 5120
  execution_role_arn = aws_iam_role.ecs_iam_role.arn
  task_role_arn      = aws_iam_role.ecs_task_role.arn

  runtime_platform {
    operating_system_family = var.operating_system_family
    cpu_architecture        = var.cpu_architecture
  }

  container_definitions = <<DEFINITION
  [
    {
      "name": "frontend",
      "image": "${var.frontend_image}",
      "cpu": 0,
      "portMappings": [
        {
          "name": "frontend-port",
          "containerPort": 9000,
          "hostPort": 9000,
          "protocol": "tcp",
          "appProtocol": "http"
        }
      ],
      "ulimits": [],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-create-group": "true",
          "awslogs-group": "/ecs/${var.name}-frontend-log-group",
          "awslogs-region": "${var.region}",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "essential": true
    }
  ]
  DEFINITION
}

# Frontend Service (with ALB)
resource "aws_ecs_service" "frontend_service" {
  name                               = "${var.name}-frontend-service"
  cluster                            = aws_ecs_cluster.ecs_cluster.id
  task_definition                    = aws_ecs_task_definition.frontend_task_definition.arn
  desired_count                      = var.desiredcount
  deployment_minimum_healthy_percent = 0
  deployment_maximum_percent         = 100
  enable_ecs_managed_tags            = true



  capacity_provider_strategy {
    capacity_provider = aws_ecs_capacity_provider.ecs_capacity_provider.name
    weight            = 100
  }

  network_configuration {
    subnets          = var.private_subnets[*].id
    security_groups  = [var.ecs_sg]
    assign_public_ip = false
  }

  deployment_circuit_breaker {
    enable   = true
    rollback = false
  }

  load_balancer {
    container_name   = "frontend"
    target_group_arn = var.alb_target_group
    container_port   = 9000
  }

  depends_on = [aws_autoscaling_group.ecs_asg]
}

################################################################################################
# Backend Resources
################################################################################################

# Backend Log Group
resource "aws_cloudwatch_log_group" "backend_log_group" {
  name              = "/ecs/${var.name}-backend-log-group"
  retention_in_days = 30
}

# Backend Task Definition
resource "aws_ecs_task_definition" "backend_task_definition" {
  family             = "${var.name}-backend-task"
  network_mode       = "awsvpc"
  cpu                = 4096
  memory             = 10240
  execution_role_arn = aws_iam_role.ecs_iam_role.arn
  task_role_arn      = aws_iam_role.ecs_task_role.arn


  runtime_platform {
    operating_system_family = var.operating_system_family
    cpu_architecture        = var.cpu_architecture
  }

  container_definitions = <<DEFINITION
  [
    {
      "name": "backend",
      "image": "${var.backend_image}",
      "cpu": 0,
      "privileged": true,
      "portMappings": [
        {
          "name": "backend-port",
          "containerPort": 5000,
          "hostPort": 5000,
          "protocol": "tcp"
        }
      ],
      "ulimits": [],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-create-group": "true",
          "awslogs-group": "/ecs/${var.name}-backend-log-group",
          "awslogs-region": "${var.region}",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "essential": true
    }
  ]
  DEFINITION
}

# Backend Service (internal communication)
resource "aws_ecs_service" "backend_service" {
  name            = "${var.name}-backend-service"
  cluster         = aws_ecs_cluster.ecs_cluster.id
  task_definition = aws_ecs_task_definition.backend_task_definition.arn
  desired_count   = var.desiredcount

  deployment_circuit_breaker {
    enable   = true
    rollback = false
  }
  capacity_provider_strategy {
    capacity_provider = aws_ecs_capacity_provider.ecs_capacity_provider.name
    weight            = 100
  }

  network_configuration {
    subnets          = var.private_subnets[*].id
    security_groups  = [var.ecs_sg]
    assign_public_ip = false
  }

  load_balancer {
    container_name   = "backend"
    target_group_arn = var.alb_target_group_backend
    container_port   = 5000
  }

  depends_on = [aws_autoscaling_group.ecs_asg]
}
