resource "aws_vpc" "vpc" {
  cidr_block = var.cidr_block

  enable_dns_support = true

  enable_dns_hostnames = true

  tags = merge(
    { Name = "${var.project_name}-vpc" }
  )
}


resource "aws_internet_gateway" "igw" {
  vpc_id = aws_vpc.vpc.id
  tags = merge(
    { Name = "${var.project_name}-vpc-igw" }
  )
}

resource "aws_subnet" "vpc_public_subnets" {
  count                   = var.subnet_count
  vpc_id                  = aws_vpc.vpc.id
  cidr_block              = element(var.public_subnet_cidrs, count.index)
  availability_zone       = "${var.region}${element(var.azs, count.index)}"
  map_public_ip_on_launch = true

  tags = {
    Name = "${var.project_name}-public-subnet-${count.index + 1}"
  }
}

resource "aws_subnet" "vpc_private_subnets" {
  count             = var.subnet_count
  vpc_id            = aws_vpc.vpc.id
  cidr_block        = element(var.private_subnet_cidrs, count.index)
  availability_zone = "${var.region}${element(var.azs, count.index)}"

  tags = {
    Name = "${var.project_name}-private-subnet-${count.index + 1}"
  }
}

resource "aws_eip" "nat_eips" {
  count = var.subnet_count
  depends_on = [
    aws_internet_gateway.igw
  ]
  tags = merge(
    { Name = "${var.project_name} nat gateway ${count.index + 1}" }
  )
}

resource "aws_nat_gateway" "nat_gateways" {
  count         = var.subnet_count
  allocation_id = aws_eip.nat_eips[count.index].id
  subnet_id     = aws_subnet.vpc_public_subnets[count.index].id

  tags = merge(
    { Name = "${var.project_name} gateway - ${count.index + 1}" }
  )
}

resource "aws_route_table" "public_route_table" {
  vpc_id = aws_vpc.vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }

  tags = merge(
    { Name = "${var.project_name} public route table" }
  )
}

resource "aws_route_table" "private_route_tables" {
  count  = var.subnet_count
  vpc_id = aws_vpc.vpc.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.nat_gateways[count.index].id
  }

  tags = merge(
    { Name = "${var.project_name} private route table ${count.index + 1}" }
  )
}

resource "aws_route_table_association" "public_association" {
  count     = var.subnet_count
  subnet_id = aws_subnet.vpc_public_subnets[count.index].id

  route_table_id = aws_route_table.public_route_table.id
}

resource "aws_route_table_association" "private_association" {
  count     = var.subnet_count
  subnet_id = aws_subnet.vpc_private_subnets[count.index].id

  route_table_id = aws_route_table.private_route_tables[count.index].id
}

resource "aws_security_group" "alb_frontend_sg" {
  name        = "alb-frontend-sg"
  description = "ALB Security Group"
  vpc_id      = aws_vpc.vpc.id

  ingress {
    description     = ""
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = []
    cidr_blocks     = ["0.0.0.0/0"]
  }

  ingress {
    description     = ""
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = []
    cidr_blocks     = ["0.0.0.0/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}

resource "aws_security_group" "alb_mapping_sg" {
  name        = "alb-mapping-sg"
  description = "ALB Security Group"
  vpc_id      = aws_vpc.vpc.id

  ingress {
    description     = ""
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = []
    cidr_blocks     = ["0.0.0.0/0"]
  }

  ingress {
    description     = ""
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = []
    cidr_blocks     = ["0.0.0.0/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}


resource "aws_security_group" "alb_backend_sg" {
  name        = "alb-backend-sg"
  description = "ALB Security Group"
  vpc_id      = aws_vpc.vpc.id

  ingress {
    description     = ""
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = []
    cidr_blocks     = ["0.0.0.0/0"]
  }

  ingress {
    description     = ""
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = []
    cidr_blocks     = ["0.0.0.0/0"]
  }

  ingress {
    description     = ""
    from_port       = 5000
    to_port         = 5000
    protocol        = "tcp"
    security_groups = []
    cidr_blocks     = ["0.0.0.0/0"]
  }

  ingress {
    description     = ""
    from_port       = 8080
    to_port         = 8080
    protocol        = "tcp"
    security_groups = []
    cidr_blocks     = ["0.0.0.0/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}





resource "aws_security_group" "ecs_frontend_sg" {
  name        = "ecs-frontend-sg"
  description = "ECS Security Group"
  vpc_id      = aws_vpc.vpc.id

  ingress {
    description     = ""
    from_port       = 9000
    to_port         = 9000
    protocol        = "tcp"
    security_groups = [aws_security_group.alb_frontend_sg.id]

  }

  ingress {
    description     = "bastion host security group"
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = [aws_security_group.jumpbox-sg.id]

  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}








resource "aws_security_group" "ecs_backend_sg" {
  name        = "ecs_backend_sg"
  description = "ECS Security Group"
  vpc_id      = aws_vpc.vpc.id

  ingress {
    description     = ""
    from_port       = 5000
    to_port         = 5000
    protocol        = "tcp"
    security_groups = [aws_security_group.alb_backend_sg.id]

  }
  ingress {
    description     = "Geoserver security group"
    from_port       = 8080
    to_port         = 8080
    protocol        = "tcp"
    security_groups = ["sg-063fa32765bfb237c"]

  }



  ingress {
    description     = "bastion host security group"
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = [aws_security_group.jumpbox-sg.id]

  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}




resource "aws_security_group" "mapping_ecs_sg" {
  name        = "ecs-mapping-sg"
  description = "ECS Security Group"
  vpc_id      = aws_vpc.vpc.id

  ingress {
    description     = ""
    from_port       = 8000
    to_port         = 8000
    protocol        = "tcp"
    security_groups = [aws_security_group.alb_mapping_sg.id]

  }

  ingress {
    description     = "bastion host security group"
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = [aws_security_group.jumpbox-sg.id]

  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}








resource "aws_security_group" "jumpbox-sg" {
  name        = "bastion-host-sg"
  description = "Bastion Host Security Group"
  vpc_id      = aws_vpc.vpc.id

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}


