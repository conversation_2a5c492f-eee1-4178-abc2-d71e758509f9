output "vpc_id" {
  value = aws_vpc.vpc.id
}

output "vpc_cidr_block" {
  value = aws_vpc.vpc.cidr_block
}

output "public_subnets" {
  value = aws_subnet.vpc_public_subnets
}

output "private_subnets" {
  value = aws_subnet.vpc_private_subnets
}

# output "db_sg" {
#   value = aws_security_group.db_sg.id
# } 
output "alb_backend_sg" {
  value = aws_security_group.alb_backend_sg.id
}
output "alb_frontend_sg" {
  value = aws_security_group.alb_frontend_sg.id
}

output "ecs_backend_sg" {
  value = aws_security_group.ecs_backend_sg.id
}

output "ecs_frontend_sg" {
  value = aws_security_group.ecs_frontend_sg.id
}


output "mapping_ecs_sg" {
  value = aws_security_group.mapping_ecs_sg.id
}

output "alb_mapping_sg" {
  value = aws_security_group.alb_mapping_sg.id
}


output "ec2_bastion_sg" {
  value = aws_security_group.jumpbox-sg.id
}
