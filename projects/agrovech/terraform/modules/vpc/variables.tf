variable "region" {
}

variable "project_name" {
}

variable "cidr_block" {
}

variable "subnet_count" {
  type    = number
  default = 2
}

variable "public_subnet_cidrs" {
  type        = list(string)
  description = "Public Subnet CIDR values"
  default     = ["********/24", "********/24"]
}

variable "private_subnet_cidrs" {
  type        = list(string)
  description = "Private Subnet CIDR values"
  default     = ["********/24", "********/24"]
}

variable "azs" {
  type        = list(string)
  description = "Availability Zones"
  default     = ["a", "b"]
}






