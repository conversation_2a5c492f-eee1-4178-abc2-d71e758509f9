
# ALB Resource
resource "aws_lb" "alb" {
  name                       = "${var.name}-alb"
  load_balancer_type         = "application"
  security_groups            = [var.alb_frontend_sg]
  subnets                    = [var.public_subnets[0].id, var.public_subnets[1].id] # Assuming var.public_subnets is a list of subnet IDs
  enable_deletion_protection = false
}

# Target Group for ECS (Port 9000)
resource "aws_lb_target_group" "alb_target_group" {
  name        = "${var.name}-alb-target"
  port        = 9000 # Adjust based on your ECS service
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = var.vpc_id

  health_check {
    enabled             = true
    interval            = 30
    path                = "/" # Adjust to your application's health check path
    port                = "traffic-port"
    timeout             = 5
    healthy_threshold   = 2
    unhealthy_threshold = 2
    matcher             = "200-299"
  }
}

resource "aws_lb_listener" "http_listener" {
  load_balancer_arn = aws_lb.alb.id
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "http_listener_433" {
  load_balancer_arn = aws_lb.alb.id
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = "arn:aws:acm:eu-central-1:058264461647:certificate/e18c6ea0-2e65-4ab3-ac90-1ff40c060ad7"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.alb_target_group.arn
  }

  tags = {
    name = "ssl_listener_fansupport_prod"
  }
}

################################################################################################
# Backend Resources
################################################################################################

# ALB Resource
resource "aws_lb" "backend_alb" {
  name                       = "${var.name}-backend-alb"
  load_balancer_type         = "application"
  security_groups            = [var.alb_backend_sg]
  subnets                    = [var.public_subnets[0].id, var.public_subnets[1].id]
  enable_deletion_protection = false
}

# Target Group for ECS (Port 5000)
resource "aws_lb_target_group" "alb_target_group_backend" {
  name        = "${var.name}-alb-target-backend"
  port        = 5000
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = var.vpc_id

  health_check {
    enabled             = true
    interval            = 30
    path                = "/get-jobs"
    port                = "traffic-port"
    timeout             = 5
    healthy_threshold   = 2
    unhealthy_threshold = 2
    matcher             = "200-299"
  }
}




resource "aws_lb_listener" "http_listener_2a" {
  load_balancer_arn = aws_lb.backend_alb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "https_listener_2" {
  load_balancer_arn = aws_lb.backend_alb.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = "arn:aws:acm:eu-central-1:058264461647:certificate/e18c6ea0-2e65-4ab3-ac90-1ff40c060ad7"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.alb_target_group_backend.arn
  }

  tags = {
    name = "ssl_listener_fansupport_prod"
  }
}



