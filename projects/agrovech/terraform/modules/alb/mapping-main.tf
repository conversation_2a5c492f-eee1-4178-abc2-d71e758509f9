# ################################################################################################
# # Mapping Resources
# ################################################################################################

# # ALB Resource for Mapping
# resource "aws_lb" "mapping_alb" {
#   name                       = "${var.name}-mapping-alb"
#   load_balancer_type         = "application"
#   security_groups            = [var.alb_mapping_sg]
#   subnets                    = [var.public_subnets[0].id, var.public_subnets[1].id]
#   enable_deletion_protection = false
# }

# # Target Group for ECS (Port 7000)
# resource "aws_lb_target_group" "alb_target_group_mapping" {
#   name        = "${var.name}-alb-target-mapping"
#   port        = 8000
#   protocol    = "HTTP"
#   target_type = "ip"
#   vpc_id      = var.vpc_id

#   health_check {
#     enabled             = true
#     interval            = 30
#     path                = "/health"
#     port                = "traffic-port"
#     timeout             = 5
#     healthy_threshold   = 2
#     unhealthy_threshold = 2
#     matcher             = "200-299"
#   }
# }

# resource "aws_lb_listener" "http_listener_mapping" {
#   load_balancer_arn = aws_lb.mapping_alb.id
#   port              = "80"
#   protocol          = "HTTP"

#   default_action {
#     type = "redirect"
#     redirect {
#       port        = "443"
#       protocol    = "HTTPS"
#       status_code = "HTTP_301"
#     }
#   }
# }

# resource "aws_lb_listener" "https_listener_mapping" {
#   load_balancer_arn = aws_lb.mapping_alb.id
#   port              = "443"
#   protocol          = "HTTPS"
#   ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
#   certificate_arn   = "arn:aws:acm:eu-central-1:058264461647:certificate/e18c6ea0-2e65-4ab3-ac90-1ff40c060ad7"

#   default_action {
#     type             = "forward"
#     target_group_arn = aws_lb_target_group.alb_target_group_mapping.arn
#   }

#   tags = {
#     name = "ssl_listener_mapping"
#   }
# }
