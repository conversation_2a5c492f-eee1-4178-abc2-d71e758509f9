################################################################################################
# ECS Cluster
################################################################################################
resource "aws_ecs_cluster" "ecs_cluster" {
  name = "${var.project_name}-ecs-cluster"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }
  # service_connect_defaults {
  #   namespace = var.cloud_map_arn
  # }
}

################################################################################################
# IAM Roles and Policies for ECS
################################################################################################
resource "aws_iam_role" "ecs_task_role" {
  name = "${var.project_name}-ECS-TaskRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect    = "Allow",
      Principal = { Service = "ecs-tasks.amazonaws.com" },
      Action    = "sts:AssumeRole",
    }],
  })
}

resource "aws_iam_role" "ecs_execution_role" {
  name               = "${var.project_name}-ECS-ExecutionRole"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role_policy_attachment" "ecs_execution_role_policy" {
  role       = aws_iam_role.ecs_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

################################################################################################
# Frontend Resources
################################################################################################

# Frontend EC2 Instance Profile and Role for ECS
resource "aws_iam_instance_profile" "frontend_ecs_profile" {
  name = aws_iam_role.frontend_instance_role.name
  role = aws_iam_role.frontend_instance_role.name
}

resource "aws_iam_role" "frontend_instance_role" {
  name = "${var.project_name}-frontend-EC2-InstanceRole"
  path = "/"
  assume_role_policy = jsonencode({
    "Version" : "2008-10-17",
    "Statement" : [
      {
        "Action" : "sts:AssumeRole",
        "Principal" : { "Service" : "ec2.amazonaws.com" },
        "Effect" : "Allow",
        "Sid" : ""
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "frontend_ec2_registry_fullaccess" {
  role       = aws_iam_role.frontend_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryFullAccess"
}

resource "aws_iam_role_policy_attachment" "frontend_ec2_service_role_policy" {
  role       = aws_iam_role.frontend_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceRole"
}

resource "aws_iam_role_policy_attachment" "frontend_ec2_service_for_ec2_role_policy" {
  role       = aws_iam_role.frontend_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

# Frontend Autoscaling Group and Launch Template
data "aws_ssm_parameter" "frontend_ecs_ami_parameter" {
  name = "/aws/service/ecs/optimized-ami/amazon-linux-2/recommended/image_id"
}

resource "aws_launch_template" "frontend_ecs_template" {
  name                   = "${var.project_name}-frontend-ECS-LaunchTemplate"
  image_id               = data.aws_ssm_parameter.frontend_ecs_ami_parameter.value
  instance_type          = var.instance_type_frontend
  vpc_security_group_ids = [var.ecs_frontend_sg]
  key_name               = "ec2-ecs-key"
  user_data = base64encode(templatefile("${path.module}/ecs.sh", {
    PROJECT_NAME_CLUSTER = aws_ecs_cluster.ecs_cluster.name
  }))

  iam_instance_profile {
    arn = aws_iam_instance_profile.frontend_ecs_profile.arn
  }

  block_device_mappings {
    device_name = "/dev/xvda"
    ebs {
      volume_size = 30
    }
  }
}

resource "aws_autoscaling_group" "frontend_ecs_asg" {
  name                      = "${var.project_name}-frontend-AutoScalingGroup"
  max_size                  = 2
  min_size                  = 1
  desired_capacity          = 1
  health_check_type         = "EC2"
  health_check_grace_period = 300
  vpc_zone_identifier       = var.private_subnets[*].id

  launch_template {
    id      = aws_launch_template.frontend_ecs_template.id
    version = "$Latest"
  }

  tag {
    key                 = "AmazonECSManaged"
    value               = true
    propagate_at_launch = true
  }

  tag {
    key                 = "Name"
    value               = "${var.project_name}-frontend-EC2-Instance"
    propagate_at_launch = true
  }
}

# Frontend ECS Capacity Provider
resource "aws_ecs_capacity_provider" "frontend_ecs_capacity_provider" {
  name = "EC2_Frontend_CapacityProvider"

  auto_scaling_group_provider {
    auto_scaling_group_arn = aws_autoscaling_group.frontend_ecs_asg.arn

    managed_scaling {
      status          = "DISABLED"
      target_capacity = 70
    }
  }
}

# Frontend Log Group
resource "aws_cloudwatch_log_group" "frontend_log_group" {
  name              = "/ecs/${var.project_name}-frontend-log-group"
  retention_in_days = 30
}

# Frontend Task Definition
resource "aws_ecs_task_definition" "frontend_task_definition" {
  family             = "${var.project_name}-frontend-task"
  network_mode       = "awsvpc"
  cpu                = 1536
  memory             = 3584
  execution_role_arn = aws_iam_role.ecs_execution_role.arn
  task_role_arn      = aws_iam_role.ecs_task_role.arn

  runtime_platform {
    operating_system_family = var.operating_system_family
    cpu_architecture        = var.cpu_architecture
  }

  container_definitions = <<DEFINITION
  [
    {
      "name": "frontend",
      "image": "${var.frontend_image}",
      "cpu": 0,
      "portMappings": [
        {
          "name": "frontend-port",
          "containerPort": 9000,
          "hostPort": 9000,
          "protocol": "tcp",
          "appProtocol": "http"
        }
      ],
      "ulimits": [],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-create-group": "true",
          "awslogs-group": "/ecs/${var.project_name}-frontend-log-group",
          "awslogs-region": "${var.region}",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "essential": true
    }
  ]
  DEFINITION
}

# Frontend Service (with ALB)
resource "aws_ecs_service" "frontend_service" {
  name                               = "${var.project_name}-frontend-service"
  cluster                            = aws_ecs_cluster.ecs_cluster.id
  task_definition                    = aws_ecs_task_definition.frontend_task_definition.arn
  desired_count                      = 1
  deployment_minimum_healthy_percent = 0
  deployment_maximum_percent         = 100
  enable_ecs_managed_tags            = true

  capacity_provider_strategy {
    capacity_provider = aws_ecs_capacity_provider.frontend_ecs_capacity_provider.name
    weight            = 100
  }

  network_configuration {
    subnets          = var.private_subnets[*].id
    security_groups  = [var.ecs_frontend_sg]
    assign_public_ip = false
  }

  deployment_circuit_breaker {
    enable   = true
    rollback = false
  }

  load_balancer {
    container_name   = "frontend"
    target_group_arn = var.alb_target_group
    container_port   = 9000
  }

  # service_registries {
  #   registry_arn   = var.frontend_service
  #   container_name = "frontend"
  # }

  depends_on = [aws_autoscaling_group.frontend_ecs_asg]
}

################################################################################################
# Backend Resources
################################################################################################

# Backend EC2 Instance Profile and Role for ECS
resource "aws_iam_instance_profile" "backend_ecs_profile" {
  name = aws_iam_role.backend_instance_role.name
  role = aws_iam_role.backend_instance_role.name
}

resource "aws_iam_role" "backend_instance_role" {
  name = "${var.project_name}-backend-EC2-InstanceRole"
  path = "/"
  assume_role_policy = jsonencode({
    "Version" : "2008-10-17",
    "Statement" : [
      {
        "Action" : "sts:AssumeRole",
        "Principal" : { "Service" : "ec2.amazonaws.com" },
        "Effect" : "Allow",
        "Sid" : ""
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "backend_ec2_registry_fullaccess" {
  role       = aws_iam_role.backend_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryFullAccess"
}

resource "aws_iam_role_policy_attachment" "backend_ec2_service_role_policy" {
  role       = aws_iam_role.backend_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceRole"
}

resource "aws_iam_role_policy_attachment" "backend_ec2_service_for_ec2_role_policy" {
  role       = aws_iam_role.backend_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

# Backend Autoscaling Group and Launch Template
data "aws_ssm_parameter" "backend_ecs_ami_parameter" {
  name = "/aws/service/ecs/optimized-ami/amazon-linux-2/recommended/image_id"
}

resource "aws_launch_template" "backend_ecs_template" {
  name                   = "${var.project_name}-backend-ECS-LaunchTemplate"
  image_id               = data.aws_ssm_parameter.backend_ecs_ami_parameter.value
  instance_type          = var.instance_type_backend
  vpc_security_group_ids = [var.ecs_backend_sg]
  key_name               = "ec2-ecs-key"
  user_data = base64encode(templatefile("${path.module}/ecs.sh", {
    PROJECT_NAME_CLUSTER = aws_ecs_cluster.ecs_cluster.name
  }))

  iam_instance_profile {
    arn = aws_iam_instance_profile.backend_ecs_profile.arn
  }

  block_device_mappings {
    device_name = "/dev/xvda"
    ebs {
      volume_size = 500
    }
  }
}

resource "aws_autoscaling_group" "backend_ecs_asg" {
  name                      = "${var.project_name}-backend-AutoScalingGroup"
  max_size                  = 2
  min_size                  = 1
  desired_capacity          = 1
  health_check_type         = "EC2"
  health_check_grace_period = 300
  vpc_zone_identifier       = var.private_subnets[*].id

  launch_template {
    id      = aws_launch_template.backend_ecs_template.id
    version = "$Latest"
  }

  tag {
    key                 = "AmazonECSManaged"
    value               = true
    propagate_at_launch = true
  }

  tag {
    key                 = "Name"
    value               = "${var.project_name}-backend-EC2-Instance"
    propagate_at_launch = true
  }
}

# Backend ECS Capacity Provider
resource "aws_ecs_capacity_provider" "backend_ecs_capacity_provider" {
  name = "EC2_Backend_CapacityProvider"

  auto_scaling_group_provider {
    auto_scaling_group_arn = aws_autoscaling_group.backend_ecs_asg.arn

    managed_scaling {
      status          = "DISABLED"
      target_capacity = 70
    }
  }
}

# Combine Frontend and Backend Capacity Providers
resource "aws_ecs_cluster_capacity_providers" "combined_capacity_providers" {
  cluster_name = aws_ecs_cluster.ecs_cluster.name

  capacity_providers = [
    aws_ecs_capacity_provider.frontend_ecs_capacity_provider.name,
    aws_ecs_capacity_provider.backend_ecs_capacity_provider.name
  ]

  default_capacity_provider_strategy {
    base              = 0
    weight            = 1
    capacity_provider = aws_ecs_capacity_provider.frontend_ecs_capacity_provider.name
  }

  default_capacity_provider_strategy {
    base              = 0
    weight            = 1
    capacity_provider = aws_ecs_capacity_provider.backend_ecs_capacity_provider.name
  }
}

# Backend Log Group
resource "aws_cloudwatch_log_group" "backend_log_group" {
  name              = "/ecs/${var.project_name}-backend-log-group"
  retention_in_days = 30
}

# Backend Task Definition
resource "aws_ecs_task_definition" "backend_task_definition" {
  family             = "${var.project_name}-backend-task"
  network_mode       = "awsvpc"
  cpu                = 3584
  memory             = 15800
  execution_role_arn = aws_iam_role.ecs_execution_role.arn
  task_role_arn      = aws_iam_role.ecs_task_role.arn

  runtime_platform {
    operating_system_family = var.operating_system_family
    cpu_architecture        = var.cpu_architecture
  }

  container_definitions = <<DEFINITION
  [
    {
      "name": "backend",
      "image": "${var.backend_image}",
      "cpu": 0,
      "portMappings": [
        {
          "name": "backend-port",
          "containerPort": 5000,
          "hostPort": 5000,
          "protocol": "tcp",
          "appProtocol": "http"
        }
      ],
      "essential": true,
      "environment": [
        {
          "name": "PYTHONUNBUFFERED",
          "value": "1"
        }
      ],
      "mountPoints": [
        {
          "sourceVolume": "myvolume",
          "containerPath": "/app/app/static",
          "readOnly": false
        }
      ],
      "volumesFrom": [],
      "privileged": true,
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/${var.project_name}-backend-log-group",
          "awslogs-create-group": "true",
          "awslogs-region": "${var.region}",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "systemControls": []
    }
  ]
  DEFINITION

  volume {
    name = "myvolume"

    host_path = "/mnt/myvolume"
  }
}



# Backend Service
resource "aws_ecs_service" "backend_service" {
  name            = "${var.project_name}-backend-service"
  cluster         = aws_ecs_cluster.ecs_cluster.id
  task_definition = aws_ecs_task_definition.backend_task_definition.arn
  # task_definition                    = "arn:aws:ecs:eu-central-1:058264461647:task-definition/agrovech-backend-task:40"
  desired_count                      = 1
  deployment_minimum_healthy_percent = 0
  deployment_maximum_percent         = 100
  enable_ecs_managed_tags            = true

  capacity_provider_strategy {
    capacity_provider = aws_ecs_capacity_provider.backend_ecs_capacity_provider.name
    weight            = 100
  }

  network_configuration {
    subnets          = var.private_subnets[*].id
    security_groups  = [var.ecs_backend_sg]
    assign_public_ip = false
  }

  deployment_circuit_breaker {
    enable   = true
    rollback = false
  }

  # service_registries {
  #   registry_arn   = var.backend_service
  #   container_name = "backend"
  # }

  load_balancer {
    container_name   = "backend"
    target_group_arn = var.alb_target_group_backend
    container_port   = 5000
  }


  depends_on = [aws_autoscaling_group.backend_ecs_asg]
}
