################################################################################################
# Variables
################################################################################################

variable "region" {
}

variable "project_name" {
}

variable "private_subnets" {
}

variable "public_subnets" {
}

variable "vpc_id" {
}

variable "alb_target_group" {
}

variable "ecs_backend_sg" {
}

variable "ecs_frontend_sg" {
}

variable "mapping_ecs_sg" {
}

variable "alb_target_group_backend" {
}

# variable "alb_target_group_mapping" {
# }

# variable "name" {
#   type    = string
#   default = "agrovech"
# }


variable "instance_type_mapping" {
  type        = string
  description = "EC2 instance type for launch template"
  default     = "t3.medium"
}

variable "instance_type_frontend" {
  type        = string
  description = "EC2 instance type for launch template"
  default     = "t3.medium"
}

variable "instance_type_backend" {
  type        = string
  description = "EC2 instance type for launch template"
  default     = "t3.xlarge"
}

variable "desiredcount" {
  default = 1
}

variable "operating_system_family" {
  type    = string
  default = "LINUX"
}

variable "cpu_architecture" {
  type    = string
  default = "X86_64"
}


variable "frontend_image" {
  type    = string
  default = "058264461647.dkr.ecr.eu-central-1.amazonaws.com/agrovech-front:latest"
}

variable "backend_image" {
  type    = string
  default = "058264461647.dkr.ecr.eu-central-1.amazonaws.com/agrovech-backend:latest"
}

variable "mapping_image" {
  type    = string
  default = "058264461647.dkr.ecr.eu-central-1.amazonaws.com/test-agro:latest"
}

