# ################################################################################################
# # Mapping ECS Cluster
# ################################################################################################

# resource "aws_ecs_cluster" "mapping_cluster" {
#   name = "${var.project_name}-mapping-ecs-cluster"

#   setting {
#     name  = "containerInsights"
#     value = "enabled"
#   }
# }

# ################################################################################################
# # Mapping EC2 Instance Profile and Role for ECS
# ################################################################################################
# resource "aws_iam_instance_profile" "mapping_ecs_profile" {
#   name = aws_iam_role.mapping_instance_role.name
#   role = aws_iam_role.mapping_instance_role.name
# }

# resource "aws_iam_role" "mapping_instance_role" {
#   name = "${var.project_name}-mapping-EC2-InstanceRole"
#   path = "/"
#   assume_role_policy = jsonencode({
#     "Version" : "2008-10-17",
#     "Statement" : [
#       {
#         "Action" : "sts:AssumeRole",
#         "Principal" : { "Service" : "ec2.amazonaws.com" },
#         "Effect" : "Allow",
#         "Sid" : ""
#       }
#     ]
#   })
# }

# resource "aws_iam_role_policy_attachment" "mapping_ec2_registry_fullaccess" {
#   role       = aws_iam_role.mapping_instance_role.name
#   policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryFullAccess"
# }

# resource "aws_iam_role_policy_attachment" "mapping_ec2_service_role_policy" {
#   role       = aws_iam_role.mapping_instance_role.name
#   policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceRole"
# }

# resource "aws_iam_role_policy_attachment" "mapping_ec2_service_for_ec2_role_policy" {
#   role       = aws_iam_role.mapping_instance_role.name
#   policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
# }

# ################################################################################################
# # Mapping Autoscaling Group and Launch Template
# ################################################################################################
# data "aws_ssm_parameter" "mapping_ecs_ami_parameter" {
#   name = "/aws/service/ecs/optimized-ami/amazon-linux-2/recommended/image_id"
# }

# resource "aws_launch_template" "mapping_ecs_template" {
#   name                   = "${var.project_name}-mapping-ECS-LaunchTemplate"
#   image_id               = data.aws_ssm_parameter.mapping_ecs_ami_parameter.value
#   instance_type          = var.instance_type_mapping
#   vpc_security_group_ids = [var.mapping_ecs_sg]
#   key_name               = "ec2-ecs-key"
#   user_data = base64encode(templatefile("${path.module}/ecs.sh", {
#     PROJECT_NAME_CLUSTER = aws_ecs_cluster.mapping_cluster.name
#   }))

#   iam_instance_profile {
#     arn = aws_iam_instance_profile.mapping_ecs_profile.arn
#   }

#   block_device_mappings {
#     device_name = "/dev/xvda"
#     ebs {
#       volume_size = 30
#     }
#   }
# }

# resource "aws_autoscaling_group" "mapping_ecs_asg" {
#   name                      = "${var.project_name}-mapping-AutoScalingGroup"
#   max_size                  = 1
#   min_size                  = 1
#   desired_capacity          = 1
#   health_check_type         = "EC2"
#   health_check_grace_period = 300
#   vpc_zone_identifier       = var.private_subnets[*].id

#   launch_template {
#     id      = aws_launch_template.mapping_ecs_template.id
#     version = "$Latest"
#   }

#   tag {
#     key                 = "AmazonECSManaged"
#     value               = true
#     propagate_at_launch = true
#   }

#   tag {
#     key                 = "Name"
#     value               = "${var.project_name}-mapping-EC2-Instance"
#     propagate_at_launch = true
#   }
# }

# ################################################################################################
# # Mapping ECS Capacity Provider
# ################################################################################################
# resource "aws_ecs_capacity_provider" "mapping_ecs_capacity_provider" {
#   name = "EC2_Mapping_CapacityProvider"

#   auto_scaling_group_provider {
#     auto_scaling_group_arn = aws_autoscaling_group.mapping_ecs_asg.arn

#     managed_scaling {
#       maximum_scaling_step_size = 1000
#       minimum_scaling_step_size = 1
#       status                    = "ENABLED"
#       target_capacity           = 1
#     }
#   }
# }

# resource "aws_ecs_cluster_capacity_providers" "mapping_capacity_providers" {
#   cluster_name = aws_ecs_cluster.mapping_cluster.name

#   capacity_providers = [
#     aws_ecs_capacity_provider.mapping_ecs_capacity_provider.name
#   ]

#   default_capacity_provider_strategy {
#     base              = 0
#     weight            = 1
#     capacity_provider = aws_ecs_capacity_provider.mapping_ecs_capacity_provider.name
#   }
# }

# ################################################################################################
# # Mapping Log Group
# ################################################################################################
# resource "aws_cloudwatch_log_group" "mapping_log_group" {
#   name              = "/ecs/${var.project_name}-mapping-log-group"
#   retention_in_days = 30
# }

# ################################################################################################
# # Mapping Task Definition
# ################################################################################################
# resource "aws_ecs_task_definition" "mapping_task_definition" {
#   family             = "${var.project_name}-mapping-task"
#   network_mode       = "awsvpc"
#   cpu                = 7680
#   memory             = 15872
#   execution_role_arn = aws_iam_role.ecs_execution_role.arn
#   task_role_arn      = aws_iam_role.ecs_task_role.arn

#   runtime_platform {
#     operating_system_family = var.operating_system_family
#     cpu_architecture        = var.cpu_architecture
#   }

#   container_definitions = <<DEFINITION
#   [
#     {
#       "name": "mapping",
#       "image": "${var.mapping_image}",
#       "cpu": 0,
#       "portMappings": [
#         {
#           "name": "mapping-port",
#           "containerPort": 8000,
#           "hostPort": 8000,
#           "protocol": "tcp",
#           "appProtocol": "http"
#         }
#       ],
#       "ulimits": [],
#       "logConfiguration": {
#         "logDriver": "awslogs",
#         "options": {
#           "awslogs-create-group": "true",
#           "awslogs-group": "/ecs/${var.project_name}-mapping-log-group",
#           "awslogs-region": "${var.region}",
#           "awslogs-stream-prefix": "ecs"
#         }
#       },
#       "essential": true
#     }
#   ]
#   DEFINITION
# }

# ################################################################################################
# # Mapping Service
# ################################################################################################
# resource "aws_ecs_service" "mapping_service" {
#   name                               = "${var.project_name}-mapping-service"
#   cluster                            = aws_ecs_cluster.mapping_cluster.id
#   task_definition                    = aws_ecs_task_definition.mapping_task_definition.arn
#   desired_count                      = 1
#   deployment_minimum_healthy_percent = 0
#   deployment_maximum_percent         = 100
#   enable_ecs_managed_tags            = true

#   capacity_provider_strategy {
#     capacity_provider = aws_ecs_capacity_provider.mapping_ecs_capacity_provider.name
#     weight            = 100
#   }

#   network_configuration {
#     subnets          = var.private_subnets[*].id
#     security_groups  = [var.mapping_ecs_sg]
#     assign_public_ip = false
#   }

#   deployment_circuit_breaker {
#     enable   = true
#     rollback = false
#   }

#   load_balancer {
#     container_name   = "backend"
#     target_group_arn = var.alb_target_group_mapping
#     container_port   = 8000
#   }

#   depends_on = [aws_autoscaling_group.mapping_ecs_asg]
# }
