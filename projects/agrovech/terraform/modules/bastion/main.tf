resource "aws_instance" "bastion-host" {
  ami                         = "ami-01be94ae58414ab2e"
  instance_type               = "t3.small"
  key_name                    = "ec2-ecs-key"
  subnet_id                   = var.public_subnets[0].id
  vpc_security_group_ids      = [var.ec2_bastion_sg]
  associate_public_ip_address = true
  root_block_device {
    volume_size = 10
    volume_type = "gp3"
  }

  tags = {
    Name = "bastion-host"
  }
}
