resource "aws_service_discovery_private_dns_namespace" "agrovech_http_ns" {
  name        = "${var.project_name}.com"
  description = "${var.project_name} HTTP Namespace"
  vpc         = var.vpc_id
}


resource "aws_service_discovery_service" "backendservice" {
  name = "backend"

  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.agrovech_http_ns.id

    dns_records {
      ttl  = 10
      type = "A"
    }

    routing_policy = "MULTIVALUE"
  }

  health_check_custom_config {
    failure_threshold = 1
  }
}






