module "alb" {
  source          = "./modules/alb"
  vpc_id          = module.vpc.vpc_id
  public_subnets  = module.vpc.public_subnets
  alb_frontend_sg = module.vpc.alb_frontend_sg
  alb_backend_sg  = module.vpc.alb_backend_sg
  alb_mapping_sg  = module.vpc.alb_mapping_sg
}






# module "cloud-map" {
#   source = "./modules/cloud-map"

#   project_name = var.project_name
#   vpc_id       = module.vpc.vpc_id
# }






module "ecs" {
  source                   = "./modules/ecs"
  project_name             = var.project_name
  region                   = var.region
  vpc_id                   = module.vpc.vpc_id
  private_subnets          = module.vpc.private_subnets
  public_subnets           = module.vpc.public_subnets
  ecs_backend_sg           = module.vpc.ecs_backend_sg
  ecs_frontend_sg          = module.vpc.ecs_frontend_sg
  mapping_ecs_sg           = module.vpc.mapping_ecs_sg
  alb_target_group         = module.alb.alb_target_group
  alb_target_group_backend = module.alb.alb_target_group_backend
}
# alb_target_group_mapping = module.alb.alb_target_group_mapping



# cloud_map_arn            = module.cloud-map.cloud_map_arn
# backend_service          = module.cloud-map.backend_service


module "vpc" {
  source       = "./modules/vpc"
  region       = var.region
  cidr_block   = var.cidr_block
  project_name = var.project_name
}


module "bastion" {
  source         = "./modules/bastion"
  vpc_id         = module.vpc.vpc_id
  public_subnets = module.vpc.public_subnets
  ec2_bastion_sg = module.vpc.ec2_bastion_sg
}
